import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import { getDocumentRoute } from '../../../utils/progressUtils';

const DocumentCard = ({ document, onEdit, onDuplicate, onShare, onDelete, compact = false }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const navigate = useNavigate();

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-success text-success-foreground';
      case 'draft':
        return 'bg-warning text-warning-foreground';
      case 'shared':
        return 'bg-accent text-accent-foreground';
      default:
        return 'bg-text-secondary text-white';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'ebook':
        return 'Book';
      case 'academic':
        return 'GraduationCap';
      case 'business':
        return 'Briefcase';
      default:
        return 'FileText';
    }
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const handleCardClick = () => {
    const route = getDocumentRoute(document);

    // Log for debugging
    console.log(`Navigating to document: ${document.title}`, {
      documentId: document.id,
      status: document.status,
      progress: document.progress,
      route: route
    });

    // For document creator route, pass documentId in state for editing existing documents
    if (route === '/document-creator') {
      navigate(route, { state: { documentId: document.id } });
    } else {
      navigate(route);
    }
  };

  // Compact version for dashboard sidebar
  if (compact) {
    return (
      <div
        className="bg-white rounded-md border border-border p-4 shadow-soft hover:shadow-card transition-all duration-300 cursor-pointer group"
        onClick={handleCardClick}
      >
        <div className="flex items-start space-x-3">
          <div className={`w-10 h-10 rounded-md flex items-center justify-center ${
            document.status === 'completed' ? 'bg-success-light' :
            document.status === 'draft' ? 'bg-warning-light' :
            'bg-primary-light'
          }`}>
            <Icon
              name={getTypeIcon(document.type)}
              size="sm"
              variant={
                document.status === 'completed' ? 'success' :
                document.status === 'draft' ? 'warning' :
                'primary'
              }
            />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-text-primary group-hover:text-primary transition-colors duration-300 truncate">
              {document.title}
            </h4>
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-text-secondary capitalize">{document.type}</span>
              <span className={`text-xs px-2 py-0.5 rounded-full ${
                document.status === 'completed' ? 'bg-success-light text-success' :
                document.status === 'draft' ? 'bg-warning-light text-warning' :
                'bg-primary-light text-primary'
              }`}>
                {document.status}
              </span>
            </div>
            {document.progress < 100 && (
              <div className="mt-2">
                <div className="w-full bg-surface-secondary rounded-full h-1">
                  <div
                    className="bg-primary h-1 rounded-full transition-all duration-300"
                    style={{ width: `${document.progress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Full card version
  return (
    <div
      className="bg-white rounded-lg border border-border shadow-card hover:shadow-elevated transition-all duration-300 cursor-pointer relative group transform hover:-translate-y-1"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleCardClick}
    >
      {/* Document Thumbnail */}
      <div className="relative h-48 overflow-hidden rounded-t-md">
        <Image
          src={document.thumbnail}
          alt={document.title}
          className="w-full h-full object-cover"
        />
        
        {/* Status Badge */}
        <div className={`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
          {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
        </div>

        {/* Type Icon */}
        <div className="absolute top-3 right-3 p-2 bg-surface/90 rounded-lg">
          <Icon name={getTypeIcon(document.type)} size={16} />
        </div>

        {/* Progress Bar for Drafts */}
        {document.status === 'draft' && document.progress && (
          <div className="absolute bottom-0 left-0 right-0 bg-surface/90 p-2">
            <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
              <span>Progress</span>
              <span>{document.progress}%</span>
            </div>
            <div className="w-full bg-border rounded-full h-1.5">
              <div
                className="bg-primary h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${document.progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Hover Actions */}
        {isHovered && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(document);
                }}
                className="bg-surface/90 hover:bg-surface p-2"
              >
                <Icon name="Edit" size={16} />
              </Button>
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onDuplicate(document);
                }}
                className="bg-surface/90 hover:bg-surface p-2"
              >
                <Icon name="Copy" size={16} />
              </Button>
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onShare(document);
                }}
                className="bg-surface/90 hover:bg-surface p-2"
              >
                <Icon name="Share2" size={16} />
              </Button>
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowActions(true);
                }}
                className="bg-surface/90 hover:bg-surface p-2"
              >
                <Icon name="MoreVertical" size={16} />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Document Info */}
      <div className="p-4">
        <h3 className="font-semibold text-text-primary mb-2 line-clamp-2">{document.title}</h3>
        <div className="flex items-center justify-between text-sm text-text-secondary">
          <span>{formatDate(document.createdAt)}</span>
          <div className="flex items-center space-x-1">
            <Icon name="FileText" size={14} />
            <span>{document.pageCount} pages</span>
          </div>
        </div>
        

      </div>

      {/* Actions Menu */}
      {showActions && (
        <div className="absolute top-2 right-2 bg-surface rounded-lg shadow-elevation-3 border border-border z-10 py-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete(document);
              setShowActions(false);
            }}
            className="w-full text-left px-3 py-2 text-sm text-error hover:bg-background flex items-center space-x-2"
          >
            <Icon name="Trash2" size={14} />
            <span>Delete</span>
          </button>
        </div>
      )}

      {/* Overlay to close actions menu */}
      {showActions && (
        <div
          className="fixed inset-0 z-5"
          onClick={(e) => {
            e.stopPropagation();
            setShowActions(false);
          }}
        />
      )}
    </div>
  );
};

export default DocumentCard;