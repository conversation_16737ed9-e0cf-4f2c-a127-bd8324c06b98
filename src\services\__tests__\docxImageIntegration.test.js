/**
 * Integration tests for DOCX export with embedded images
 * Tests the complete flow from content processing to DOCX generation
 */

import { exportAsDocx } from '../exportService.js';

// Mock the DOCX generation service
jest.mock('../docxGenerationService.js', () => ({
    generateAndDownloadDocxWithImages: jest.fn()
}));

describe('DOCX Export Integration - Image Embedding', () => {
    let mockGenerateAndDownloadDocxWithImages;

    beforeEach(() => {
        jest.clearAllMocks();
        mockGenerateAndDownloadDocxWithImages = require('../docxGenerationService.js').generateAndDownloadDocxWithImages;
    });

    describe('exportAsDocx with TipTap editor', () => {
        test('should export DOCX with HTML content from TipTap editor', async () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author',
                description: 'Test Description'
            };

            const generatedContent = {
                chapters: [
                    {
                        number: 1,
                        title: 'Chapter 1',
                        content: 'Chapter content'
                    }
                ]
            };

            const mockEditor = {
                getHTML: jest.fn().mockReturnValue('<p>Test content</p><img src="test.jpg" alt="Test image" />')
            };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({
                success: true,
                imageStats: {
                    totalImages: 1,
                    successfulImages: 1,
                    failedImages: 0
                }
            });

            const result = await exportAsDocx(documentData, generatedContent, mockEditor);

            expect(result.success).toBe(true);
            expect(result.message).toContain('DOCX export completed successfully with embedded images');
            expect(result.message).toContain('Images: 1/1 embedded successfully');

            expect(mockGenerateAndDownloadDocxWithImages).toHaveBeenCalledWith(
                documentData,
                '<p>Test content</p><img src="test.jpg" alt="Test image" />',
                'html'
            );
        });

        test('should handle mixed success/failure image processing', async () => {
            const documentData = { title: 'Test Document' };
            const generatedContent = { chapters: [] };
            const mockEditor = {
                getHTML: jest.fn().mockReturnValue('<img src="success.jpg" /><img src="failed.jpg" />')
            };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({
                success: true,
                imageStats: {
                    totalImages: 2,
                    successfulImages: 1,
                    failedImages: 1
                }
            });

            const result = await exportAsDocx(documentData, generatedContent, mockEditor);

            expect(result.success).toBe(true);
            expect(result.message).toContain('Images: 1/2 embedded successfully, 1 failed to download');
        });

        test('should handle no images in content', async () => {
            const documentData = { title: 'Test Document' };
            const generatedContent = { chapters: [] };
            const mockEditor = {
                getHTML: jest.fn().mockReturnValue('<p>Just text content</p>')
            };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({
                success: true,
                imageStats: {
                    totalImages: 0,
                    successfulImages: 0,
                    failedImages: 0
                }
            });

            const result = await exportAsDocx(documentData, generatedContent, mockEditor);

            expect(result.success).toBe(true);
            expect(result.message).toBe('DOCX export completed successfully with embedded images');
        });
    });

    describe('exportAsDocx without editor (markdown fallback)', () => {
        test('should export DOCX with markdown content from chapters', async () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author'
            };

            const generatedContent = {
                chapters: [
                    {
                        number: 1,
                        title: 'First Chapter',
                        content: 'This is chapter content with ![image](test.jpg) embedded.'
                    },
                    {
                        number: 2,
                        title: 'Second Chapter',
                        content: 'More content here.'
                    }
                ]
            };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({
                success: true,
                imageStats: {
                    totalImages: 1,
                    successfulImages: 1,
                    failedImages: 0
                }
            });

            const result = await exportAsDocx(documentData, generatedContent);

            expect(result.success).toBe(true);

            const expectedContent = `# Chapter 1: First Chapter

This is chapter content with ![image](test.jpg) embedded.

---

# Chapter 2: Second Chapter

More content here.`;

            expect(mockGenerateAndDownloadDocxWithImages).toHaveBeenCalledWith(
                documentData,
                expectedContent,
                'markdown'
            );
        });

        test('should handle empty chapters', async () => {
            const documentData = { title: 'Empty Document' };
            const generatedContent = { chapters: [] };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({
                success: true,
                imageStats: {
                    totalImages: 0,
                    successfulImages: 0,
                    failedImages: 0
                }
            });

            const result = await exportAsDocx(documentData, generatedContent);

            expect(result.success).toBe(true);
            expect(mockGenerateAndDownloadDocxWithImages).toHaveBeenCalledWith(
                documentData,
                '',
                'markdown'
            );
        });
    });

    describe('error handling', () => {
        test('should handle DOCX generation errors', async () => {
            const documentData = { title: 'Test Document' };
            const generatedContent = { chapters: [] };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({
                success: false,
                error: 'DOCX generation failed'
            });

            const result = await exportAsDocx(documentData, generatedContent);

            expect(result.success).toBe(false);
            expect(result.error).toBe('DOCX generation failed');
        });

        test('should handle service import errors', async () => {
            const documentData = { title: 'Test Document' };
            const generatedContent = { chapters: [] };

            // Mock the import to throw an error
            mockGenerateAndDownloadDocxWithImages.mockRejectedValue(new Error('Service unavailable'));

            const result = await exportAsDocx(documentData, generatedContent);

            expect(result.success).toBe(false);
            expect(result.error).toBe('Service unavailable');
        });

        test('should handle editor getHTML errors', async () => {
            const documentData = { title: 'Test Document' };
            const generatedContent = { chapters: [] };
            const mockEditor = {
                getHTML: jest.fn().mockImplementation(() => {
                    throw new Error('Editor error');
                })
            };

            const result = await exportAsDocx(documentData, generatedContent, mockEditor);

            expect(result.success).toBe(false);
            expect(result.error).toBe('Editor error');
        });
    });

    describe('content type detection', () => {
        test('should use HTML content type when editor is provided', async () => {
            const documentData = { title: 'Test Document' };
            const generatedContent = { chapters: [] };
            const mockEditor = {
                getHTML: jest.fn().mockReturnValue('<p>HTML content</p>')
            };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({ success: true });

            await exportAsDocx(documentData, generatedContent, mockEditor);

            expect(mockGenerateAndDownloadDocxWithImages).toHaveBeenCalledWith(
                documentData,
                '<p>HTML content</p>',
                'html'
            );
        });

        test('should use markdown content type when no editor provided', async () => {
            const documentData = { title: 'Test Document' };
            const generatedContent = {
                chapters: [{ title: 'Chapter', content: 'Content' }]
            };

            mockGenerateAndDownloadDocxWithImages.mockResolvedValue({ success: true });

            await exportAsDocx(documentData, generatedContent);

            expect(mockGenerateAndDownloadDocxWithImages).toHaveBeenCalledWith(
                documentData,
                expect.any(String),
                'markdown'
            );
        });
    });
});