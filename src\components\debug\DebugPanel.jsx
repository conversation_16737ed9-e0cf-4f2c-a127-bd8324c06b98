import React, { useState } from 'react'
import But<PERSON> from '../ui/Button'
import { supabaseDebug } from '../../utils/supabaseDebug'
import { authTests } from '../../utils/authTest'

const DebugPanel = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [testResults, setTestResults] = useState(null)
  const [isRunning, setIsRunning] = useState(false)

  const runSupabaseTests = async () => {
    setIsRunning(true)
    console.log('🧪 Running Supabase diagnostic tests...')
    
    try {
      const results = await supabaseDebug.runAllTests()
      setTestResults(results)
      console.log('✅ Supabase tests completed:', results)
    } catch (error) {
      console.error('❌ Supabase tests failed:', error)
      setTestResults({ success: false, error: error.message })
    } finally {
      setIsRunning(false)
    }
  }

  const runAuthTests = async () => {
    setIsRunning(true)
    console.log('🧪 Running Auth tests...')
    
    try {
      const results = await authTests.runAllTests()
      setTestResults(results)
      console.log('✅ Auth tests completed:', results)
    } catch (error) {
      console.error('❌ Auth tests failed:', error)
      setTestResults({ success: false, error: error.message })
    } finally {
      setIsRunning(false)
    }
  }

  const testRegistration = async () => {
    setIsRunning(true)
    console.log('🧪 Testing registration flow...')
    
    try {
      // Test with a dummy email
      const testEmail = `test-${Date.now()}@example.com`
      const testPassword = 'TestPassword123!'
      
      console.log('📧 Testing registration with:', testEmail)
      
      // Import the auth helpers directly
      const { authHelpers } = await import('../../lib/supabase')
      
      const result = await authHelpers.signUp(testEmail, testPassword, {
        full_name: 'Test User',
        user_type: 'student'
      })
      
      console.log('📥 Registration test result:', result)
      setTestResults({ 
        success: !result.error, 
        message: result.error ? result.error.message : 'Registration test successful',
        data: result.data 
      })
    } catch (error) {
      console.error('❌ Registration test failed:', error)
      setTestResults({ success: false, error: error.message })
    } finally {
      setIsRunning(false)
    }
  }

  const clearResults = () => {
    setTestResults(null)
  }

  if (!import.meta.env.DEV) {
    return null // Only show in development
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Toggle Button */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        variant="primary"
        size="sm"
        className="mb-2 shadow-lg"
      >
        🔧 Debug
      </Button>

      {/* Debug Panel */}
      {isOpen && (
        <div className="bg-surface border border-border rounded-lg shadow-elevated p-4 w-80 max-h-96 overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-text-primary">Debug Panel</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-text-muted hover:text-text-primary"
            >
              ✕
            </button>
          </div>

          <div className="space-y-2 mb-4">
            <Button
              onClick={runSupabaseTests}
              disabled={isRunning}
              variant="outline"
              size="sm"
              fullWidth
            >
              {isRunning ? 'Running...' : 'Test Supabase'}
            </Button>

            <Button
              onClick={runAuthTests}
              disabled={isRunning}
              variant="outline"
              size="sm"
              fullWidth
            >
              {isRunning ? 'Running...' : 'Test Auth Flow'}
            </Button>

            <Button
              onClick={testRegistration}
              disabled={isRunning}
              variant="outline"
              size="sm"
              fullWidth
            >
              {isRunning ? 'Running...' : 'Test Registration'}
            </Button>

            {testResults && (
              <Button
                onClick={clearResults}
                variant="ghost"
                size="sm"
                fullWidth
              >
                Clear Results
              </Button>
            )}
          </div>

          {/* Results Display */}
          {testResults && (
            <div className={`p-3 rounded-lg text-sm ${
              testResults.success 
                ? 'bg-success/10 border border-success/20 text-success' 
                : 'bg-error/10 border border-error/20 text-error'
            }`}>
              <div className="font-medium mb-1">
                {testResults.success ? '✅ Success' : '❌ Failed'}
              </div>
              {testResults.message && (
                <div className="text-xs opacity-80">
                  {testResults.message}
                </div>
              )}
              {testResults.error && (
                <div className="text-xs opacity-80 mt-1">
                  Error: {testResults.error}
                </div>
              )}
            </div>
          )}

          <div className="mt-4 pt-3 border-t border-border">
            <p className="text-xs text-text-muted">
              Check browser console for detailed logs
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

export default DebugPanel
