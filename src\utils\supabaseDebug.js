// Supabase Debug Utilities
import { supabase } from '../lib/supabase'

export const supabaseDebug = {
  // Test basic connection
  async testConnection() {
    console.log('🔍 Testing Supabase connection...')
    console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL)
    console.log('Supabase Anon Key (first 20 chars):', import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 20) + '...')
    
    try {
      // Test a simple query
      const { data, error } = await supabase
        .from('user_profiles')
        .select('count')
        .limit(1)
      
      if (error) {
        console.log('⚠️ Connection test error:', error)
        return { success: false, error }
      }
      
      console.log('✅ Supabase connection successful')
      return { success: true, data }
    } catch (err) {
      console.error('❌ Connection test failed:', err)
      return { success: false, error: err }
    }
  },

  // Test auth configuration
  async testAuth() {
    console.log('🔐 Testing Supabase Auth configuration...')
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.log('⚠️ Auth test error:', error)
        return { success: false, error }
      }
      
      console.log('✅ Auth configuration working')
      console.log('Current session:', session ? 'Active' : 'None')
      return { success: true, session }
    } catch (err) {
      console.error('❌ Auth test failed:', err)
      return { success: false, error: err }
    }
  },

  // Test user_profiles table access
  async testUserProfilesTable() {
    console.log('📊 Testing user_profiles table access...')
    
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .limit(5)
      
      if (error) {
        console.log('⚠️ Table access error:', error)
        return { success: false, error }
      }
      
      console.log('✅ user_profiles table accessible')
      console.log('Existing profiles:', data?.length || 0)
      return { success: true, data }
    } catch (err) {
      console.error('❌ Table access failed:', err)
      return { success: false, error: err }
    }
  },

  // Test RLS policies
  async testRLSPolicies() {
    console.log('🛡️ Testing RLS policies...')
    
    try {
      // This should fail if not authenticated (which is expected)
      const { data, error } = await supabase
        .from('user_profiles')
        .insert([{
          id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
          email: '<EMAIL>',
          full_name: 'Test User'
        }])
      
      if (error) {
        if (error.message.includes('RLS') || error.message.includes('policy')) {
          console.log('✅ RLS policies are active (insert blocked as expected)')
          return { success: true, message: 'RLS working correctly' }
        } else {
          console.log('⚠️ Unexpected error:', error)
          return { success: false, error }
        }
      }
      
      console.log('⚠️ Insert succeeded when it should have been blocked by RLS')
      return { success: false, message: 'RLS may not be working correctly' }
    } catch (err) {
      console.error('❌ RLS test failed:', err)
      return { success: false, error: err }
    }
  },

  // Test the trigger function
  async testTriggerFunction() {
    console.log('⚙️ Testing trigger function exists...')
    
    try {
      const { data, error } = await supabase
        .rpc('handle_new_user')
        .then(() => ({ success: true }))
        .catch((err) => ({ success: false, error: err }))
      
      if (error) {
        console.log('⚠️ Trigger function test:', error)
        return { success: false, error }
      }
      
      console.log('✅ Trigger function accessible')
      return { success: true }
    } catch (err) {
      console.log('ℹ️ Trigger function test (expected to fail):', err.message)
      return { success: true, message: 'Function exists but requires auth context' }
    }
  },

  // Run all tests
  async runAllTests() {
    console.log('🧪 Running Supabase diagnostic tests...')
    console.log('=' .repeat(60))
    
    const tests = [
      { name: 'Connection', test: () => this.testConnection() },
      { name: 'Auth Config', test: () => this.testAuth() },
      { name: 'User Profiles Table', test: () => this.testUserProfilesTable() },
      { name: 'RLS Policies', test: () => this.testRLSPolicies() },
      { name: 'Trigger Function', test: () => this.testTriggerFunction() }
    ]
    
    const results = {}
    
    for (const { name, test } of tests) {
      console.log(`\n🔍 Running ${name} test...`)
      results[name] = await test()
    }
    
    console.log('\n' + '=' .repeat(60))
    console.log('📋 TEST RESULTS SUMMARY:')
    console.log('=' .repeat(60))
    
    Object.entries(results).forEach(([testName, result]) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL'
      const message = result.error?.message || result.message || ''
      console.log(`${status} ${testName}: ${message}`)
    })
    
    const allPassed = Object.values(results).every(r => r.success)
    
    console.log('\n' + '=' .repeat(60))
    if (allPassed) {
      console.log('🎉 All tests passed! Supabase is configured correctly.')
    } else {
      console.log('⚠️ Some tests failed. Check the configuration.')
    }
    
    return { success: allPassed, results }
  }
}

// Make available globally in development
if (import.meta.env.DEV) {
  window.supabaseDebug = supabaseDebug
}

export default supabaseDebug
