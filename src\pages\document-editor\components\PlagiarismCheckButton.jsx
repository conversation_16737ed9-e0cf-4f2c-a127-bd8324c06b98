import React, { useState } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

/**
 * PlagiarismCheckButton - Button component for triggering plagiarism checks in document editor
 * Integrates with existing UI patterns and provides loading states
 */
const PlagiarismCheckButton = ({ 
  onPlagiarismCheck, 
  isChecking = false, 
  disabled = false,
  className = '' 
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const handleClick = () => {
    if (!disabled && !isChecking && onPlagiarismCheck) {
      onPlagiarismCheck();
    }
  };

  return (
    <div className={`relative ${className}`}>
      <Button
        variant="outline"
        onClick={handleClick}
        disabled={disabled || isChecking}
        iconName={isChecking ? "Loader2" : "Shield"}
        iconPosition="left"
        className={`text-sm ${isChecking ? 'animate-spin' : ''}`}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {isChecking ? 'Checking...' : 'Check Plagiarism'}
      </Button>

      {/* Tooltip */}
      {showTooltip && !isChecking && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap z-50">
          Check document for plagiarism and AI content
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
        </div>
      )}
    </div>
  );
};

export default PlagiarismCheckButton;
