import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = 'https://mlfojzeyywdxlpbgbtsv.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1sZm9qemV5eXdkeGxwYmdidHN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU1NzE4NzQsImV4cCI6MjA1MTE0Nzg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testConnection() {
  try {
    console.log('🔍 Testing Supabase connection...')
    
    // Test basic connection
    const { data, error } = await supabase.from('auth.users').select('count').limit(1)
    
    if (error) {
      console.log('⚠️  Connection test result:', error.message)
      console.log('ℹ️  This is expected if tables don\'t exist yet')
    } else {
      console.log('✅ Supabase connection successful!')
    }
    
    // Test auth
    const { data: session } = await supabase.auth.getSession()
    console.log('🔐 Current session:', session.session ? 'Active' : 'None')
    
    // Test if we can create a simple table
    console.log('🛠️  Testing table creation...')
    
    // Note: This will only work if you have the proper permissions
    // In a real Supabase project, you'd run this through the dashboard or with service key
    
  } catch (error) {
    console.error('❌ Connection test failed:', error)
  }
}

testConnection()
