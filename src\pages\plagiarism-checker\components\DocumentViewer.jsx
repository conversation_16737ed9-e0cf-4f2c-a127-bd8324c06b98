import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const DocumentViewer = ({ document, matches, onHighlightSelect }) => {
  const [selectedMatch, setSelectedMatch] = useState(null);
  const [viewMode, setViewMode] = useState('split'); // split, original, comparison

  const highlightedContent = document.content.split('\n').map((paragraph, pIndex) => {
    let highlightedParagraph = paragraph;
    
    // Apply highlights for matches
    matches.forEach((match, mIndex) => {
      if (match.paragraphIndex === pIndex) {
        const highlightClass = selectedMatch === mIndex ? 
          'bg-primary/20 border-l-4 border-primary' : 'bg-warning/20 hover:bg-warning/30 cursor-pointer';
        
        highlightedParagraph = highlightedParagraph.replace(
          match.text,
          `<span class="${highlightClass}" data-match="${mIndex}">${match.text}</span>`
        );
      }
    });
    
    return highlightedParagraph;
  });

  const handleMatchClick = (matchIndex) => {
    setSelectedMatch(matchIndex);
    onHighlightSelect(matches[matchIndex]);
  };

  const getMatchSeverity = (similarity) => {
    if (similarity > 80) return { color: 'text-error', bg: 'bg-error/10', label: 'High' };
    if (similarity > 60) return { color: 'text-warning', bg: 'bg-warning/10', label: 'Medium' };
    return { color: 'text-success', bg: 'bg-success/10', label: 'Low' };
  };

  return (
    <div className="bg-surface rounded-lg border border-border">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div>
          <h3 className="text-lg font-semibold text-text-primary">{document.name}</h3>
          <p className="text-sm text-text-secondary">
            {matches.length} potential matches found
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex bg-background rounded-lg p-1">
            <button
              onClick={() => setViewMode('original')}
              className={`px-3 py-1 text-xs rounded transition-micro ${
                viewMode === 'original' ?'bg-surface text-text-primary shadow-elevation-1' :'text-text-secondary hover:text-text-primary'
              }`}
            >
              Original
            </button>
            <button
              onClick={() => setViewMode('split')}
              className={`px-3 py-1 text-xs rounded transition-micro ${
                viewMode === 'split' ?'bg-surface text-text-primary shadow-elevation-1' :'text-text-secondary hover:text-text-primary'
              }`}
            >
              Split View
            </button>
            <button
              onClick={() => setViewMode('comparison')}
              className={`px-3 py-1 text-xs rounded transition-micro ${
                viewMode === 'comparison' ?'bg-surface text-text-primary shadow-elevation-1' :'text-text-secondary hover:text-text-primary'
              }`}
            >
              Comparison
            </button>
          </div>
          
          <Button variant="ghost" size="sm">
            <Icon name="ZoomIn" size={16} />
          </Button>
          <Button variant="ghost" size="sm">
            <Icon name="Download" size={16} />
          </Button>
        </div>
      </div>

      <div className="flex h-96">
        {/* Document Structure Panel */}
        <div className="w-64 border-r border-border bg-background p-4 overflow-y-auto">
          <h4 className="text-sm font-semibold text-text-primary mb-3">Document Structure</h4>
          
          <div className="space-y-2">
            {document.sections.map((section, index) => (
              <div key={index} className="p-2 rounded hover:bg-surface cursor-pointer transition-micro">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-primary">{section.title}</span>
                  {section.matchCount > 0 && (
                    <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                      section.matchCount > 3 ? 'bg-error/20 text-error' :
                      section.matchCount > 1 ? 'bg-warning/20 text-warning': 'bg-success/20 text-success'
                    }`}>
                      {section.matchCount}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6">
            <h4 className="text-sm font-semibold text-text-primary mb-3">Match Summary</h4>
            <div className="space-y-2">
              {matches.slice(0, 5).map((match, index) => {
                const severity = getMatchSeverity(match.similarity);
                return (
                  <div
                    key={index}
                    onClick={() => handleMatchClick(index)}
                    className={`p-2 rounded cursor-pointer transition-micro ${
                      selectedMatch === index ? 'bg-primary/10 border border-primary/20' : 'hover:bg-surface'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium text-text-primary">
                        Match {index + 1}
                      </span>
                      <span className={`text-xs font-bold ${severity.color}`}>
                        {match.similarity}%
                      </span>
                    </div>
                    <p className="text-xs text-text-secondary truncate">
                      {match.source}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex">
          {/* Original Document */}
          {(viewMode === 'original' || viewMode === 'split') && (
            <div className={`${viewMode === 'split' ? 'w-1/2' : 'w-full'} p-4 overflow-y-auto`}>
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-text-primary mb-2">Original Document</h4>
                <div className="text-xs text-text-secondary">
                  Click on highlighted text to view source details
                </div>
              </div>
              
              <div className="prose prose-base max-w-none">
                {highlightedContent.map((paragraph, index) => (
                  <p
                    key={index}
                    className="mb-4 leading-relaxed text-text-primary"
                    dangerouslySetInnerHTML={{ __html: paragraph }}
                    onClick={(e) => {
                      const matchIndex = e.target.dataset.match;
                      if (matchIndex !== undefined) {
                        handleMatchClick(parseInt(matchIndex));
                      }
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Comparison/Source View */}
          {(viewMode === 'split' || viewMode === 'comparison') && selectedMatch !== null && (
            <div className={`${viewMode === 'split' ? 'w-1/2 border-l border-border' : 'w-full'} p-4 overflow-y-auto`}>
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-semibold text-text-primary">Source Comparison</h4>
                  <Button variant="ghost" size="sm">
                    <Icon name="ExternalLink" size={14} />
                  </Button>
                </div>
                
                <div className="flex items-center space-x-4 text-xs text-text-secondary">
                  <span>Similarity: {matches[selectedMatch]?.similarity}%</span>
                  <span>•</span>
                  <span className="truncate">{matches[selectedMatch]?.source}</span>
                </div>
              </div>

              <div className="space-y-4">
                <div className="p-3 bg-error/5 border border-error/20 rounded-lg">
                  <div className="text-xs font-medium text-error mb-2">Your Text</div>
                  <p className="text-sm text-text-primary leading-relaxed">
                    {matches[selectedMatch]?.originalText}
                  </p>
                </div>

                <div className="p-3 bg-warning/5 border border-warning/20 rounded-lg">
                  <div className="text-xs font-medium text-warning mb-2">Source Text</div>
                  <p className="text-sm text-text-primary leading-relaxed">
                    {matches[selectedMatch]?.sourceText}
                  </p>
                </div>

                <div className="p-3 bg-success/5 border border-success/20 rounded-lg">
                  <div className="text-xs font-medium text-success mb-2">Suggested Revision</div>
                  <p className="text-sm text-text-primary leading-relaxed">
                    {matches[selectedMatch]?.suggestion}
                  </p>
                  <Button variant="outline" size="sm" className="mt-2">
                    <Icon name="Copy" size={14} className="mr-1" />
                    Copy Suggestion
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;