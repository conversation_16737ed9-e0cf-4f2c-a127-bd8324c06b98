/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        // Primary Colors - Matching reference design
        primary: "#3B82F6", // Blue from reference
        "primary-foreground": "#FFFFFF",
        "primary-light": "#EFF6FF", // Very light blue
        "primary-dark": "#1E40AF", // Darker blue for contrast

        // Secondary Colors
        secondary: "#6B7280", // Gray from reference
        "secondary-foreground": "#FFFFFF",
        "secondary-light": "#F9FAFB",

        // Accent Colors
        accent: "#8B5CF6", // Purple accent
        "accent-foreground": "#FFFFFF",
        "accent-light": "#F3E8FF",

        // Background Colors - Matching reference design
        background: "#F8F9FA", // Light gray background from reference
        surface: "#FFFFFF", // Pure white for cards
        "surface-secondary": "#F3F4F6", // Slightly darker gray
        "surface-hover": "#F1F5F9", // Hover states

        // Text Colors - Matching reference contrast
        "text-primary": "#111827", // Dark gray for primary text
        "text-secondary": "#6B7280", // Medium gray for secondary text
        "text-muted": "#9CA3AF", // Light gray for muted text

        // Status Colors
        success: "#10B981",
        "success-light": "#D1FAE5",
        warning: "#F59E0B",
        "warning-light": "#FEF3C7",
        error: "#EF4444",
        "error-light": "#FEE2E2",

        // Border Colors - Matching reference subtlety
        border: "#E5E7EB",
        "border-light": "#F3F4F6",
        "border-strong": "#D1D5DB",

        // Hero Gradient Colors - Matching reference dark hero
        "hero-start": "#1E293B", // Dark navy
        "hero-middle": "#334155", // Medium slate
        "hero-end": "#475569", // Lighter slate
        "hero-accent": "#3B82F6", // Blue accent in hero
      },
      fontFamily: {
        sans: ["Nunito Sans", "Noto Color Emoji", "ui-sans-serif", "system-ui", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"],
        heading: ["Nunito Sans", "Noto Color Emoji", "ui-sans-serif", "system-ui", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"],
        body: ["Nunito Sans", "Noto Color Emoji", "ui-sans-serif", "system-ui", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"],
        mono: ["JetBrains Mono", "monospace"],
      },
      fontSize: {
        xs: ["0.75rem", { lineHeight: "1rem" }],
        sm: ["0.875rem", { lineHeight: "1.25rem" }],
        base: ["1rem", { lineHeight: "1.5rem" }],
        lg: ["1.125rem", { lineHeight: "1.75rem" }],
        xl: ["1.25rem", { lineHeight: "1.75rem" }],
        "2xl": ["1.5rem", { lineHeight: "2rem" }],
        "3xl": ["1.875rem", { lineHeight: "2.25rem" }],
        "4xl": ["2.25rem", { lineHeight: "2.5rem" }],
      },
      fontWeight: {
        light: "300",
        normal: "400",
        medium: "500",
        semibold: "600",
        bold: "700",
        extrabold: "800",
      },
      boxShadow: {
        soft: "0 1px 3px rgba(0, 0, 0, 0.05)",
        card: "0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)",
        elevated:
          "0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.03)",
        hero: "0 25px 50px -12px rgba(0, 0, 0, 0.15)",
        "inner-soft": "inset 0 1px 2px rgba(0, 0, 0, 0.05)",
      },
      borderRadius: {
        none: "0",
        sm: "0.25rem", // Reduced from 0.375rem (6px to 4px)
        DEFAULT: "0.375rem", // Reduced from 0.5rem (8px to 6px)
        md: "0.5rem", // Reduced from 0.75rem (12px to 8px)
        lg: "0.75rem", // Reduced from 1rem (16px to 12px)
        xl: "1rem", // Reduced from 1.5rem (24px to 16px)
        "2xl": "1.25rem", // Reduced from 2rem (32px to 20px)
        full: "9999px",
      },
      animation: {
        "fade-in": "fadeIn 200ms ease-out",
        "slide-up": "slideUp 300ms ease-out",
        "scale-in": "scaleIn 150ms ease-in-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        scaleIn: {
          "0%": { transform: "scale(0.95)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
      },
      spacing: {
        18: "4.5rem",
        88: "22rem",
      },
      zIndex: {
        1000: "1000",
        1050: "1050",
        1100: "1100",
        1200: "1200",
      },
    },
  },
  plugins: [
    require("@tailwindcss/typography"),
    require("@tailwindcss/forms"),
    require("tailwindcss-animate"),
  ],
};
