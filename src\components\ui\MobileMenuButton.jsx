import React from 'react';
import { useSidebar } from '../../contexts/SidebarContext';
import Icon from '../AppIcon';

/**
 * MobileMenuButton - Kebab/hamburger menu button for mobile navigation
 * Shows only on mobile/tablet devices and toggles the mobile sidebar
 * Transforms from hamburger to X icon when sidebar is open
 */
const MobileMenuButton = ({ className = '', isOpen = false }) => {
  const { isMobile, toggleMobileSidebar } = useSidebar();

  // Only show on mobile/tablet devices
  if (!isMobile) {
    return null;
  }

  return (
    <button
      onClick={toggleMobileSidebar}
      className={`p-3 text-text-secondary hover:text-text-primary hover:bg-surface-hover rounded-lg transition-all duration-200 lg:hidden ${
        isOpen ? 'relative z-1055' : ''
      } ${className}`}
      style={{ minWidth: '44px', minHeight: '44px' }} // Ensure minimum touch target
      title={isOpen ? "Close navigation menu" : "Open navigation menu"}
      aria-label={isOpen ? "Close navigation menu" : "Open navigation menu"}
    >
      <Icon
        name={isOpen ? "X" : "Menu"}
        size={20}
        className="transition-transform duration-200 ease-in-out"
      />
    </button>
  );
};

export default MobileMenuButton;
