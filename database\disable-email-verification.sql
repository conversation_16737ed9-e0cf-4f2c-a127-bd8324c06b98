-- Disable Email Verification in Supabase
-- Run this in your Supabase SQL Editor to disable email verification

-- 1. Check current auth configuration
SELECT 
    'Current auth configuration:' as info,
    *
FROM auth.config;

-- 2. Disable email confirmation requirements
-- Note: This might not work in all Supabase versions, as auth.config might be read-only
-- The primary way is through the dashboard settings

-- 3. Update the trigger function to handle users without email confirmation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_name TEXT;
BEGIN
    -- Log the trigger execution (for debugging)
    RAISE LOG 'handle_new_user trigger fired for user ID: %', NEW.id;
    
    -- Extract email (required)
    user_email := NEW.email;
    
    -- Extract full name from metadata or use email as fallback
    user_name := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );
    
    -- Log what we're about to insert
    RAISE LOG 'Creating profile for user: email=%, name=%, confirmed=%', 
        user_email, user_name, NEW.email_confirmed_at;
    
    -- Insert the user profile regardless of email confirmation status
    INSERT INTO public.user_profiles (
        id, 
        email, 
        full_name,
        user_type,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        user_email,
        user_name,
        COALESCE(NEW.raw_user_meta_data->>'user_type', 'student'),
        NOW(),
        NOW()
    );
    
    RAISE LOG 'Successfully created profile for user: %', NEW.id;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE LOG 'Error creating user profile for %: % %', NEW.id, SQLERRM, SQLSTATE;
        -- Still return NEW so the user creation doesn't fail
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Ensure the trigger is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW 
    EXECUTE FUNCTION public.handle_new_user();

-- 5. Update RLS policies to allow profile creation for unconfirmed users
DROP POLICY IF EXISTS "Users can insert own profile" ON public.user_profiles;

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT 
    WITH CHECK (
        auth.uid() = id OR 
        auth.role() = 'service_role' OR
        auth.role() = 'authenticated'
    );

-- 6. Create a more permissive policy for profile access
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;

CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT 
    USING (
        auth.uid() = id OR 
        auth.role() = 'service_role'
    );

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE 
    USING (
        auth.uid() = id OR 
        auth.role() = 'service_role'
    );

-- 7. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT ALL ON public.user_profiles TO authenticated, anon;
GRANT ALL ON public.user_sessions TO authenticated, anon;
GRANT ALL ON public.login_history TO authenticated, anon;

-- 8. Test the setup
SELECT 
    'Email verification disabled setup completed' as status,
    COUNT(*) as existing_profiles,
    'Check Supabase Dashboard > Auth > Settings to disable email confirmation' as note
FROM public.user_profiles;

-- 9. Show current users (for debugging)
SELECT 
    'Current auth.users:' as info,
    id,
    email,
    email_confirmed_at,
    created_at
FROM auth.users
ORDER BY created_at DESC
LIMIT 5;
