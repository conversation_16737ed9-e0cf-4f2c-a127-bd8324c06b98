# DocForge AI Content Data Structure and Persistence

## Overview
DocForge AI uses a hybrid content storage system that combines markdown persistence with runtime block-based editing. This document details the complete data structure, conversion mechanisms, and persistence strategies.

## Document Structure Hierarchy

### 1. Top-Level Document Structure
```javascript
{
  documentData: {
    // Questionnaire data
    documentPurpose: { primaryType, title, urgency, ... },
    topicAndNiche: { mainTopic, subNiche, ... },
    audienceAnalysis: { primaryAudience, ... },
    contentDetails: { addImages, length, ... },
    toneAndVoice: { toneOfVoice, writingStyle, ... },
    documentOutline: { approved, chapters, ... }
  },
  generatedContent: {
    title: "Document Title",
    introduction: {
      content: "markdown content",
      wordCount: 150
    },
    chapters: [
      {
        id: "chapter-1",
        number: 1,
        title: "Chapter Title",
        content: "markdown content",
        wordCount: 500,
        sections: ["section1", "section2"]
      }
    ],
    conclusion: {
      content: "markdown content", 
      wordCount: 200
    }
  }
}
```

### 2. Chapter-Based Content Organization
**Storage Format**: Each chapter stores content as markdown string
**Runtime Format**: Markdown converted to blocks for editing
**Persistence**: Blocks converted back to markdown on save

```javascript
// Chapter structure
{
  id: "chapter-1",
  number: 1,
  title: "Chapter Title",
  content: "# Heading\n\nParagraph content\n\n- List item 1\n- List item 2",
  wordCount: 500,
  sections: ["Introduction", "Main Content", "Summary"]
}
```

## Block Data Structure

### 1. Runtime Block Format
```javascript
{
  id: "chapter-1-block-1703123456789-abc123def",
  type: "paragraph|heading|list|image",
  content: {
    // Type-specific content structure
  }
}
```

### 2. Block Content Structures

#### Paragraph Block
```javascript
{
  type: "paragraph",
  content: {
    text: "This is paragraph content with **bold** and *italic* formatting."
  }
}
```

#### Heading Block
```javascript
{
  type: "heading",
  content: {
    text: "Chapter Title",
    level: 2  // 1-6 for H1-H6
  }
}
```

#### List Block
```javascript
{
  type: "list",
  content: {
    type: "ordered|unordered",
    items: [
      "First list item",
      "Second list item",
      "Third list item"
    ]
  }
}
```

#### Image Block
```javascript
{
  type: "image",
  content: {
    src: "https://example.com/image.jpg",
    alt: "Image description",
    caption: "Image caption text",
    style: {
      width: "100%",
      alignment: "center|left|right"
    },
    metadata: {
      source: "unsplash|upload|url",
      originalSize: { width: 1920, height: 1080 }
    }
  }
}
```

## Content Conversion System

### 1. Markdown to HTML Conversion (Tiptap)
**Function**: Content conversion handled by Tiptap's built-in parsers
**Location**: `TiptapRichTextEditor.jsx` and `ContentConverter.js`

#### Parsing Logic
```javascript
const convertMarkdownToBlocks = (markdownText) => {
  if (!markdownText) return [];

  const lines = markdownText.split('\n');
  const blocks = [];
  let currentParagraph = [];
  let currentUnorderedList = [];
  let currentOrderedList = [];
  let currentCodeBlock = [];
  let codeBlockLanguage = null;
  let inCodeBlock = false;
  let blockId = 0;

  // Process each line
  lines.forEach(line => {
    const trimmedLine = line.trim();
    
    // Handle different markdown elements
    if (trimmedLine.startsWith('#')) {
      // Heading
      const level = (trimmedLine.match(/^#+/) || [''])[0].length;
      const text = trimmedLine.replace(/^#+\s*/, '');
      blocks.push({
        id: `${id}-block-${blockId++}`,
        type: 'heading',
        content: { text, level }
      });
    } else if (trimmedLine.match(/^\d+\.\s/)) {
      // Ordered list item
      const content = trimmedLine.replace(/^\d+\.\s/, '');
      currentOrderedList.push(content);
    } else if (trimmedLine.startsWith('- ')) {
      // Unordered list item
      const content = trimmedLine.substring(2);
      currentUnorderedList.push(content);
    } else if (trimmedLine.match(/^!\[.*?\]\(.*?\)$/)) {
      // Image markdown
      const imageMatch = trimmedLine.match(/^!\[(.*?)\]\((.*?)\)$/);
      if (imageMatch) {
        const [, alt, src] = imageMatch;
        blocks.push({
          id: `${id}-block-${blockId++}`,
          type: 'image',
          content: {
            src, alt: alt || 'Image', caption: '',
            style: { width: '100%', alignment: 'center' },
            metadata: { source: 'markdown' }
          }
        });
      }
    } else {
      // Regular paragraph content
      if (trimmedLine) {
        currentParagraph.push(line);
      }
    }
  });

  return blocks;
};
```

### 2. HTML to Markdown Conversion (Tiptap)
**Function**: Content conversion handled by Tiptap's built-in serializers
**Location**: `ContentConverter.js` utility functions

```javascript
const convertBlocksToMarkdown = (blocks) => {
  return blocks.map(block => {
    switch (block.type) {
      case 'heading':
        const hashes = '#'.repeat(block.content.level);
        return `${hashes} ${block.content.text}`;
      case 'paragraph':
        return block.content.text;
      case 'list':
        if (block.content.type === 'ordered') {
          return block.content.items.map((item, index) => 
            `${index + 1}. ${item}`).join('\n');
        } else {
          return block.content.items.map(item => `- ${item}`).join('\n');
        }
      case 'image':
        const alt = block.content.alt || 'Image';
        const src = block.content.src || '';
        return `![${alt}](${src})`;
      default:
        return '';
    }
  }).join('\n\n');
};
```

### 3. JSON to Markdown Fallback (Tiptap)
**Purpose**: Handle cases where AI returns JSON instead of markdown
**Location**: `TiptapRichTextEditor.jsx` and `ContentConverter.js`

```javascript
const convertJsonToMarkdown = (jsonString) => {
  try {
    const jsonData = JSON.parse(jsonString);

    // Handle array of content items
    if (Array.isArray(jsonData)) {
      return jsonData.map(item => {
        if (typeof item === 'string') return item;
        if (item.type === 'paragraph' && item.content) return item.content;
        if (item.type === 'heading' && item.content) {
          const level = item.level || 2;
          const hashes = '#'.repeat(level);
          return `${hashes} ${item.content}`;
        }
        if (item.content) return item.content;
        if (item.text) return item.text;
        return JSON.stringify(item);
      }).join('\n\n');
    }

    // Handle object with content properties
    if (jsonData.content) return jsonData.content;
    if (jsonData.text) return jsonData.text;
    
    return JSON.stringify(jsonData);
  } catch (error) {
    return jsonString; // Return original if not valid JSON
  }
};
```

## Auto-Save and Persistence System

### 1. Auto-Save Hook
**Location**: `src/pages/document-editor/components/blocks/hooks/useAutoSave.js`

```javascript
export const useAutoSave = (onSave, delay = 300) => {
  const timeoutRef = useRef(null);
  const isUpdatingRef = useRef(false);

  const debouncedSave = useCallback((data) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set updating flag to prevent cursor position issues
    isUpdatingRef.current = true;

    // Set new timeout for save operation
    timeoutRef.current = setTimeout(() => {
      onSave(data);
      isUpdatingRef.current = false;
    }, delay);
  }, [onSave, delay]);

  return { debouncedSave, isUpdating, cancelSave };
};
```

### 2. Content Update Flow (Tiptap)
```
1. User types in Tiptap editor → Editor.onUpdate
2. Tiptap processes content changes internally
3. Editor calls onUpdate callback with HTML content
4. TiptapRichTextEditor calls onContentUpdate prop
5. DocumentCanvas.handleContentUpdate
6. DocumentEditor.handleContentUpdate
7. Content persisted to document state
```

### 3. Tiptap Editor State Management
```javascript
// TiptapRichTextEditor state
const [error, setError] = useState(null);
const [isLoading, setIsLoading] = useState(true);

// Tiptap editor configuration
const {
  editor,
  wordCount,
  characterCount,
  getHTML
} = useTiptapCore({
  content: convertedContent || '',
  onUpdate: (html) => {
    try {
      // Call the onContentUpdate prop with the new content
      if (onContentUpdate) {
        onContentUpdate(html);
      }
    } catch (err) {
      console.error('Error updating content:', err);
      setError(err);
    }
  },
  readOnly: isReadOnly
});
```

## Data Validation and Error Handling

### 1. Content Safety Checks
```javascript
// Check if content looks like JSON before processing
useEffect(() => {
  if (content) {
    let processedContent = content;
    const trimmedContent = content.trim();
    if (trimmedContent.startsWith('{') || trimmedContent.startsWith('[')) {
      processedContent = convertJsonToMarkdown(trimmedContent);
    }
    const convertedBlocks = convertMarkdownToBlocks(processedContent);
    setBlocks(convertedBlocks);
  }
}, [content]);
```

### 2. Block ID Generation
```javascript
const generateBlockId = () => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return `${id}-block-${timestamp}-${random}`;
};
```

### 3. Fallback Strategies
- **Empty Content**: Default to empty paragraph block
- **Invalid JSON**: Return original string
- **Missing Block Types**: Skip unknown blocks
- **Malformed Markdown**: Parse as paragraph content

## Integration Points

### 1. Document Editor Integration
- **DocumentEditor** manages overall document state
- **DocumentCanvas** handles chapter-level content
- **TiptapRichTextEditor** manages content editing with custom nodes
- **Custom Tiptap Nodes** handle content-specific editing (paragraphs, headings)

### 2. AI Service Integration
- AI generates markdown content
- Content converted to HTML for Tiptap editor
- Tiptap editor handles content editing with custom nodes
- JSON fallback for AI response handling in ContentConverter

### 3. Export System Integration
- Markdown content used for export generation
- Block structure preserved during export
- Image metadata included in export data

This data structure provides flexibility for editing while maintaining compatibility with markdown-based storage and AI processing systems.
