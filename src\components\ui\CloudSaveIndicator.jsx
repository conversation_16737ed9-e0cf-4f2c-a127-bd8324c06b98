import React from 'react';

/**
 * CloudSaveIndicator - Reusable cloud icon with checkmark for save status
 * Provides consistent visual indication of document save state
 *
 * Features:
 * - Outline-only design (no filled backgrounds)
 * - Consistent color across all states (no color changes)
 * - Checkmark visually attached/overlaid on cloud icon
 * - Clear visual distinction between saved and unsaved states
 */
const CloudSaveIndicator = ({
  size = 'large',
  className = '',
  title = 'Document saved',
  showCheckmark = true,
  color = 'text-gray-600' // Default consistent color
}) => {
  // Size configurations
  const sizeConfig = {
    small: {
      cloud: 'w-8 h-8',
      checkmark: 'w-4 h-4',
      position: '-bottom-0.5 -right-0.5',
      strokeWidth: 2.5,
      cloudStrokeWidth: 1.5
    },
    medium: {
      cloud: 'w-10 h-10',
      checkmark: 'w-4.5 h-4.5',
      position: '-bottom-1 -right-1',
      strokeWidth: 2.5,
      cloudStrokeWidth: 1.5
    },
    large: {
      cloud: 'w-12 h-12',
      checkmark: 'w-5 h-5',
      position: '-bottom-1 -right-1',
      strokeWidth: 2,
      cloudStrokeWidth: 2
    }
  };

  const config = sizeConfig[size] || sizeConfig.medium;

  return (
    <div className={`flex items-center ${color} ${className}`} title={title}>
      <div className="relative">
        {/* Cloud icon - outline only */}
        <svg
          className={config.cloud}
          fill="none"
          stroke="currentColor"
          strokeWidth={config.cloudStrokeWidth}
          viewBox="0 0 24 24"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M6.5 17.5c-1.38 0-2.5-1.12-2.5-2.5 0-1.09.7-2.02 1.68-2.36C5.96 11.1 7.37 10 9 10c.35 0 .69.06 1 .16C10.95 8.85 12.37 8 14 8c2.21 0 4 1.79 4 4 0 .35-.05.69-.14 1.02.86.32 1.47 1.16 1.47 2.15 0 1.27-1.03 2.3-2.3 2.3H6.5z"/>
        </svg>

        {/* Checkmark overlay - outline only, visually attached to cloud */}
        {showCheckmark && (
          <div className={`absolute ${config.position} flex items-center justify-center`}>
            <svg
              className={config.checkmark}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={config.strokeWidth}
            >
              <path d="M5 13l4 4L19 7"/>
            </svg>
          </div>
        )}
      </div>
    </div>
  );
};

export default CloudSaveIndicator;
