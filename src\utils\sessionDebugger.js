// Session Debugger Utility
// This utility helps diagnose and fix issues with persistent sessions

import { supabase } from '../lib/supabase';

export const sessionDebugger = {
    // Get detailed information about the current session state
    async getSessionDiagnostics() {
        console.log('🔍 Running session diagnostics...');

        const diagnostics = {
            timestamp: new Date().toISOString(),
            browserInfo: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookiesEnabled: navigator.cookieEnabled
            },
            localStorage: {
                keys: Object.keys(localStorage),
                supabaseTokenExists: !!localStorage.getItem('sb-supabase-auth-token'),
                docforgeItems: Object.keys(localStorage).filter(k => k.startsWith('docforge_'))
            },
            sessionStorage: {
                keys: Object.keys(sessionStorage),
                docforgeItems: Object.keys(sessionStorage).filter(k => k.startsWith('docforge_'))
            }
        };

        // Check Supabase session
        try {
            const { data: { session }, error } = await supabase.auth.getSession();

            if (error) {
                diagnostics.supabaseSession = {
                    error: error.message,
                    status: 'error'
                };
            } else if (session) {
                // Don't log the full token for security reasons
                const tokenPreview = session.access_token ?
                    `${session.access_token.substring(0, 10)}...` : 'null';

                diagnostics.supabaseSession = {
                    exists: true,
                    user: {
                        id: session.user.id,
                        email: session.user.email,
                        emailConfirmed: !!session.user.email_confirmed_at
                    },
                    expires: new Date(session.expires_at * 1000).toISOString(),
                    expiresIn: (session.expires_at * 1000) - Date.now(),
                    tokenPreview,
                    status: 'valid'
                };
            } else {
                diagnostics.supabaseSession = {
                    exists: false,
                    status: 'missing'
                };
            }
        } catch (err) {
            diagnostics.supabaseSession = {
                error: err.message,
                status: 'exception'
            };
        }

        console.log('📊 Session diagnostics:', diagnostics);
        return diagnostics;
    },

    // Fix common session issues
    async fixSessionIssues() {
        console.log('🔧 Attempting to fix session issues...');

        const diagnostics = await this.getSessionDiagnostics();
        const fixes = [];

        // Check for token but no session
        if (diagnostics.localStorage.supabaseTokenExists &&
            (!diagnostics.supabaseSession.exists || diagnostics.supabaseSession.status !== 'valid')) {
            console.log('⚠️ Found token but no valid session - clearing token');
            localStorage.removeItem('sb-supabase-auth-token');
            fixes.push('cleared_invalid_token');
        }

        // Check for expired session
        if (diagnostics.supabaseSession.expiresIn < 0) {
            console.log('⚠️ Session expired - clearing session');
            await supabase.auth.signOut();
            fixes.push('cleared_expired_session');
        }

        // Check for corrupted profile cache
        const profileKeys = diagnostics.localStorage.keys.filter(k => k.includes('docforge_profile_'));
        if (profileKeys.length > 0) {
            for (const key of profileKeys) {
                try {
                    const profile = JSON.parse(localStorage.getItem(key));
                    if (!profile || !profile.id) {
                        console.log(`⚠️ Corrupted profile cache found: ${key} - clearing`);
                        localStorage.removeItem(key);
                        fixes.push(`cleared_corrupted_profile_${key}`);
                    }
                } catch (e) {
                    console.log(`⚠️ Invalid JSON in profile cache: ${key} - clearing`);
                    localStorage.removeItem(key);
                    fixes.push(`cleared_invalid_json_${key}`);
                }
            }
        }

        // Force refresh token if needed
        if (diagnostics.supabaseSession.exists &&
            diagnostics.supabaseSession.status === 'valid' &&
            diagnostics.supabaseSession.expiresIn < 30 * 60 * 1000) { // Less than 30 minutes
            console.log('⚠️ Session expiring soon - refreshing token');
            try {
                await supabase.auth.refreshSession();
                fixes.push('refreshed_token');
            } catch (e) {
                console.error('Failed to refresh token:', e);
                fixes.push('refresh_token_failed');
            }
        }

        console.log('🔧 Session fixes applied:', fixes);
        return { fixes, diagnostics };
    },

    // Add debug overlay to the page
    addDebugOverlay() {
        // Remove existing overlay if any
        const existingOverlay = document.getElementById('session-debug-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create overlay container
        const overlay = document.createElement('div');
        overlay.id = 'session-debug-overlay';
        overlay.style.position = 'fixed';
        overlay.style.bottom = '20px';
        overlay.style.right = '20px';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        overlay.style.color = 'white';
        overlay.style.padding = '10px';
        overlay.style.borderRadius = '5px';
        overlay.style.zIndex = '9999';
        overlay.style.fontSize = '12px';
        overlay.style.fontFamily = 'monospace';
        overlay.style.maxWidth = '300px';
        overlay.style.maxHeight = '200px';
        overlay.style.overflow = 'auto';

        // Add title
        const title = document.createElement('div');
        title.textContent = 'Session Debugger';
        title.style.fontWeight = 'bold';
        title.style.marginBottom = '5px';
        title.style.borderBottom = '1px solid rgba(255, 255, 255, 0.3)';
        title.style.paddingBottom = '5px';
        overlay.appendChild(title);

        // Add buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.style.display = 'flex';
        buttonContainer.style.gap = '5px';
        buttonContainer.style.marginBottom = '5px';

        const diagButton = document.createElement('button');
        diagButton.textContent = 'Diagnose';
        diagButton.style.backgroundColor = '#4CAF50';
        diagButton.style.border = 'none';
        diagButton.style.color = 'white';
        diagButton.style.padding = '3px 6px';
        diagButton.style.borderRadius = '3px';
        diagButton.style.cursor = 'pointer';
        diagButton.onclick = async () => {
            const diag = await this.getSessionDiagnostics();
            infoContainer.textContent = JSON.stringify(diag, null, 2);
        };
        buttonContainer.appendChild(diagButton);

        const fixButton = document.createElement('button');
        fixButton.textContent = 'Fix Issues';
        fixButton.style.backgroundColor = '#2196F3';
        fixButton.style.border = 'none';
        fixButton.style.color = 'white';
        fixButton.style.padding = '3px 6px';
        fixButton.style.borderRadius = '3px';
        fixButton.style.cursor = 'pointer';
        fixButton.onclick = async () => {
            const result = await this.fixSessionIssues();
            infoContainer.textContent = JSON.stringify(result, null, 2);
        };
        buttonContainer.appendChild(fixButton);

        const resetButton = document.createElement('button');
        resetButton.textContent = 'Reset & Reload';
        resetButton.style.backgroundColor = '#F44336';
        resetButton.style.border = 'none';
        resetButton.style.color = 'white';
        resetButton.style.padding = '3px 6px';
        resetButton.style.borderRadius = '3px';
        resetButton.style.cursor = 'pointer';
        resetButton.onclick = async () => {
            await supabase.auth.signOut();
            localStorage.clear();
            sessionStorage.clear();
            window.location.reload();
        };
        buttonContainer.appendChild(resetButton);

        overlay.appendChild(buttonContainer);

        // Add info container
        const infoContainer = document.createElement('pre');
        infoContainer.style.margin = '0';
        infoContainer.style.whiteSpace = 'pre-wrap';
        infoContainer.style.fontSize = '10px';
        infoContainer.textContent = 'Click a button above to start debugging';
        overlay.appendChild(infoContainer);

        // Add close button
        const closeButton = document.createElement('button');
        closeButton.textContent = '×';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '5px';
        closeButton.style.right = '5px';
        closeButton.style.backgroundColor = 'transparent';
        closeButton.style.border = 'none';
        closeButton.style.color = 'white';
        closeButton.style.fontSize = '16px';
        closeButton.style.cursor = 'pointer';
        closeButton.onclick = () => overlay.remove();
        overlay.appendChild(closeButton);

        document.body.appendChild(overlay);
        return overlay;
    },

    // Add a debug button to the page
    addDebugButton() {
        // Remove existing button if any
        const existingButton = document.getElementById('session-debug-button');
        if (existingButton) {
            existingButton.remove();
        }

        const button = document.createElement('button');
        button.id = 'session-debug-button';
        button.textContent = '🔍 Debug';
        button.style.position = 'fixed';
        button.style.bottom = '20px';
        button.style.left = '20px';
        button.style.backgroundColor = '#673AB7';
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.borderRadius = '4px';
        button.style.padding = '8px 12px';
        button.style.fontSize = '12px';
        button.style.cursor = 'pointer';
        button.style.zIndex = '9999';
        button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';

        button.onclick = () => {
            this.addDebugOverlay();
        };

        document.body.appendChild(button);
        return button;
    }
};

export default sessionDebugger;