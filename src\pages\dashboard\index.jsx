import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import { useAuth } from '../../contexts/AuthContext';
import { useProjects } from '../../hooks/useProjects';
import { sessionRecovery } from '../../utils/sessionRecovery';
import { sessionDebugger } from '../../utils/sessionDebugger';
import { sessionRepair } from '../../utils/sessionRepair';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import QuickStatsCard from './components/QuickStatsCard';
import DocumentLibrary from './components/DocumentLibrary';
import QuickActions from './components/QuickActions';
import CreditUsageMeter from './components/CreditUsageMeter';
import { ProjectCard, ProjectsLoading, ProjectsError } from '../../components/projects';
import Button from '../../components/ui/Button';

import ActivityFeed from './components/ActivityFeed';
import ChatWidget from '../../components/ui/ChatWidget';
import Icon from '../../components/AppIcon';

const Dashboard = () => {
  const navigate = useNavigate();
  const { contentMargin } = useSidebar();
  const { user, profile, loading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  // Fetch recent projects (last 4 accessed)
  const {
    projects: recentProjects,
    isLoading: isLoadingRecent,
    error: recentError,
    refetch: refetchRecent
  } = useProjects({
    sortBy: 'last_accessed_at',
    sortOrder: 'desc',
    limit: 4
  });

  // Fetch all projects for stats calculations
  const {
    projects: allProjects,
    isLoading: isLoadingStats
  } = useProjects({
    sortBy: 'updated_at',
    sortOrder: 'desc',
    limit: null // Get all projects for stats
  });



  const mockRecentTemplates = [
    {
      id: 1,
      name: "Research Paper Template",
      category: "Academic",
      icon: "FileText"
    },
    {
      id: 2,
      name: "Business Report Template",
      category: "Business",
      icon: "BarChart3"
    },
    {
      id: 3,
      name: "eBook Template",
      category: "Publishing",
      icon: "Book"
    }
  ];



  const mockActivityFeed = [
    {
      id: 1,
      type: "document_created",
      description: "Created new business document 'Marketing Strategy'",
      timestamp: "2024-01-15T10:30:00Z"
    },

    {
      id: 3,
      type: "plagiarism_check",
      description: "Completed plagiarism check for 'Research Paper'",
      timestamp: "2024-01-14T16:20:00Z"
    },
    {
      id: 4,
      type: "template_used",
      description: "Used \'Academic Paper\' template",
      timestamp: "2024-01-14T14:15:00Z"
    },
    {
      id: 5,
      type: "document_shared",
      description: "Shared \'Business Proposal\' publicly",
      timestamp: "2024-01-13T12:45:00Z"
    }
  ];

  const mockUsageHistory = [
    { activity: "Document Generation", credits: 15, icon: "FileText" },
    { activity: "AI Content Enhancement", credits: 8, icon: "Wand2" },
    { activity: "Plagiarism Check", credits: 5, icon: "Shield" },
    { activity: "Template Customization", credits: 3, icon: "Settings" }
  ];

  // Stats calculations using real data
  const totalDocuments = allProjects.length;
  const draftDocuments = allProjects.filter(doc => doc.status === 'draft').length;
  const completedDocuments = allProjects.filter(doc => doc.status === 'completed').length;
  const sharedDocuments = allProjects.filter(doc => doc.status === 'shared').length;

  const plagiarismScans = mockActivityFeed.filter(activity => activity.type === 'plagiarism_check').length;

  // Add progressive loading, timeout mechanism, and automatic session repair
  useEffect(() => {

    
    // Mark the start of loading for session recovery detection
    sessionRecovery.markLoadStart();
    
    // Handle loading state - wait for auth to complete
    if (!authLoading) {
      setIsLoading(false);
      sessionRecovery.clearLoadMarker();
    }
    
    // First safety timeout - try to repair session if still loading after 3 seconds
    const repairTimeout = setTimeout(async () => {
      if (isLoading && authLoading) {
        console.warn('⚠️ Dashboard still loading after 3 seconds - attempting automatic repair');
        
        try {
          // Attempt to automatically repair the session
          await sessionRepair.checkAndRepair();
        } catch (err) {
          console.error('Session repair failed:', err);
        }
      }
    }, 3000);
    
    // Second safety timeout - force render after 5 seconds as a last resort
    const safetyTimeout = setTimeout(() => {
      if (isLoading) {
        console.warn('⚠️ Safety timeout triggered - forcing dashboard to render');
        setIsLoading(false);
        
        // Add debug button for manual troubleshooting
        sessionDebugger.addDebugButton();
      }
    }, 5000);
    
    return () => {
      clearTimeout(repairTimeout);
      clearTimeout(safetyTimeout);
    };
  }, [authLoading, isLoading, user, profile]);

  const handleEditDocument = (document) => {
    navigate('/document-creator', { state: { documentId: document.id } });
  };

  const handleEbookOptionClick = (action) => {
    switch (action) {
      case 'start-from-scratch':
        navigate('/document-creator');
        break;
      case 'import-from-url':
        // Navigate to document creator with URL import pre-selected
        navigate('/document-creator', {
          state: {
            preselectedBaseline: 'import-url'
          }
        });
        break;
      case 'import-from-docx':
        // Navigate to document creator with DOCX import pre-selected
        navigate('/document-creator', {
          state: {
            preselectedBaseline: 'import-docx'
          }
        });
        break;
      case 'import-from-pdf':
        // Navigate to document creator with PDF import pre-selected
        navigate('/document-creator', {
          state: {
            preselectedBaseline: 'import-pdf'
          }
        });
        break;
      case 'start-from-template':
        // Navigate to Template Library for template selection
        navigate('/template-library');
        break;
      default:
        // For now, other buttons don't have functionality
        console.log(`${action} functionality not yet implemented`);
        break;
    }
  };

  const handleDuplicateDocument = (document) => {
    console.log('Duplicating document:', document.title);
    // Implement duplication logic
  };

  const handleShareDocument = (document) => {
    console.log('Sharing document:', document.title);
    // Implement sharing logic (email, link sharing, etc.)
  };

  const handleDeleteDocument = (document) => {
    console.log('Deleting document:', document.title);
    // Implement deletion logic with confirmation
  };



  // Add effect to show debug button after a delay
  useEffect(() => {
    if (isLoading || authLoading) {
      // If still loading after 3 seconds, show debug button
      const debugTimer = setTimeout(() => {
        if (document.querySelector('.loading-dashboard')) {
          console.warn('⚠️ Dashboard still loading after 3 seconds - adding debug button');
          sessionDebugger.addDebugButton();
        }
      }, 3000);
      
      return () => clearTimeout(debugTimer);
    }
  }, [isLoading, authLoading]);

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-background loading-dashboard">
        <QuickActionSidebar />
        <Header />
        <main className="lg:ml-64 ml-0 pt-16">
          <div className="px-6 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-text-secondary">Loading your dashboard...</p>
                <button 
                  onClick={() => sessionRecovery.recoverSession()}
                  className="mt-8 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                >
                  Reset Session & Reload
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      <Header />

      <main className={`${contentMargin} ml-0 pt-16 sidebar-layout`}>
        <div className="px-6 py-8">

          {/* Welcome Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl lg:text-3xl font-bold text-text-primary mb-2">
                  Welcome back, {profile?.full_name || user?.email?.split('@')[0] || 'User'}! 👋
                </h1>
                <p className="text-text-secondary">
                  {profile?.user_type ? (
                    <>Ready to create amazing documents as a {profile.user_type.replace('_', ' ')}?</>
                  ) : (
                    'Ready to create amazing documents with AI?'
                  )}
                </p>
              </div>
              <div className="hidden md:flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-sm text-text-secondary">Current Plan</p>
                  <p className="font-semibold text-text-primary capitalize">
                    {profile?.subscription_tier || 'Free'}
                  </p>
                </div>
                {profile?.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.full_name || user?.email}
                    className="w-12 h-12 rounded-full object-cover border-2 border-border"
                  />
                ) : (
                  <div className="w-12 h-12 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center border-2 border-border">
                    <span className="text-white font-semibold">
                      {(profile?.full_name || user?.email || 'U').charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Quick Action Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* New eBook Card */}
            <div className="bg-surface rounded-lg border border-border p-6 hover:shadow-elevated transition-all duration-300 cursor-pointer group">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-light rounded-md flex items-center justify-center">
                    <Icon name="BookOpen" size={20} className="text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary group-hover:text-primary transition-colors">New eBook</h3>
                    <p className="text-sm text-text-secondary">Start writing on a blank page or import</p>
                  </div>
                </div>
                <Icon name="ArrowRight" size={16} className="text-text-muted group-hover:text-primary transition-colors" />
              </div>
            </div>

            {/* New Audiobook Card */}
            {/* <div className="bg-surface rounded-lg border border-border p-6 hover:shadow-elevated transition-all duration-300 cursor-pointer group">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-accent-light rounded-md flex items-center justify-center">
                    <Icon name="Volume2" size={20} className="text-accent" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary group-hover:text-accent transition-colors">New Audiobook</h3>
                    <p className="text-sm text-text-secondary">Start writing on a blank page or import</p>
                  </div>
                </div>
                <Icon name="ArrowRight" size={16} className="text-text-muted group-hover:text-accent transition-colors" />
              </div>
              <div className="flex items-center space-x-2 text-xs">
                <span className="bg-primary text-white px-2 py-1 rounded">Drag and drop or</span>
                <button className="text-primary hover:underline">browse to import</button>
                <span className="text-text-muted">pdf, docx, .txt</span>
              </div>
            </div> */}
          </div>

          {/* Hero Section - Matching reference design */}
          <div className="mb-12">
            <div className="bg-gradient-to-r from-hero-start via-hero-middle to-hero-end rounded-xl p-8 lg:p-12 text-white relative overflow-hidden">
              <div className="relative z-10 flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-4">
                    <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                      ✨ AI-Powered
                    </span>
                  </div>
                  <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                    {profile?.documents_created > 0
                      ? `Continue your document journey`
                      : `Meet your AI-powered document creator`
                    }
                  </h1>
                  <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                    DocForge AI
                  </h2>
                  <button
                    onClick={() => navigate('/document-creator')}
                    className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-md font-semibold transition-all duration-300 transform hover:-translate-y-1 shadow-elevated"
                  >
                    Try it now →
                  </button>
                </div>

                {/* Hero Image/Illustration */}
                <div className="hidden lg:block flex-shrink-0 ml-8">
                  <div className="w-64 h-48 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Icon name="Sparkles" size={48} className="mx-auto mb-4 text-white/80" />
                      <p className="text-sm text-white/70">Generate text with DocForge</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            </div>
          </div>

          {/* More options to create an eBook - Matching reference design */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-text-primary mb-6">More options to create an eBook</h3>

            <div className="flex space-x-4 overflow-x-auto pb-4">
              {[
                {
                  icon: 'FileText',
                  label: 'Start from scratch',
                  description: '',
                  color: 'bg-blue-50 border-blue-200',
                  iconColor: 'text-blue-500',
                  action: 'start-from-scratch'
                },
                {
                  icon: 'Link',
                  label: 'Import from blog post or URL',
                  description: '',
                  color: 'bg-purple-50 border-purple-200',
                  iconColor: 'text-purple-500',
                  action: 'import-from-url'
                },
                {
                  icon: 'FileText',
                  label: 'Start from template',
                  description: '',
                  color: 'bg-green-50 border-green-200',
                  iconColor: 'text-green-500',
                  action: 'start-from-template'
                },
                {
                  icon: 'FileText',
                  label: 'Import from DOCX',
                  description: '',
                  color: 'bg-orange-50 border-orange-200',
                  iconColor: 'text-orange-500',
                  action: 'import-from-docx'
                },
                {
                  icon: 'FileText',
                  label: 'Import from PDF',
                  description: '',
                  color: 'bg-blue-50 border-blue-200',
                  iconColor: 'text-blue-500',
                  action: 'import-from-pdf'
                },
                {
                  icon: 'FileText',
                  label: 'Import from...',
                  description: '',
                  color: 'bg-gray-50 border-gray-200',
                  iconColor: 'text-gray-500',
                  action: 'import-from-other'
                }
              ].map((option, index) => (
                <div
                  key={index}
                  className={`flex-shrink-0 w-32 h-32 ${option.color} border-2 border-dashed rounded-lg flex flex-col items-center justify-center p-4 cursor-pointer hover:shadow-md transition-all duration-300 group`}
                  onClick={() => handleEbookOptionClick(option.action)}
                >
                  <Icon name={option.icon} size={24} className={`${option.iconColor} mb-2 group-hover:scale-110 transition-transform`} />
                  <p className="text-xs text-center font-medium text-text-primary leading-tight">{option.label}</p>
                  {option.description && (
                    <p className="text-xs text-center text-text-muted mt-1">{option.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recent Projects - Using real data */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-text-primary mb-6">Recent projects</h3>

            {isLoadingRecent ? (
              <ProjectsLoading count={4} compact={true} />
            ) : recentError ? (
              <ProjectsError error={recentError} onRetry={refetchRecent} compact={true} />
            ) : recentProjects.length === 0 ? (
              <div className="text-center py-12 bg-surface rounded-lg border border-border">
                <div className="text-text-secondary mb-4">
                  <Icon name="FileText" size={48} className="mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">No recent projects</p>
                  <p className="text-sm">Create your first project to get started</p>
                </div>
                <Button onClick={() => navigate('/document-creator')} variant="primary">
                  Create Project
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {recentProjects.map((project) => (
                  <ProjectCard
                    key={project.id}
                    project={project}
                    onClick={handleEditDocument}
                    showActions={false}  // Hide action buttons on dashboard
                    compact={true}       // Use compact mode
                  />
                ))}
              </div>
            )}
          </div>


        </div>
      </main>

      {/* Chat Widget */}
      <ChatWidget />
    </div>
  );
};

export default Dashboard;