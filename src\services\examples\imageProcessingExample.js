/**
 * Example usage of the image processing functionality
 * Demonstrates HTML and markdown image extraction, download, and processing
 */

import {
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    extractAndProcessImages,
    processImages,
    validateImageUrl,
    downloadImage
} from '../contentProcessingService.js';

/**
 * Example: Extract images from HTML content (TipTap editor output)
 */
export const htmlImageExtractionExample = () => {
    const htmlContent = `
        <h1>My Document</h1>
        <p>Here's an image from the editor:</p>
        <img src="https://picsum.photos/600/400" alt="Sample Image" class="medium" />
        <p>And another one:</p>
        <img src="https://picsum.photos/800/600" alt="Another Image" width="400" height="300" />
    `;

    const images = extractImagesFromHTML(htmlContent);
    console.log('Extracted HTML images:', images);

    return images;
};

/**
 * Example: Extract images from markdown content (legacy content)
 */
export const markdownImageExtractionExample = () => {
    const markdownContent = `
        # My Document
        
        Here's an image: ![Sample Image](https://picsum.photos/600/400)
        
        And another: ![Another Image](https://picsum.photos/800/600)
        
        Some text without images.
    `;

    const images = extractImagesFromMarkdown(markdownContent);
    console.log('Extracted markdown images:', images);

    return images;
};

/**
 * Example: Download a single image with error handling and retry logic
 */
export const singleImageDownloadExample = async () => {
    const imageUrl = 'https://picsum.photos/600/400';

    console.log(`Downloading image: ${imageUrl}`);

    const result = await downloadImage(imageUrl);

    if (result.success) {
        console.log('Image downloaded successfully:', {
            url: result.url,
            contentType: result.contentType,
            size: result.size,
            extension: result.extension
        });
    } else {
        console.error('Image download failed:', result.error);
    }

    return result;
};

/**
 * Example: Process multiple images with concurrent downloads
 */
export const multipleImageProcessingExample = async () => {
    const images = [
        { src: 'https://picsum.photos/600/400', alt: 'Image 1', index: 0 },
        { src: 'https://picsum.photos/800/600', alt: 'Image 2', index: 1 },
        { src: 'https://picsum.photos/400/300', alt: 'Image 3', index: 2 }
    ];

    console.log('Processing multiple images...');

    const result = await processImages(images, 2); // Concurrency of 2

    console.log('Processing result:', {
        success: result.success,
        totalImages: result.totalImages,
        successCount: result.successCount,
        failureCount: result.failureCount
    });

    if (result.processedImages.length > 0) {
        console.log('Successfully processed images:');
        result.processedImages.forEach(img => {
            console.log(`- ${img.alt}: ${img.size} bytes (${img.contentType})`);
        });
    }

    if (result.failedImages.length > 0) {
        console.log('Failed images:');
        result.failedImages.forEach(img => {
            console.log(`- ${img.alt}: ${img.error}`);
        });
    }

    return result;
};

/**
 * Example: Complete image extraction and processing pipeline
 */
export const completeImageProcessingExample = async () => {
    const htmlContent = `
        <h1>Document with Images</h1>
        <p>Here are some images:</p>
        <img src="https://picsum.photos/600/400" alt="First Image" />
        <img src="https://picsum.photos/800/600" alt="Second Image" />
        <img src="https://invalid-url.com/nonexistent.jpg" alt="Broken Image" />
    `;

    console.log('Running complete image processing pipeline...');

    const result = await extractAndProcessImages(htmlContent, 'html');

    console.log('Complete processing result:', {
        success: result.success,
        contentType: result.contentType,
        totalImages: result.totalImages,
        successCount: result.successCount,
        failureCount: result.failureCount
    });

    return result;
};

/**
 * Example: URL validation
 */
export const urlValidationExample = () => {
    const urls = [
        'https://picsum.photos/600/400',
        'http://example.com/image.jpg',
        'invalid-url',
        'ftp://example.com/image.jpg',
        '  https://example.com/image.png  ',
        ''
    ];

    console.log('URL validation examples:');

    urls.forEach(url => {
        const result = validateImageUrl(url);
        console.log(`${url} -> ${result.isValid ? 'VALID' : 'INVALID'}${result.error ? ': ' + result.error : ''}`);
    });
};

/**
 * Example: Error handling scenarios
 */
export const errorHandlingExample = async () => {
    console.log('Testing error handling scenarios...');

    // Test invalid URL
    const invalidUrlResult = await downloadImage('not-a-url');
    console.log('Invalid URL result:', invalidUrlResult.success ? 'SUCCESS' : 'FAILED - ' + invalidUrlResult.error);

    // Test non-existent image
    const notFoundResult = await downloadImage('https://httpstat.us/404');
    console.log('404 URL result:', notFoundResult.success ? 'SUCCESS' : 'FAILED - ' + notFoundResult.error);

    // Test timeout (this will take a while due to retries)
    console.log('Testing timeout scenario (this may take a while due to retries)...');
    const timeoutResult = await downloadImage('https://httpstat.us/200?sleep=15000');
    console.log('Timeout result:', timeoutResult.success ? 'SUCCESS' : 'FAILED - ' + timeoutResult.error);
};

/**
 * Run all examples
 */
export const runAllExamples = async () => {
    console.log('=== Image Processing Examples ===\n');

    console.log('1. HTML Image Extraction:');
    htmlImageExtractionExample();
    console.log('');

    console.log('2. Markdown Image Extraction:');
    markdownImageExtractionExample();
    console.log('');

    console.log('3. URL Validation:');
    urlValidationExample();
    console.log('');

    console.log('4. Single Image Download:');
    await singleImageDownloadExample();
    console.log('');

    console.log('5. Multiple Image Processing:');
    await multipleImageProcessingExample();
    console.log('');

    console.log('6. Complete Processing Pipeline:');
    await completeImageProcessingExample();
    console.log('');

    console.log('7. Error Handling Examples:');
    await errorHandlingExample();

    console.log('\n=== Examples Complete ===');
};

// Export all examples
export default {
    htmlImageExtractionExample,
    markdownImageExtractionExample,
    singleImageDownloadExample,
    multipleImageProcessingExample,
    completeImageProcessingExample,
    urlValidationExample,
    errorHandlingExample,
    runAllExamples
};