import mammoth from 'mammoth';

/**
 * DOCX Content Extraction Service
 * Handles extraction of content, structure, and metadata from DOCX files
 */

/**
 * Test mammoth library availability and basic functionality
 * @returns {Object} Test result
 */
export const testMammothLibrary = () => {
  try {
    if (!mammoth) {
      return { success: false, error: 'Mammoth library not found' };
    }

    if (!mammoth.extractRawText) {
      return { success: false, error: 'extractRawText method not available' };
    }

    if (!mammoth.convertToHtml) {
      return { success: false, error: 'convertToHtml method not available' };
    }

    return { success: true, message: 'Mammoth library is available and functional' };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Extract content from a DOCX file
 * @param {File} file - The DOCX file to extract content from
 * @returns {Promise<Object>} Extraction result with success status and data
 */
export const extractContentFromDocx = async (file) => {
  console.log('Starting DOCX extraction for file:', file?.name, 'Size:', file?.size);

  try {
    // Validate file exists
    if (!file) {
      throw new Error('No file provided for extraction.');
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.docx')) {
      throw new Error('Invalid file type. Only .docx files are supported.');
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error('File size too large. Maximum size is 10MB.');
    }

    // Validate file is not empty
    if (file.size === 0) {
      throw new Error('File is empty. Please select a valid DOCX file.');
    }

    console.log('File validation passed, converting to array buffer...');

    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();
    console.log('Array buffer created, size:', arrayBuffer.byteLength);

    // Check if mammoth is available
    if (!mammoth) {
      throw new Error('Mammoth library not available. Please check the installation.');
    }

    console.log('Extracting content with mammoth...');

    // Extract content using mammoth with proper options format
    const result = await mammoth.extractRawText({ arrayBuffer: arrayBuffer });
    const htmlResult = await mammoth.convertToHtml({ arrayBuffer: arrayBuffer });

    console.log('Mammoth extraction completed. Text length:', result.value?.length, 'HTML length:', htmlResult.value?.length);

    // Check if extraction returned content
    if (!result.value && !htmlResult.value) {
      throw new Error('No content could be extracted from the DOCX file. The file may be corrupted or empty.');
    }

    // Parse the extracted content
    const extractedData = parseDocxContent(result.value, htmlResult.value, file);
    console.log('Content parsing completed:', extractedData);

    return {
      success: true,
      data: {
        fileName: file.name,
        fileSize: file.size,
        extractedContent: extractedData.content,
        originalTitle: extractedData.title,
        structure: extractedData.structure,
        wordCount: extractedData.wordCount,
        extractedAt: new Date().toISOString(),
        extractionStatus: 'success',
        extractionError: '',
        // Additional metadata
        headings: extractedData.headings,
        paragraphs: extractedData.paragraphs,
        documentType: inferDocumentType(extractedData),
        // Note: No sourceUrl for file uploads - this distinguishes from URL imports
        sourceUrl: null
      }
    };

  } catch (error) {
    console.error('DOCX extraction error:', error);
    console.error('Error stack:', error.stack);
    console.error('File details:', {
      name: file?.name,
      size: file?.size,
      type: file?.type,
      lastModified: file?.lastModified
    });

    // Determine more specific error message
    let errorMessage = error.message || 'Failed to extract content from DOCX file';

    // Check for specific error types
    if (error.message?.includes('arrayBuffer')) {
      errorMessage = 'Failed to read the DOCX file. The file may be corrupted.';
    } else if (error.message?.includes('mammoth')) {
      errorMessage = 'Failed to process the DOCX file. Please ensure it\'s a valid Microsoft Word document.';
    } else if (error.name === 'TypeError') {
      errorMessage = 'Invalid file format. Please select a valid .docx file.';
    }

    return {
      success: false,
      data: {
        fileName: file?.name || 'Unknown',
        fileSize: file?.size || 0,
        extractionError: errorMessage,
        extractionStatus: 'error',
        extractedAt: new Date().toISOString(),
        // Additional debugging info
        debugInfo: {
          originalError: error.message,
          errorType: error.name,
          stack: error.stack?.split('\n').slice(0, 3).join('\n') // First 3 lines of stack
        }
      }
    };
  }
};

/**
 * Parse extracted DOCX content and structure
 * @param {string} rawText - Raw text content from mammoth
 * @param {string} htmlContent - HTML content from mammoth
 * @param {File} file - Original file object
 * @returns {Object} Parsed content structure
 */
const parseDocxContent = (rawText, htmlContent, file) => {
  try {
    console.log('Parsing DOCX content. Raw text length:', rawText?.length, 'HTML length:', htmlContent?.length);

    // Handle empty content
    if (!rawText && !htmlContent) {
      throw new Error('No content to parse from DOCX file');
    }

    // Clean up the raw text
    const cleanText = (rawText || '').replace(/\r\n/g, '\n').replace(/\r/g, '\n').trim();
    console.log('Cleaned text length:', cleanText.length);

    // Extract title from filename or first heading
    const title = extractTitle(cleanText, file.name);
    console.log('Extracted title:', title);

    // Parse HTML to extract structure
    const structure = parseHtmlStructure(htmlContent || '');
    console.log('Parsed structure:', structure);

    // Split into paragraphs
    const paragraphs = cleanText
      .split('\n\n')
      .map(p => p.trim())
      .filter(p => p.length > 0);

    // Extract headings from structure
    const headings = structure.headings;

    // Calculate word count
    const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;
    console.log('Word count:', wordCount);

    const result = {
      content: cleanText,
      title,
      structure,
      headings,
      paragraphs,
      wordCount
    };

    console.log('Parse completed successfully:', result);
    return result;

  } catch (error) {
    console.error('Error parsing DOCX content:', error);
    // Return minimal structure on parse error
    return {
      content: rawText || '',
      title: file?.name?.replace(/\.[^/.]+$/, '') || 'Untitled',
      structure: { headings: [], paragraphs: [], hasStructure: false },
      headings: [],
      paragraphs: [],
      wordCount: 0
    };
  }
};

/**
 * Parse HTML structure to extract headings and formatting
 * @param {string} htmlContent - HTML content from mammoth
 * @returns {Object} Document structure information
 */
const parseHtmlStructure = (htmlContent) => {
  try {
    console.log('Parsing HTML structure...');

    // Handle empty HTML content
    if (!htmlContent) {
      console.log('No HTML content to parse');
      return {
        headings: [],
        paragraphs: [],
        hasStructure: false
      };
    }

    // Create a temporary DOM element to parse HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
  
  // Extract headings
  const headings = [];
  const headingElements = doc.querySelectorAll('h1, h2, h3, h4, h5, h6');
  
  headingElements.forEach((heading, index) => {
    headings.push({
      level: parseInt(heading.tagName.charAt(1)),
      text: heading.textContent.trim(),
      index: index
    });
  });

  // Extract paragraphs with basic formatting
  const paragraphElements = doc.querySelectorAll('p');
  const formattedParagraphs = [];
  
  paragraphElements.forEach((p, index) => {
    const text = p.textContent.trim();
    if (text.length > 0) {
      formattedParagraphs.push({
        text,
        index,
        hasFormatting: p.innerHTML !== p.textContent // Basic check for formatting
      });
    }
  });

  const result = {
    headings,
    paragraphs: formattedParagraphs,
    hasStructure: headings.length > 0
  };

  console.log('HTML structure parsed:', result);
  return result;

  } catch (error) {
    console.error('Error parsing HTML structure:', error);
    return {
      headings: [],
      paragraphs: [],
      hasStructure: false
    };
  }
};

/**
 * Extract document title from content or filename
 * @param {string} content - Document content
 * @param {string} filename - Original filename
 * @returns {string} Extracted or inferred title
 */
const extractTitle = (content, filename) => {
  // Try to find title from first line or heading
  const lines = content.split('\n').filter(line => line.trim().length > 0);
  
  if (lines.length > 0) {
    const firstLine = lines[0].trim();
    
    // If first line is short and looks like a title
    if (firstLine.length < 100 && firstLine.length > 3) {
      // Check if it doesn't end with punctuation (except ?)
      if (!/[.!,;:]$/.test(firstLine) || /\?$/.test(firstLine)) {
        return firstLine;
      }
    }
  }
  
  // Fallback to filename without extension
  return filename.replace(/\.[^/.]+$/, '').replace(/[-_]/g, ' ');
};

/**
 * Infer document type based on content structure and patterns
 * @param {Object} extractedData - Parsed document data
 * @returns {string} Inferred document type
 */
const inferDocumentType = (extractedData) => {
  const { content, headings, wordCount } = extractedData;
  const contentLower = content.toLowerCase();

  // Check for academic paper patterns
  if (contentLower.includes('abstract') ||
      contentLower.includes('introduction') ||
      contentLower.includes('methodology') ||
      contentLower.includes('conclusion') ||
      contentLower.includes('references')) {
    return 'academic'; // Fixed: return 'academic' instead of 'academic-paper'
  }
  
  // Check for business report patterns
  if (contentLower.includes('executive summary') ||
      contentLower.includes('recommendations') ||
      contentLower.includes('analysis') ||
      contentLower.includes('findings')) {
    return 'business'; // Fixed: return 'business' instead of 'business-report'
  }
  
  // Check for technical/business document patterns
  if (contentLower.includes('whitepaper') ||
      contentLower.includes('technical report') ||
      contentLower.includes('position paper') ||
      contentLower.includes('report') ||
      contentLower.includes('survey') ||
      contentLower.includes('study results') ||
      contentLower.includes('data analysis') ||
      (contentLower.includes('solution') && contentLower.includes('technology'))) {
    return 'business';
  }

  // Check for instructional content patterns
  if (contentLower.includes('manual') ||
      contentLower.includes('instruction') ||
      contentLower.includes('user guide') ||
      contentLower.includes('documentation')) {
    return 'guide';
  }

  // Check for guide/manual patterns
  if (contentLower.includes('step') ||
      contentLower.includes('how to') ||
      contentLower.includes('tutorial') ||
      headings.length > 5) {
    return 'guide';
  }

  // Check for creative writing patterns
  if (contentLower.includes('chapter') ||
      wordCount > 5000) {
    return 'ebook';
  }

  // Default fallback
  return 'ebook';
};

/**
 * Validate DOCX file before processing
 * @param {File} file - File to validate
 * @returns {Object} Validation result
 */
export const validateDocxFile = (file) => {
  console.log('Validating DOCX file:', file?.name);

  const errors = [];
  const warnings = [];
  const info = [];

  if (!file) {
    errors.push('No file provided for validation.');
    return { isValid: false, errors, warnings, info };
  }

  // Check file type
  if (!file.name.toLowerCase().endsWith('.docx')) {
    errors.push('Invalid file type. Only .docx files are supported.');
    info.push(`File extension: ${file.name.split('.').pop()}`);
  }

  // Check MIME type if available
  if (file.type && !file.type.includes('wordprocessingml')) {
    warnings.push(`Unexpected MIME type: ${file.type}. Expected: application/vnd.openxmlformats-officedocument.wordprocessingml.document`);
  }

  // Check file size
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    errors.push('File size too large. Maximum size is 10MB.');
  }

  // Check if file is too small (might be corrupted)
  if (file.size < 1024) { // Less than 1KB
    warnings.push('File seems very small. Please ensure it contains content.');
  }

  // Check if file is empty
  if (file.size === 0) {
    errors.push('File is empty. Please select a valid DOCX file.');
  }

  // Add file information
  info.push(`File size: ${(file.size / 1024).toFixed(2)} KB`);
  info.push(`Last modified: ${new Date(file.lastModified).toLocaleString()}`);

  const result = {
    isValid: errors.length === 0,
    errors,
    warnings,
    info
  };

  console.log('Validation result:', result);
  return result;
};

/**
 * Get content preview for display
 * @param {Object} extractedData - Extracted content data
 * @param {number} maxLength - Maximum preview length
 * @returns {Object} Preview data
 */
export const getDocxContentPreview = (extractedData, maxLength = 500) => {
  const { content, title, headings, wordCount } = extractedData;
  
  // Create preview text
  let preview = content;
  if (content.length > maxLength) {
    preview = content.substring(0, maxLength) + '...';
  }
  
  // Create structure summary
  const structureSummary = {
    totalHeadings: headings.length,
    topLevelHeadings: headings.filter(h => h.level === 1).length,
    hasStructure: headings.length > 0
  };
  
  return {
    title,
    preview,
    wordCount,
    structure: structureSummary,
    headings: headings.slice(0, 5) // First 5 headings for preview
  };
};
