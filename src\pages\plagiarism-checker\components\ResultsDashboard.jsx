import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ResultsDashboard = ({ results, onExportReport, onStartNewCheck }) => {
  const [activeTab, setActiveTab] = useState('plagiarism');

  const getSimilarityColor = (percentage) => {
    if (percentage < 15) return 'text-success';
    if (percentage < 30) return 'text-warning';
    return 'text-error';
  };

  const getSimilarityBgColor = (percentage) => {
    if (percentage < 15) return 'bg-success/10 border-success/20';
    if (percentage < 30) return 'bg-warning/10 border-warning/20';
    return 'bg-error/10 border-error/20';
  };

  const getRiskLevel = (percentage) => {
    if (percentage < 15) return { label: 'Low Risk', icon: 'CheckCircle' };
    if (percentage < 30) return { label: 'Medium Risk', icon: 'AlertTriangle' };
    return { label: 'High Risk', icon: 'AlertCircle' };
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-text-primary mb-1">
              Plagiarism Check Results
            </h2>
            <p className="text-text-secondary">
              Completed on {formatDate(results.completedAt)}
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onStartNewCheck}>
              <Icon name="Plus" size={16} className="mr-2" />
              New Check
            </Button>
            <Button variant="primary" onClick={onExportReport}>
              <Icon name="Download" size={16} className="mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Document Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-background rounded-lg">
          <div className="flex items-center space-x-3">
            <Icon name="FileText" size={20} className="text-text-secondary" />
            <div>
              <p className="text-sm font-medium text-text-primary">{results.documentName}</p>
              <p className="text-xs text-text-secondary">{results.documentSize}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Icon name="Type" size={20} className="text-text-secondary" />
            <div>
              <p className="text-sm font-medium text-text-primary">
                {results.wordCount.toLocaleString()} words
              </p>
              <p className="text-xs text-text-secondary">
                {results.characterCount.toLocaleString()} characters
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Icon name="Clock" size={20} className="text-text-secondary" />
            <div>
              <p className="text-sm font-medium text-text-primary">
                {results.processingTime}
              </p>
              <p className="text-xs text-text-secondary">Processing time</p>
            </div>
          </div>
        </div>
      </div>

      {/* Results Tabs */}
      <div className="bg-surface rounded-lg border border-border">
        <div className="flex border-b border-border">
          <button
            onClick={() => setActiveTab('plagiarism')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-micro ${
              activeTab === 'plagiarism' ?'text-primary border-b-2 border-primary bg-primary/5' :'text-text-secondary hover:text-text-primary'
            }`}
          >
            <Icon name="Search" size={16} className="inline mr-2" />
            Plagiarism Results
          </button>
          <button
            onClick={() => setActiveTab('ai-detection')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-micro ${
              activeTab === 'ai-detection' ?'text-primary border-b-2 border-primary bg-primary/5' :'text-text-secondary hover:text-text-primary'
            }`}
          >
            <Icon name="Bot" size={16} className="inline mr-2" />
            AI Content Detection
          </button>
        </div>

        <div className="p-6">
          {activeTab === 'plagiarism' && (
            <div>
              {/* Similarity Score */}
              <div className="text-center mb-8">
                <div className={`inline-flex items-center justify-center w-32 h-32 rounded-full border-4 ${getSimilarityBgColor(results.plagiarism.similarityPercentage)}`}>
                  <div className="text-center">
                    <div className={`text-3xl font-bold ${getSimilarityColor(results.plagiarism.similarityPercentage)}`}>
                      {results.plagiarism.similarityPercentage}%
                    </div>
                    <div className="text-xs text-text-secondary">Similar</div>
                  </div>
                </div>
                <div className="mt-4">
                  <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full ${getSimilarityBgColor(results.plagiarism.similarityPercentage)}`}>
                    <Icon name={getRiskLevel(results.plagiarism.similarityPercentage).icon} size={16} />
                    <span className="text-sm font-medium">
                      {getRiskLevel(results.plagiarism.similarityPercentage).label}
                    </span>
                  </div>
                </div>
              </div>

              {/* Source Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-background rounded-lg">
                  <Icon name="Globe" size={24} className="mx-auto text-text-secondary mb-2" />
                  <div className="text-2xl font-bold text-text-primary">
                    {results.plagiarism.webSources}
                  </div>
                  <div className="text-sm text-text-secondary">Web Sources</div>
                </div>
                <div className="text-center p-4 bg-background rounded-lg">
                  <Icon name="GraduationCap" size={24} className="mx-auto text-text-secondary mb-2" />
                  <div className="text-2xl font-bold text-text-primary">
                    {results.plagiarism.academicSources}
                  </div>
                  <div className="text-sm text-text-secondary">Academic Sources</div>
                </div>
                <div className="text-center p-4 bg-background rounded-lg">
                  <Icon name="FileText" size={24} className="mx-auto text-text-secondary mb-2" />
                  <div className="text-2xl font-bold text-text-primary">
                    {results.plagiarism.documentSources}
                  </div>
                  <div className="text-sm text-text-secondary">Document Sources</div>
                </div>
              </div>

              {/* Top Sources */}
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-4">Top Matching Sources</h3>
                <div className="space-y-3">
                  {results.plagiarism.topSources.map((source, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-background rounded-lg">
                      <div className="flex items-center space-x-3 flex-1">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${getSimilarityBgColor(source.similarity)}`}>
                          {source.similarity}%
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-text-primary truncate">
                            {source.title}
                          </p>
                          <p className="text-xs text-text-secondary truncate">
                            {source.url}
                          </p>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Icon name="ExternalLink" size={14} />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'ai-detection' && (
            <div>
              {/* AI Detection Score */}
              <div className="text-center mb-8">
                <div className={`inline-flex items-center justify-center w-32 h-32 rounded-full border-4 ${
                  results.aiDetection.confidence > 70 ? 'bg-error/10 border-error/20' : 
                  results.aiDetection.confidence > 40 ? 'bg-warning/10 border-warning/20': 'bg-success/10 border-success/20'
                }`}>
                  <div className="text-center">
                    <div className={`text-3xl font-bold ${
                      results.aiDetection.confidence > 70 ? 'text-error' : 
                      results.aiDetection.confidence > 40 ? 'text-warning': 'text-success'
                    }`}>
                      {results.aiDetection.confidence}%
                    </div>
                    <div className="text-xs text-text-secondary">AI Generated</div>
                  </div>
                </div>
                <div className="mt-4">
                  <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full ${
                    results.aiDetection.confidence > 70 ? 'bg-error/10 border-error/20' : 
                    results.aiDetection.confidence > 40 ? 'bg-warning/10 border-warning/20': 'bg-success/10 border-success/20'
                  }`}>
                    <Icon name="Bot" size={16} />
                    <span className="text-sm font-medium">
                      {results.aiDetection.confidence > 70 ? 'Likely AI Generated' : 
                       results.aiDetection.confidence > 40 ? 'Possibly AI Generated': 'Likely Human Written'}
                    </span>
                  </div>
                </div>
              </div>

              {/* AI Analysis Breakdown */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-text-primary">Analysis Breakdown</h3>
                
                {results.aiDetection.analysis.map((item, index) => (
                  <div key={index} className="p-4 bg-background rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-text-primary">{item.metric}</span>
                      <span className={`text-sm font-bold ${
                        item.score > 70 ? 'text-error' : 
                        item.score > 40 ? 'text-warning': 'text-success'
                      }`}>
                        {item.score}%
                      </span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          item.score > 70 ? 'bg-error' : 
                          item.score > 40 ? 'bg-warning': 'bg-success'
                        }`}
                        style={{ width: `${item.score}%` }}
                      />
                    </div>
                    <p className="text-xs text-text-secondary mt-2">{item.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResultsDashboard;