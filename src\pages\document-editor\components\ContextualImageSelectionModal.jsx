import React, { useState, useEffect } from 'react';
import Button from '../../../components/ui/Button';

/**
 * ContextualImageSelectionModal - Modal for selecting images at specific placement locations
 * 
 * This modal:
 * - Shows 3 AI-suggested images relevant to the specific chapter/section
 * - Displays contextual information about the placement location
 * - Allows user to select one image and insert it at the determined location
 * - Provides preview and metadata for each suggested image
 */
const ContextualImageSelectionModal = ({
  isOpen,
  onClose,
  onImageSelect,
  chapterId,
  placementId,
  imageSuggestions = {},
  placement = null
}) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedImage(null);
      setIsLoading(false);
    }
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  // Get images for the specific chapter
  const chapterSuggestions = imageSuggestions[chapterId];
  const availableImages = chapterSuggestions?.images || [];

  const handleImageSelect = (image) => {
    setSelectedImage(image);
  };

  const handleConfirmSelection = async () => {
    if (!selectedImage) return;

    setIsLoading(true);
    try {
      // Call the parent handler with image and placement context
      await onImageSelect(selectedImage, placement, chapterId, placementId);
      onClose();
    } catch (error) {
      console.error('Error inserting image:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setSelectedImage(null);
    onClose();
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCancel();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Select Image for {chapterSuggestions?.chapterTitle || 'Chapter'}
            </h2>
            {placement && (
              <p className="text-sm text-gray-600 mt-1">
                {placement.description} • {placement.contextualHint}
              </p>
            )}
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {availableImages.length > 0 ? (
            <>
              <h3 className="text-sm font-medium text-gray-900 mb-4">
                AI-Suggested Images ({availableImages.length} available)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {availableImages.map((image) => (
                  <div
                    key={image.id}
                    className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                      selectedImage?.id === image.id
                        ? 'border-blue-500 shadow-lg ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                    }`}
                    onClick={() => handleImageSelect(image)}
                  >
                    <div className="aspect-video">
                      <img
                        src={image.thumbnailUrl || image.url}
                        alt={image.description}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* Image Info Overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3">
                      <p className="text-white text-sm font-medium truncate">
                        {image.description}
                      </p>
                      <p className="text-gray-300 text-xs">
                        by {image.photographer}
                      </p>
                    </div>

                    {/* Selection Indicator */}
                    {selectedImage?.id === image.id && (
                      <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}

                    {/* Hover Effect */}
                    <div className="absolute inset-0 bg-blue-500 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200" />
                  </div>
                ))}
              </div>

              {/* Selected Image Preview */}
              {selectedImage && (
                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Selected Image</h4>
                  <div className="flex items-start space-x-4">
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                      <img
                        src={selectedImage.thumbnailUrl || selectedImage.url}
                        alt={selectedImage.description}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {selectedImage.description}
                      </p>
                      <p className="text-sm text-gray-600">
                        by {selectedImage.photographer}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        This image will be inserted {placement?.position || 'at the selected location'}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Images Available</h3>
              <p className="text-gray-600">
                No AI-suggested images found for this chapter. Please try again later.
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <Button
            variant="secondary"
            onClick={handleCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSelection}
            disabled={!selectedImage || isLoading}
            isLoading={isLoading}
          >
            {isLoading ? 'Inserting...' : 'Insert Image'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ContextualImageSelectionModal;
