import React, { useState, useEffect } from 'react';
import Icon from '../AppIcon';
import { validateUrl } from '../../services/urlExtractionService';

/**
 * Enhanced URL Input Component with real-time validation
 * Provides visual feedback, suggestions, and validation states
 */
const URLInput = ({ 
  value, 
  onChange, 
  onValidationChange,
  placeholder = "https://example.com/blog-post",
  disabled = false,
  className = ""
}) => {
  const [validation, setValidation] = useState({ isValid: false });
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestion, setShowSuggestion] = useState(false);

  // Validate URL on value change
  useEffect(() => {
    if (value) {
      const result = validateUrl(value);
      setValidation(result);
      onValidationChange?.(result);
    } else {
      setValidation({ isValid: false });
      onValidationChange?.({ isValid: false });
    }
  }, [value]);

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  const handleFocus = () => {
    setIsFocused(true);
    setShowSuggestion(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Hide suggestion after a delay to allow clicking on it
    setTimeout(() => setShowSuggestion(false), 200);
  };

  const applySuggestion = () => {
    if (validation.suggestion && validation.suggestion.startsWith('Try: ')) {
      const suggestedUrl = validation.suggestion.replace('Try: ', '');
      onChange(suggestedUrl);
    }
  };

  const getInputClasses = () => {
    let classes = `w-full px-4 py-3 border rounded-lg transition-all duration-200 ${className}`;
    
    if (disabled) {
      classes += ' bg-gray-100 cursor-not-allowed';
    } else if (value && validation.isValid) {
      classes += ' border-green-300 focus:border-green-500 focus:ring-2 focus:ring-green-200';
    } else if (value && !validation.isValid) {
      classes += ' border-red-300 focus:border-red-500 focus:ring-2 focus:ring-red-200';
    } else {
      classes += ' border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20';
    }

    return classes;
  };

  const getValidationIcon = () => {
    if (!value) return null;
    
    if (validation.isValid) {
      return (
        <Icon 
          name="CheckCircle" 
          size={20} 
          className="text-green-500" 
        />
      );
    } else {
      return (
        <Icon 
          name="AlertCircle" 
          size={20} 
          className="text-red-500" 
        />
      );
    }
  };

  return (
    <div className="space-y-2">
      {/* URL Input Field */}
      <div className="relative">
        <input
          type="url"
          value={value}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={getInputClasses()}
        />
        
        {/* Validation Icon */}
        {value && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {getValidationIcon()}
          </div>
        )}
      </div>

      {/* Validation Messages */}
      {value && (
        <div className="space-y-2">
          {/* Error Message */}
          {!validation.isValid && validation.error && (
            <div className="flex items-start space-x-2 text-sm text-red-600">
              <Icon name="AlertCircle" size={16} className="mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">{validation.error}</p>
                {validation.suggestion && (
                  <div className="mt-1">
                    {validation.suggestion.startsWith('Try: ') ? (
                      <button
                        onClick={applySuggestion}
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {validation.suggestion}
                      </button>
                    ) : (
                      <p className="text-gray-600">{validation.suggestion}</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Warning Message */}
          {validation.isValid && validation.warning && (
            <div className="flex items-start space-x-2 text-sm text-yellow-600">
              <Icon name="AlertTriangle" size={16} className="mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">{validation.warning}</p>
                {validation.suggestion && (
                  <p className="text-gray-600 mt-1">{validation.suggestion}</p>
                )}
              </div>
            </div>
          )}

          {/* Success Message */}
          {validation.isValid && !validation.warning && (
            <div className="flex items-center space-x-2 text-sm text-green-600">
              <Icon name="CheckCircle" size={16} />
              <span>
                {validation.isTestUrl ? (
                  <span className="font-medium">
                    ✨ Test URL detected - will return sample content
                  </span>
                ) : (
                  validation.message
                )}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Help Text */}
      {!value && isFocused && (
        <div className="text-sm text-gray-500 space-y-1">
          <p>Enter a complete URL to extract content from a blog post or article.</p>
          <div className="flex items-center space-x-1">
            <Icon name="Lightbulb" size={14} />
            <span>Try test URLs like: https://example.com/test for sample content</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default URLInput;
