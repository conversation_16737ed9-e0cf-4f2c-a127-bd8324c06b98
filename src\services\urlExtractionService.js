/**
 * URL Content Extraction Service for DocForge AI
 * Handles extracting and cleaning content from blog posts and web URLs
 */

import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor';

// Create a logger for the URL extraction service
const extractionLogger = errorMonitor.createContextLogger('URLExtraction');

/**
 * Extract content from a URL using a web scraping API
 * @param {string} url - The URL to extract content from
 * @returns {Promise<Object>} Extracted content data
 */
export const extractContentFromUrl = async (url) => {
  const startTime = performance.now();

  extractionLogger.info('Starting URL content extraction', {
    url,
    timestamp: new Date().toISOString()
  });

  try {
    // Validate URL format
    const validation = validateUrl(url);
    if (!validation.isValid) {
      extractionLogger.warn('Invalid URL format', {
        url,
        validationError: validation.error,
        suggestion: validation.suggestion
      });
      throw new Error(validation.error || 'Invalid URL format');
    }

    extractionLogger.debug('URL validation passed, beginning extraction', {
      url,
      isTestUrl: validation.isTestUrl || false
    });

    // For Phase 1, we'll use a simple approach with a free web scraping API
    // In production, you might want to use Mercury Parser, Readability API, or similar
    const extractedData = await extractUsingFreeAPI(url);

    const duration = performance.now() - startTime;
    const wordCount = extractedData.content ? extractedData.content.split(/\s+/).length : 0;

    extractionLogger.info('URL content extraction completed successfully', {
      url,
      title: extractedData.title,
      wordCount,
      hasAuthor: !!extractedData.author,
      durationMs: Math.round(duration)
    });

    return {
      success: true,
      data: {
        sourceUrl: url,
        extractedContent: extractedData.content || '',
        originalTitle: extractedData.title || '',
        author: extractedData.author || '',
        publishDate: extractedData.publishDate || '',
        wordCount: wordCount,
        extractedAt: new Date().toISOString(),
        extractionStatus: 'success',
        extractionError: ''
      }
    };

  } catch (error) {
    const duration = performance.now() - startTime;

    extractionLogger.error(error, {
      url,
      action: 'url_extraction',
      durationMs: Math.round(duration),
      errorType: error.name,
      errorMessage: error.message
    });

    return {
      success: false,
      data: {
        sourceUrl: url,
        extractedContent: '',
        originalTitle: '',
        author: '',
        publishDate: '',
        wordCount: 0,
        extractedAt: new Date().toISOString(),
        extractionStatus: 'error',
        extractionError: error.message || 'Failed to extract content from URL'
      }
    };
  }
};

/**
 * Extract content using ThingProxy CORS service
 * @param {string} url - The URL to scrape
 * @returns {Promise<Object>} Scraped content
 */
const extractUsingFreeAPI = async (url) => {
  // Fallback: Try mock data for test URLs first
  if (url.includes('example.com') || url.includes('test')) {
    console.log('Using mock data for test URL');
    return getMockExtractedContent(url);
  }

  try {
    console.log(`Extracting content from URL: ${url}`);

    // Try multiple CORS proxy services in order of reliability
    const proxyServices = [
      `https://api.codetabs.com/v1/proxy?quest=${encodeURIComponent(url)}`,
      `https://corsproxy.io/?${encodeURIComponent(url)}`,
      `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`,
      `https://thingproxy.freeboard.io/fetch/${url}`
    ];

    let lastError = null;

    for (const proxyUrl of proxyServices) {
      try {
        console.log(`Trying proxy: ${proxyUrl.split('?')[0]}...`);

        const fetchOptions = {
          method: 'GET',
          headers: {
            'Accept': 'text/html, application/xhtml+xml, application/xml;q=0.9, */*;q=0.8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        };

        const response = await fetch(proxyUrl, fetchOptions);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const htmlContent = await response.text();

        if (!htmlContent || htmlContent.length < 100) {
          throw new Error('Received empty or insufficient content from the webpage');
        }

        // Check if we got an error page instead of content
        if (htmlContent.includes('Access denied') || htmlContent.includes('403 Forbidden') || htmlContent.includes('404 Not Found')) {
          throw new Error('The webpage is not accessible or does not exist');
        }

        // Basic content extraction from HTML
        const extractedData = parseHtmlContent(htmlContent);

        console.log('Successfully extracted content from URL');
        return extractedData;

      } catch (error) {
        lastError = error;
        console.error(`Proxy failed: ${error.message}`);
        // Continue to next proxy
      }
    }

    // If all proxies failed, throw the last error
    throw lastError || new Error('All proxy services failed to extract content');

  } catch (error) {
    console.error('Content extraction failed:', error.message);

    // Provide helpful error messages based on the type of error
    if (error.message.includes('Failed to fetch') || error.message.includes('network')) {
      throw new Error('Network error: Unable to connect to the website. Please check your internet connection and try again.');
    } else if (error.message.includes('403') || error.message.includes('Access denied')) {
      throw new Error('Access denied: The website is blocking automated content extraction. Try a different URL or copy the content manually.');
    } else if (error.message.includes('404') || error.message.includes('Not Found')) {
      throw new Error('Page not found: The URL does not exist or has been moved. Please check the URL and try again.');
    } else if (error.message.includes('too short') || error.message.includes('No content found')) {
      throw new Error('Content extraction failed: The webpage does not contain enough readable text content. This might be a login page, paywall, or JavaScript-heavy site.');
    } else {
      throw new Error(`Content extraction failed: ${error.message}. Please try a different URL or copy the content manually.`);
    }
  }
};

/**
 * Parse HTML content to extract meaningful text and metadata
 * @param {string} htmlContent - Raw HTML content
 * @returns {Object} Parsed content data
 */
const parseHtmlContent = (htmlContent) => {
  try {
    // Create a temporary DOM element to parse HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // Extract title with multiple fallbacks
    const title = extractTitle(doc);

    // Extract author with multiple strategies
    const author = extractAuthor(doc);

    // Extract publish date
    const publishDate = extractPublishDate(doc);

    // Extract main content using intelligent selectors
    const content = extractMainContent(doc);

    return {
      title: title,
      author: author,
      publishDate: publishDate,
      content: content
    };

  } catch (error) {
    console.error('HTML parsing failed:', error);
    throw new Error('Failed to parse HTML content');
  }
};

/**
 * Extract title from document with multiple fallback strategies
 * @param {Document} doc - Parsed HTML document
 * @returns {string} Extracted title
 */
const extractTitle = (doc) => {
  // Try multiple title extraction strategies in order of preference
  const titleSelectors = [
    'meta[property="og:title"]',
    'meta[name="twitter:title"]',
    'meta[property="article:title"]',
    'title',
    'h1.post-title',
    'h1.entry-title',
    'h1.article-title',
    '.post-title h1',
    '.entry-title h1',
    'h1',
    '[data-testid="headline"]',
    '.headline',
    '.title'
  ];

  for (const selector of titleSelectors) {
    const element = doc.querySelector(selector);
    if (element) {
      let title = (element.content || element.textContent || '').trim();

      // Clean up title - remove site name suffixes common in titles
      title = title.replace(/\s*[-|–]\s*[^-|–]*$/, ''); // Remove " - Site Name" or " | Site Name"
      title = title.replace(/\s*\|\s*Substack$/, ''); // Remove " | Substack"
      title = title.replace(/\s*-\s*Medium$/, ''); // Remove " - Medium"

      if (title && title.length > 0) {
        return title;
      }
    }
  }

  return 'Untitled Document';
};

/**
 * Extract author from document with multiple strategies
 * @param {Document} doc - Parsed HTML document
 * @returns {string} Extracted author
 */
const extractAuthor = (doc) => {
  const authorSelectors = [
    'meta[name="author"]',
    'meta[property="article:author"]',
    'meta[name="twitter:creator"]',
    '.author',
    '.byline',
    '.post-author',
    '.article-author',
    '[rel="author"]',
    '[data-testid="author"]'
  ];

  for (const selector of authorSelectors) {
    const element = doc.querySelector(selector);
    if (element) {
      const author = (element.content || element.textContent || '').trim();
      if (author && author.length > 0) {
        return author;
      }
    }
  }

  return '';
};

/**
 * Extract publish date from document
 * @param {Document} doc - Parsed HTML document
 * @returns {string} Extracted publish date
 */
const extractPublishDate = (doc) => {
  const dateSelectors = [
    'meta[property="article:published_time"]',
    'meta[name="date"]',
    'meta[name="publish_date"]',
    'time[datetime]',
    '.publish-date',
    '.post-date',
    '.article-date'
  ];

  for (const selector of dateSelectors) {
    const element = doc.querySelector(selector);
    if (element) {
      const date = element.content || element.getAttribute('datetime') || element.textContent || '';
      if (date && date.trim().length > 0) {
        return date.trim();
      }
    }
  }

  return '';
};

/**
 * Extract main content from document using intelligent selectors
 * @param {Document} doc - Parsed HTML document
 * @returns {string} Extracted content
 */
const extractMainContent = (doc) => {
  // Remove unwanted elements first
  removeUnwantedElements(doc);

  // Try content selectors in order of preference, including platform-specific ones
  const contentSelectors = [
    // Substack specific
    '.available-content',
    '.post-content',
    '[data-testid="post-content"]',

    // Medium specific
    'article section',
    '.postArticle-content',

    // WordPress and general blog selectors
    'article',
    '[role="main"]',
    '.entry-content',
    '.article-content',
    '.article-body',
    '.post-body',
    '.content',
    'main',
    '.main-content',

    // Dev.to specific
    '.crayons-article__main',

    // Hashnode specific
    '.article-content',

    // Generic fallbacks
    '.body',
    '.text',
    '.story'
  ];

  for (const selector of contentSelectors) {
    const element = doc.querySelector(selector);
    if (element) {
      const content = extractTextFromElement(element);
      if (content && content.length > 200) { // Ensure we have substantial content
        return cleanExtractedContent(content);
      }
    }
  }

  // Fallback: extract from body but filter out navigation and other non-content
  const bodyElement = doc.querySelector('body');
  if (bodyElement) {
    const content = extractTextFromElement(bodyElement);
    return cleanExtractedContent(content);
  }

  throw new Error('No content found in the document');
};

/**
 * Remove unwanted elements from the document
 * @param {Document} doc - Parsed HTML document
 */
const removeUnwantedElements = (doc) => {
  const unwantedSelectors = [
    'script',
    'style',
    'nav',
    'header',
    'footer',
    '.navigation',
    '.nav',
    '.menu',
    '.sidebar',
    '.comments',
    '.comment',
    '.advertisement',
    '.ads',
    '.social-share',
    '.related-posts',
    '.popup',
    '.modal',
    '[role="banner"]',
    '[role="navigation"]',
    '[role="complementary"]'
  ];

  unwantedSelectors.forEach(selector => {
    const elements = doc.querySelectorAll(selector);
    elements.forEach(element => element.remove());
  });
};

/**
 * Extract clean text from a DOM element
 * @param {Element} element - DOM element to extract text from
 * @returns {string} Cleaned text content
 */
const extractTextFromElement = (element) => {
  // Remove script and style elements
  const scripts = element.querySelectorAll('script, style, nav, header, footer, aside');
  scripts.forEach(script => script.remove());

  // Get text content and clean it up
  let text = element.textContent || element.innerText || '';

  // Basic cleaning
  text = text.replace(/\s+/g, ' ').trim();

  return text;
};

/**
 * Clean and format extracted content
 * @param {string} content - Raw extracted content
 * @returns {string} Cleaned content
 */
const cleanExtractedContent = (content) => {
  if (!content) return '';

  // Remove excessive whitespace and normalize line breaks
  content = content.replace(/\s+/g, ' ').trim();
  content = content.replace(/\n\s*\n/g, '\n\n'); // Preserve paragraph breaks

  // Remove common unwanted phrases and patterns
  const unwantedPhrases = [
    'Cookie Policy',
    'Privacy Policy',
    'Terms of Service',
    'Subscribe to newsletter',
    'Follow us on',
    'Share this article',
    'Read more',
    'Continue reading',
    'Sign up',
    'Log in',
    'Advertisement',
    'Sponsored content',
    'Related articles',
    'You might also like',
    'More from',
    'Tags:',
    'Categories:'
  ];

  unwantedPhrases.forEach(phrase => {
    const regex = new RegExp(phrase, 'gi');
    content = content.replace(regex, '');
  });

  // Remove URLs and email addresses
  content = content.replace(/https?:\/\/[^\s]+/g, '');
  content = content.replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '');

  // Remove excessive punctuation
  content = content.replace(/[.]{3,}/g, '...');
  content = content.replace(/[!]{2,}/g, '!');
  content = content.replace(/[?]{2,}/g, '?');

  // Clean up spacing around punctuation
  content = content.replace(/\s+([,.!?;:])/g, '$1');
  content = content.replace(/([.!?])\s*([A-Z])/g, '$1 $2');

  // Final cleanup
  content = content.replace(/\s+/g, ' ').trim();

  // Ensure minimum content length
  if (content.length < 100) {
    throw new Error('Extracted content is too short or contains mostly non-content elements. Please check the URL or try a different source.');
  }

  return content;
};

/**
 * Validate URL format with detailed feedback
 * @param {string} url - URL to validate
 * @returns {Object} Validation result with details
 */
export const validateUrl = (url) => {
  if (!url || url.trim() === '') {
    return {
      isValid: false,
      error: 'Please enter a URL',
      suggestion: 'Enter a complete URL starting with http:// or https://'
    };
  }

  const trimmedUrl = url.trim();

  // Check if URL starts with protocol
  if (!trimmedUrl.startsWith('http://') && !trimmedUrl.startsWith('https://')) {
    return {
      isValid: false,
      error: 'URL must start with http:// or https://',
      suggestion: `Try: https://${trimmedUrl}`
    };
  }

  try {
    const urlObj = new URL(trimmedUrl);

    // Check for valid protocol
    if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
      return {
        isValid: false,
        error: 'Only HTTP and HTTPS URLs are supported',
        suggestion: 'Use http:// or https:// protocol'
      };
    }

    // Check for valid hostname
    if (!urlObj.hostname || urlObj.hostname.length === 0) {
      return {
        isValid: false,
        error: 'Invalid domain name',
        suggestion: 'Enter a valid domain like example.com'
      };
    }

    // Check for localhost or IP addresses (might not work with proxy)
    if (urlObj.hostname === 'localhost' || urlObj.hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
      return {
        isValid: true,
        warning: 'Local URLs may not work with content extraction',
        suggestion: 'Try a public website URL for better results'
      };
    }

    // Check for test URLs that will work with mock data
    if (urlObj.hostname.includes('example.com') || urlObj.hostname.includes('test')) {
      return {
        isValid: true,
        isTestUrl: true,
        message: 'This test URL will return sample content'
      };
    }

    return {
      isValid: true,
      message: 'Valid URL format'
    };

  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid URL format',
      suggestion: 'Check the URL format and try again'
    };
  }
};

/**
 * Simple URL validation for internal use
 * @param {string} url - URL to validate
 * @returns {boolean} True if valid URL
 */
const isValidUrl = (url) => {
  const validation = validateUrl(url);
  return validation.isValid;
};

/**
 * Get mock extracted content for development/testing
 * @param {string} url - Original URL
 * @returns {Object} Mock content data
 */
const getMockExtractedContent = (url) => {
  return {
    title: 'Sample Blog Post: The Future of AI in Content Creation',
    author: 'John Doe',
    publishDate: '2024-01-15',
    content: `Artificial Intelligence is revolutionizing the way we create and consume content. In this comprehensive guide, we'll explore the latest developments in AI-powered content generation and what it means for writers, marketers, and businesses.

The landscape of content creation has changed dramatically over the past few years. With the advent of sophisticated AI models, we're seeing unprecedented capabilities in generating human-like text, creating compelling narratives, and even adapting content for different audiences.

Key benefits of AI in content creation include:
- Increased efficiency and speed
- Consistent quality and tone
- Personalization at scale
- Cost-effective content production

However, it's important to understand that AI is not meant to replace human creativity, but rather to augment and enhance it. The best results come from a collaborative approach where AI handles the heavy lifting while humans provide strategic direction, creativity, and emotional intelligence.

As we look to the future, we can expect even more sophisticated AI tools that will further transform how we approach content creation, making it more accessible, efficient, and impactful than ever before.`
  };
};

/**
 * Preview extracted content (truncated for UI display)
 * @param {string} content - Full extracted content
 * @param {number} maxLength - Maximum length for preview
 * @returns {string} Truncated content preview
 */
export const getContentPreview = (content, maxLength = 200) => {
  if (!content || content.length <= maxLength) {
    return content;
  }

  return content.substring(0, maxLength).trim() + '...';
};

/**
 * Analyze extracted content to suggest document properties
 * @param {Object} extractedData - Extracted content data
 * @returns {Object} Suggested document properties
 */
export const analyzeContentForSuggestions = (extractedData) => {
  const { extractedContent, originalTitle } = extractedData;

  // Basic analysis for suggestions
  const wordCount = extractedContent.split(/\s+/).length;

  // Suggest document type based on content analysis
  const contentLower = extractedContent.toLowerCase();
  let suggestedType = 'ebook';

  // Check for specific document type indicators
  if (contentLower.includes('abstract') || contentLower.includes('methodology')) {
    suggestedType = 'academic';
  } else if (contentLower.includes('executive summary') ||
             contentLower.includes('business') ||
             contentLower.includes('whitepaper') ||
             contentLower.includes('technical report') ||
             contentLower.includes('report') ||
             contentLower.includes('survey') ||
             contentLower.includes('data analysis')) {
    suggestedType = 'business';
  } else if (contentLower.includes('manual') ||
             contentLower.includes('instruction') ||
             contentLower.includes('step') ||
             contentLower.includes('how to')) {
    suggestedType = 'guide';
  } else if (wordCount < 1000) {
    suggestedType = 'guide';
  } else if (wordCount > 5000) {
    suggestedType = 'business'; // Changed from 'report' to 'business'
  }

  // Extract potential topic from title
  const suggestedTopic = originalTitle.replace(/[^\w\s]/g, '').trim();

  return {
    suggestedDocumentType: suggestedType,
    suggestedTopic: suggestedTopic,
    estimatedLength: wordCount > 3000 ? 'long' : wordCount > 1500 ? 'medium' : 'short',
    contentComplexity: wordCount > 2000 ? 'comprehensive' : 'detailed'
  };
};