/**
 * Unsplash Service for DocForge AI
 * Handles image fetching and integration for document content
 */

import { createApi } from 'unsplash-js';

// Initialize Unsplash API
const UNSPLASH_ACCESS_KEY = import.meta.env.VITE_UNSPLASH_ACCESS_KEY;

let unsplash = null;

if (UNSPLASH_ACCESS_KEY) {
  unsplash = createApi({
    accessKey: UNSPLASH_ACCESS_KEY,
  });
}

/**
 * Search for images based on query terms
 * @param {string} query - Search query for images
 * @param {number} count - Number of images to fetch (default: 3)
 * @param {string} orientation - Image orientation preference
 * @returns {Promise<Array>} Array of image objects
 */
export const searchImages = async (query, count = 3, orientation = 'landscape') => {
  try {
    if (!unsplash || !UNSPLASH_ACCESS_KEY || UNSPLASH_ACCESS_KEY === 'your-unsplash-access-key-here') {
      console.warn('Unsplash not configured (missing or placeholder API key), returning mock images for query:', query);
      return getMockImages(query, count);
    }

    const result = await unsplash.search.getPhotos({
      query: query,
      page: 1,
      perPage: count,
      orientation: orientation,
      orderBy: 'relevant',
    });

    if (result.errors) {
      console.error('Unsplash API errors:', result.errors);
      return getMockImages(query, count);
    }

    const images = result.response.results.map(photo => ({
      id: photo.id,
      url: photo.urls.regular,
      thumbnailUrl: photo.urls.small,
      description: photo.description || photo.alt_description || query,
      photographer: photo.user.name,
      photographerUrl: photo.user.links.html,
      downloadUrl: photo.links.download_location,
      width: photo.width,
      height: photo.height,
      color: photo.color,
    }));

    return images;

  } catch (error) {
    console.error('Error fetching images from Unsplash:', error);
    return getMockImages(query, count);
  }
};

/**
 * Generate simple chapter-based image suggestions (no complex placement logic)
 * @param {Object} chapter - Chapter object with title and content
 * @param {Object} documentData - Document data for context
 * @returns {Object} Simple chapter suggestion object
 */
const generateChapterImageSuggestion = (chapter, documentData) => {
  return {
    chapterNumber: chapter.number,
    chapterTitle: chapter.title,
    searchQuery: generateSearchQuery(
      chapter.title,
      documentData.topicAndNiche?.mainTopic,
      documentData.topicAndNiche?.subNiches
    ),
    // Simple metadata for the chapter
    description: `Image suggestions for ${chapter.title}`,
    contextualHint: `Visual content to enhance Chapter ${chapter.number}: ${chapter.title}`
  };
};

/**
 * Generate image suggestions for document chapters
 * @param {Object} documentData - Complete document data
 * @param {Object} outline - Document outline with chapters
 * @returns {Promise<Object>} Object with chapter IDs as keys and image arrays as values
 */
export const generateImageSuggestions = async (documentData, outline) => {
  try {
    const suggestions = {};

    if (!outline?.chapters) {
      console.warn('No chapters found in outline, returning empty suggestions');
      return suggestions;
    }

    // Generate simple chapter-based image suggestions
    for (const chapter of outline.chapters) {
      const chapterSuggestion = generateChapterImageSuggestion(chapter, documentData);
      const images = await searchImages(chapterSuggestion.searchQuery, 3);

      suggestions[`chapter-${chapter.number}`] = {
        chapterTitle: chapter.title,
        chapterNumber: chapter.number,
        searchQuery: chapterSuggestion.searchQuery,
        images: images,
        description: chapterSuggestion.description,
        contextualHint: chapterSuggestion.contextualHint,
        // Simplified: no complex placement logic, just chapter-boundary placement
        placementType: 'chapter-boundary',
        // Legacy support - keep existing structure for backward compatibility
        suggestedPlacements: [
          {
            type: 'chapter-header',
            position: 'before-chapter',
            description: `Image suggestions for ${chapter.title}`
          }
        ]
      };
    }

    return suggestions;

  } catch (error) {
    console.error('Error generating image suggestions:', error);
    return {};
  }
};

/**
 * Generate optimized search query for images
 * @param {string} chapterTitle - Chapter title
 * @param {string} mainTopic - Main document topic
 * @param {Array} subNiches - Selected sub-niches
 * @returns {string} Optimized search query
 */
const generateSearchQuery = (chapterTitle, mainTopic, subNiches = []) => {
  // Extract key terms from chapter title
  const chapterKeywords = chapterTitle
    .toLowerCase()
    .replace(/chapter \d+:?\s*/i, '') // Remove "Chapter X:" prefix
    .replace(/[^\w\s]/g, '') // Remove special characters
    .split(' ')
    .filter(word => word.length > 2) // Filter short words
    .slice(0, 3); // Take first 3 meaningful words

  // Combine with main topic
  const topicKeywords = mainTopic
    .toLowerCase()
    .split(' ')
    .filter(word => word.length > 2)
    .slice(0, 2);

  // Add relevant sub-niche if available
  const subNicheKeyword = subNiches.length > 0 
    ? subNiches[0].name?.toLowerCase().split(' ')[0] 
    : '';

  // Combine and create search query
  const allKeywords = [...chapterKeywords, ...topicKeywords];
  if (subNicheKeyword) {
    allKeywords.push(subNicheKeyword);
  }

  return allKeywords.slice(0, 4).join(' ');
};

/**
 * Mock images for when Unsplash is not available
 * @param {string} query - Search query
 * @param {number} count - Number of images
 * @returns {Array} Mock image objects
 */
const getMockImages = (query, count) => {
  const mockImages = [];
  
  for (let i = 0; i < count; i++) {
    mockImages.push({
      id: `mock-${query.replace(/\s+/g, '-')}-${i + 1}`,
      url: `https://picsum.photos/800/600?random=${Date.now() + i}`,
      thumbnailUrl: `https://picsum.photos/300/200?random=${Date.now() + i}`,
      description: `${query} illustration ${i + 1}`,
      photographer: 'Stock Photo',
      photographerUrl: '#',
      downloadUrl: '#',
      width: 800,
      height: 600,
      color: '#6B7280',
    });
  }
  
  return mockImages;
};

/**
 * Download and track image usage (required by Unsplash API)
 * @param {string} downloadUrl - Download URL from Unsplash
 */
export const trackImageDownload = async (downloadUrl) => {
  try {
    if (!unsplash || !downloadUrl || downloadUrl === '#') {
      return;
    }

    // This is required by Unsplash API terms to track downloads
    await fetch(downloadUrl);
  } catch (error) {
    console.error('Error tracking image download:', error);
  }
};

/**
 * Get image placement suggestions for content
 * @param {string} content - Text content
 * @param {Array} images - Available images
 * @returns {Array} Placement suggestions
 */
export const getImagePlacementSuggestions = (content, images) => {
  const suggestions = [];
  const paragraphs = content.split('\n\n');
  
  if (paragraphs.length < 2 || images.length === 0) {
    return suggestions;
  }

  // Suggest image after introduction (first paragraph)
  if (paragraphs.length > 1) {
    suggestions.push({
      position: 1, // After first paragraph
      image: images[0],
      type: 'content-break',
      description: 'Visual break after introduction'
    });
  }

  // Suggest image in middle of content
  if (paragraphs.length > 4 && images.length > 1) {
    const middlePosition = Math.floor(paragraphs.length / 2);
    suggestions.push({
      position: middlePosition,
      image: images[1],
      type: 'section-illustration',
      description: 'Section illustration'
    });
  }

  // Suggest image near end if content is long
  if (paragraphs.length > 6 && images.length > 2) {
    suggestions.push({
      position: paragraphs.length - 2,
      image: images[2],
      type: 'conclusion-visual',
      description: 'Visual support for conclusion'
    });
  }

  return suggestions;
};

/**
 * Check if Unsplash is configured
 * @returns {boolean} Whether Unsplash API is available
 */
export const isUnsplashConfigured = () => {
  return !!(UNSPLASH_ACCESS_KEY && unsplash);
};

/**
 * Get Unsplash configuration status
 * @returns {Object} Configuration status and details
 */
export const getUnsplashStatus = () => {
  return {
    configured: isUnsplashConfigured(),
    hasAccessKey: !!UNSPLASH_ACCESS_KEY,
    apiInitialized: !!unsplash,
  };
};
