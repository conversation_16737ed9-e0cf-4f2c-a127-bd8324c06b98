import React from "react";
import Routes from "./Routes";
import { SidebarProvider } from "./contexts/SidebarContext";
import { AuthProvider } from "./contexts/AuthContext";
import AuthErrorBoundary from "./components/auth/AuthErrorBoundary";
import "./styles/animations.css";

function App() {
  return (
    <AuthErrorBoundary>
      <AuthProvider>
        <SidebarProvider>
          <Routes />
        </SidebarProvider>
      </AuthProvider>
    </AuthErrorBoundary>
  );
}

export default App;
