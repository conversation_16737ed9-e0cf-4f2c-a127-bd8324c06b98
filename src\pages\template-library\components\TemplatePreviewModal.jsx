import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const TemplatePreviewModal = ({ template, isOpen, onClose, onSelect }) => {
  const [currentPage, setCurrentPage] = useState(0);

  if (!isOpen || !template) return null;

  const samplePages = template.samplePages || [
    template.thumbnail,
    `https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=400&h=600&fit=crop`,
    `https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=600&fit=crop`
  ];

  const nextPage = () => {
    setCurrentPage(prev => (prev + 1) % samplePages.length);
  };

  const prevPage = () => {
    setCurrentPage(prev => (prev - 1 + samplePages.length) % samplePages.length);
  };

  const formatBadges = (formats) => {
    const badgeColors = {
      PDF: 'bg-error/10 text-error',
      EPUB: 'bg-success/10 text-success',
      MOBI: 'bg-warning/10 text-warning',
      DOCX: 'bg-secondary/10 text-secondary'
    };

    return formats.map(format => ({
      format,
      className: badgeColors[format] || 'bg-background text-text-secondary'
    }));
  };

  return (
    <div className="fixed inset-0 z-1200 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-surface rounded-xl shadow-elevation-3 max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-text-primary">{template.title}</h2>
            <span className="text-sm text-text-secondary bg-background px-3 py-1 rounded-full">
              {template.category}
            </span>
          </div>
          <Button
            variant="ghost"
            onClick={onClose}
            className="p-2"
          >
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-8rem)]">
          {/* Preview Area */}
          <div className="flex-1 flex flex-col">
            {/* Page Navigation */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-text-secondary">
                  Page {currentPage + 1} of {samplePages.length}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  onClick={prevPage}
                  disabled={samplePages.length <= 1}
                  className="p-2"
                >
                  <Icon name="ChevronLeft" size={16} />
                </Button>
                <Button
                  variant="ghost"
                  onClick={nextPage}
                  disabled={samplePages.length <= 1}
                  className="p-2"
                >
                  <Icon name="ChevronRight" size={16} />
                </Button>
              </div>
            </div>

            {/* Page Display */}
            <div className="flex-1 p-6 flex items-center justify-center bg-background">
              <div className="relative max-w-md w-full">
                <Image
                  src={samplePages[currentPage]}
                  alt={`${template.title} - Page ${currentPage + 1}`}
                  className="w-full h-auto rounded-lg shadow-elevation-2"
                />
              </div>
            </div>
          </div>

          {/* Details Sidebar */}
          <div className="w-80 border-l border-border p-6 overflow-y-auto">
            {/* Template Info */}
            <div className="mb-6">
              <h3 className="font-semibold text-text-primary mb-2">Template Details</h3>
              <p className="text-sm text-text-secondary mb-4">
                {template.description}
              </p>

              {/* Stats */}
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-text-secondary">Usage Count:</span>
                  <span className="text-text-primary font-medium">
                    {template.usageCount.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-text-secondary">Rating:</span>
                  <div className="flex items-center space-x-1">
                    <Icon name="Star" size={14} color="var(--color-warning)" />
                    <span className="text-text-primary font-medium">{template.rating}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-text-secondary">Estimated Time:</span>
                  <span className="text-text-primary font-medium">{template.estimatedTime}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-text-secondary">Complexity:</span>
                  <span className="text-text-primary font-medium capitalize">
                    {template.complexity}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-text-secondary">Language:</span>
                  <span className="text-text-primary font-medium">{template.language}</span>
                </div>
              </div>
            </div>

            {/* Format Support */}
            <div className="mb-6">
              <h3 className="font-semibold text-text-primary mb-3">Supported Formats</h3>
              <div className="flex flex-wrap gap-2">
                {formatBadges(template.formats).map(({ format, className }) => (
                  <span
                    key={format}
                    className={`text-xs px-3 py-1.5 rounded-full font-medium ${className}`}
                  >
                    {format}
                  </span>
                ))}
              </div>
            </div>

            {/* Features */}
            <div className="mb-6">
              <h3 className="font-semibold text-text-primary mb-3">Key Features</h3>
              <ul className="space-y-2">
                {template.features?.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-2 text-sm">
                    <Icon name="Check" size={14} color="var(--color-success)" className="mt-0.5 flex-shrink-0" />
                    <span className="text-text-secondary">{feature}</span>
                  </li>
                )) || [
                  "Professional layout design",
                  "Customizable sections",
                  "Multiple export formats",
                  "Mobile-responsive design"
                ].map((feature, index) => (
                  <li key={index} className="flex items-start space-x-2 text-sm">
                    <Icon name="Check" size={14} color="var(--color-success)" className="mt-0.5 flex-shrink-0" />
                    <span className="text-text-secondary">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Credits */}
            <div className="mb-6 p-4 bg-background rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm text-text-secondary">Credits Required:</span>
                <span className={`text-lg font-semibold ${
                  template.credits === 0 ? 'text-success' : 'text-text-primary'
                }`}>
                  {template.credits === 0 ? 'Free' : `${template.credits} Credits`}
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                variant="primary"
                onClick={() => onSelect(template)}
                className="w-full"
              >
                <Icon name="Plus" size={16} />
                Use This Template
              </Button>
              <Button
                variant="outline"
                className="w-full"
              >
                <Icon name="Heart" size={16} />
                Add to Favorites
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplatePreviewModal;