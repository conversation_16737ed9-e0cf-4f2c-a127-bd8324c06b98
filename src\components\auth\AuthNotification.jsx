import React, { useState, useEffect } from 'react'
import Icon from '../AppIcon'
import Button from '../ui/Button'

const AuthNotification = ({ 
  type = 'info', 
  title, 
  message, 
  onClose, 
  onAction,
  actionLabel,
  autoClose = true,
  duration = 5000,
  persistent = false
}) => {
  const [isVisible, setIsVisible] = useState(true)
  const [isClosing, setIsClosing] = useState(false)

  useEffect(() => {
    if (autoClose && !persistent) {
      const timer = setTimeout(() => {
        handleClose()
      }, duration)
      return () => clearTimeout(timer)
    }
  }, [autoClose, persistent, duration])

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(() => {
      setIsVisible(false)
      if (onClose) onClose()
    }, 300)
  }

  const handleAction = () => {
    if (onAction) onAction()
    if (!persistent) handleClose()
  }

  if (!isVisible) return null

  const typeConfig = {
    success: {
      bgColor: 'bg-success/10',
      borderColor: 'border-success/20',
      textColor: 'text-success',
      icon: 'CheckCircle'
    },
    error: {
      bgColor: 'bg-error/10',
      borderColor: 'border-error/20',
      textColor: 'text-error',
      icon: 'AlertCircle'
    },
    warning: {
      bgColor: 'bg-warning/10',
      borderColor: 'border-warning/20',
      textColor: 'text-warning',
      icon: 'AlertTriangle'
    },
    info: {
      bgColor: 'bg-primary/10',
      borderColor: 'border-primary/20',
      textColor: 'text-primary',
      icon: 'Info'
    }
  }

  const config = typeConfig[type]

  return (
    <div className={`fixed top-4 right-4 z-50 max-w-md w-full transition-all duration-300 ${
      isClosing ? 'transform translate-x-full opacity-0' : 'transform translate-x-0 opacity-100'
    }`}>
      <div className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4 shadow-elevated backdrop-blur-sm`}>
        <div className="flex items-start space-x-3">
          <Icon 
            name={config.icon} 
            size={20} 
            color={`var(--color-${type})`}
            className="flex-shrink-0 mt-0.5"
          />
          
          <div className="flex-1 min-w-0">
            {title && (
              <h4 className={`font-medium ${config.textColor} mb-1`}>
                {title}
              </h4>
            )}
            <p className="text-sm text-text-secondary">
              {message}
            </p>
            
            {actionLabel && onAction && (
              <div className="mt-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleAction}
                  className={`${config.textColor} hover:bg-current/10`}
                >
                  {actionLabel}
                </Button>
              </div>
            )}
          </div>
          
          {!persistent && (
            <button
              onClick={handleClose}
              className="flex-shrink-0 text-text-muted hover:text-text-primary transition-colors"
            >
              <Icon name="X" size={16} />
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// Predefined notification types for common auth scenarios
export const SuccessNotification = ({ message, onClose }) => (
  <AuthNotification
    type="success"
    title="Success"
    message={message}
    onClose={onClose}
  />
)

export const ErrorNotification = ({ message, onClose, onRetry }) => (
  <AuthNotification
    type="error"
    title="Error"
    message={message}
    onClose={onClose}
    onAction={onRetry}
    actionLabel={onRetry ? "Try Again" : undefined}
    persistent={!!onRetry}
  />
)

export const SessionExpiredNotification = ({ onSignIn }) => (
  <AuthNotification
    type="warning"
    title="Session Expired"
    message="Your session has expired. Please sign in again to continue."
    onAction={onSignIn}
    actionLabel="Sign In"
    persistent={true}
  />
)

export const WelcomeNotification = ({ userName, onClose }) => (
  <AuthNotification
    type="success"
    title="Welcome!"
    message={`Welcome to DocForge AI, ${userName}! Let's create something amazing.`}
    onClose={onClose}
    duration={7000}
  />
)

export const ProfileUpdateNotification = ({ onClose }) => (
  <AuthNotification
    type="success"
    title="Profile Updated"
    message="Your profile has been successfully updated."
    onClose={onClose}
  />
)

export const PasswordResetNotification = ({ email, onClose }) => (
  <AuthNotification
    type="info"
    title="Password Reset Sent"
    message={`We've sent a password reset link to ${email}. Please check your email.`}
    onClose={onClose}
    duration={8000}
  />
)

export const EmailVerificationNotification = ({ onResend, onClose }) => (
  <AuthNotification
    type="info"
    title="Verify Your Email"
    message="Please check your email and click the verification link to activate your account."
    onAction={onResend}
    actionLabel="Resend Email"
    onClose={onClose}
    persistent={true}
  />
)

// Notification manager hook
export const useAuthNotifications = () => {
  const [notifications, setNotifications] = useState([])

  const addNotification = (notification) => {
    const id = Date.now() + Math.random()
    setNotifications(prev => [...prev, { ...notification, id }])
  }

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearAll = () => {
    setNotifications([])
  }

  const showSuccess = (message, options = {}) => {
    addNotification({
      type: 'success',
      title: 'Success',
      message,
      ...options
    })
  }

  const showError = (message, options = {}) => {
    addNotification({
      type: 'error',
      title: 'Error',
      message,
      ...options
    })
  }

  const showWarning = (message, options = {}) => {
    addNotification({
      type: 'warning',
      title: 'Warning',
      message,
      ...options
    })
  }

  const showInfo = (message, options = {}) => {
    addNotification({
      type: 'info',
      title: 'Info',
      message,
      ...options
    })
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}

// Notification container component
export const AuthNotificationContainer = ({ notifications, onRemove }) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <AuthNotification
          key={notification.id}
          {...notification}
          onClose={() => onRemove(notification.id)}
        />
      ))}
    </div>
  )
}

export default AuthNotification
