import React from 'react';
import Icon from '../AppIcon';

const ProjectCard = ({
  project,
  onClick,
  onPreview = null,
  onDelete = null,
  showActions = true,
  compact = false
}) => {
  const formatDate = (dateString) => {
    if (!dateString) {
      return 'No date';
    }

    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get default thumbnail if none provided
  const getDefaultThumbnail = (documentType) => {
    const defaults = {
      'ebook': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
      'academic': 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop',
      'business': 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
      'report': 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=300&fit=crop'
    };
    
    return defaults[documentType] || defaults['business'];
  };

  const thumbnailUrl = project.thumbnail_url || project.thumbnail || getDefaultThumbnail(project.document_type);

  return (
    <div
      className="bg-surface rounded-md border border-border hover:shadow-elevated transition-all duration-300 cursor-pointer group overflow-hidden"
      onClick={() => onClick(project)}
    >
      {/* Project Thumbnail */}
      <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
        <img
          src={thumbnailUrl}
          alt={project.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          onError={(e) => {
            // Fallback to default thumbnail if image fails to load
            e.target.src = getDefaultThumbnail(project.document_type);
          }}
        />

        {/* Status Badge */}
        <div className={`absolute top-3 left-3 px-2 py-1 rounded-lg text-xs font-medium shadow-sm ${
          project.status === 'completed' ? 'bg-success text-white' :
          project.status === 'draft' ? 'bg-warning text-white' :
          'bg-primary text-white'
        }`}>
          {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
        </div>

        {/* Hover Actions - Only show if showActions is true */}
        {showActions && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex items-center space-x-2">
              {onPreview && (
                <button
                  className="bg-white text-text-primary p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onPreview(project);
                  }}
                  title="Preview"
                >
                  <Icon name="Eye" size={16} />
                </button>
              )}
              <button
                className="bg-white text-text-primary p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onClick(project);
                }}
                title="Edit"
              >
                <Icon name="Edit" size={16} />
              </button>
              {onDelete && (
                <button
                  className="bg-white text-destructive p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(project);
                  }}
                  title="Delete project"
                  aria-label={`Delete ${project.title}`}
                >
                  <Icon name="Trash2" size={16} />
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Project Info */}
      <div className="p-4">
        <h4 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300 line-clamp-2 mb-2">
          {project.title}
        </h4>
        <div className="flex items-center justify-between text-sm text-text-secondary">
          <span className="capitalize">{project.document_type}</span>
          <span>{formatDate(project.updated_at)}</span>
        </div>

        {project.progress < 100 && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
              <span>Progress</span>
              <span>{project.progress}%</span>
            </div>
            <div className="w-full bg-surface-secondary rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${project.progress}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectCard;
