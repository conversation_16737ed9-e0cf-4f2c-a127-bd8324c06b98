/**
 * Export Service - Handles document export functionality
 * Supports PDF, ePub, DOCX, and HTML formats
 */

// Utility function to extract images from block-based content
const extractImagesFromContent = (content) => {
  if (!content) return [];

  const images = [];
  const imageRegex = /!\[(.*?)\]\((.*?)\)/g;
  let match;

  while ((match = imageRegex.exec(content)) !== null) {
    const [, alt, src] = match;
    images.push({
      src: src,
      alt: alt || 'Image',
      description: alt || 'Image'
    });
  }

  return images;
};

// Utility function to convert markdown to HTML with image handling
const markdownToHtml = (markdown) => {
  if (!markdown) return '';

  return markdown
    // Images - convert to HTML img tags
    .replace(/!\[(.*?)\]\((.*?)\)/gim, '<img src="$2" alt="$1" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin: 20px 0;" />')
    // Headers
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // Bold
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    // Italic
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    // Lists
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/gims, '<ul>$1</ul>')
    // Line breaks
    .replace(/\n/gim, '<br>');
};

// Helper function to render images from block-based content (DEPRECATED - images now handled in markdownToHtml)
const renderChapterImages = (chapterId, placedImages, position = 'top') => {
  // Legacy function - no longer used as images are now embedded in content
  // Images are handled directly in markdownToHtml function
  return '';
};

// Generate HTML content from document data
const generateHtmlContent = (documentData, generatedContent) => {
  const { title, author, description } = documentData;
  const chapters = generatedContent.chapters || [];





  // Note: placedImages is legacy - images are now embedded in content as markdown

  let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title || 'Document'}</title>
    <style>
        body {
            font-family: 'Georgia', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            color: #333;
        }
        .title-page {
            text-align: center;
            margin-bottom: 60px;
            page-break-after: always;
        }
        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        .author {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        .description {
            font-style: italic;
            color: #95a5a6;
            max-width: 600px;
            margin: 0 auto;
        }
        .chapter {
            margin-bottom: 40px;
            page-break-before: always;
        }
        .chapter-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .chapter-content {
            text-align: justify;
        }
        .chapter-image {
            margin: 20px 0;
            text-align: center;
            page-break-inside: avoid;
        }
        .chapter-image img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .image-credit {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }
        h1, h2, h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        p {
            margin-bottom: 15px;
        }
        @media print {
            body {
                font-size: 12pt;
            }
            .chapter {
                page-break-before: always;
            }
            .chapter-image {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="title-page">
        <h1 class="title">${title || 'Untitled Document'}</h1>
        ${author ? `<div class="author">by ${author}</div>` : ''}
        ${description ? `<div class="description">${description}</div>` : ''}
    </div>
`;

  // Add chapters (images are now embedded in content)
  chapters.forEach((chapter, index) => {
    htmlContent += `
    <div class="chapter">
        <h2 class="chapter-title">Chapter ${chapter.number || index + 1}: ${chapter.title || 'Untitled Chapter'}</h2>
        <div class="chapter-content">
            ${markdownToHtml(chapter.content || '')}
        </div>
    </div>
`;
  });

  htmlContent += `
</body>
</html>`;

  return htmlContent;
};

// Create and download file
const downloadFile = (content, filename, mimeType) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Export as HTML
export const exportAsHtml = async (documentData, generatedContent) => {
  try {
    const htmlContent = generateHtmlContent(documentData, generatedContent);
    const filename = `${documentData.title || 'document'}.html`;
    downloadFile(htmlContent, filename, 'text/html');
    return { success: true, message: 'HTML export completed successfully' };
  } catch (error) {
    console.error('HTML export failed:', error);
    return { success: false, error: error.message };
  }
};

// Export as PDF (using browser's print functionality)
export const exportAsPdf = async (documentData, generatedContent) => {
  try {
    // Create a new window with the HTML content
    const htmlContent = generateHtmlContent(documentData, generatedContent);
    const printWindow = window.open('', '_blank');
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Wait for content to load, then trigger print
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    };

    return { success: true, message: 'PDF export initiated. Please use your browser\'s print dialog to save as PDF.' };
  } catch (error) {
    console.error('PDF export failed:', error);
    return { success: false, error: error.message };
  }
};

// Helper function to extract and format images for RTF content
const extractRtfImages = (content) => {
  if (!content) return '';

  const images = extractImagesFromContent(content);
  if (images.length === 0) return '';

  // RTF doesn't support external images easily, so we'll add image references as text
  return images.map(image => `\\par\\par{\\i [Image: ${image.description || 'Image'}]}\\par\\par`).join('');
};

// Export as DOCX with embedded images using proper DOCX generation
export const exportAsDocx = async (documentData, generatedContent, editorInstance = null) => {
  try {
    // Import the DOCX generation service
    const { generateDocxWithImages, downloadDocxFile } = await import('./docxGenerationService.js');

    // Extract content from the document
    let content = '';

    // If we have an editor instance, use its HTML content
    if (editorInstance && typeof editorInstance.getHTML === 'function') {
      content = editorInstance.getHTML();
    } else {
      // Fallback: combine chapter content as markdown
      const chapters = generatedContent.chapters || [];
      content = chapters.map((chapter, index) => {
        const chapterTitle = `# Chapter ${chapter.number || index + 1}: ${chapter.title || 'Untitled Chapter'}`;
        const chapterContent = chapter.content || '';
        return `${chapterTitle}\n\n${chapterContent}`;
      }).join('\n\n---\n\n');
    }

    // Determine content type
    const contentType = editorInstance ? 'html' : 'markdown';

    // Generate DOCX with embedded images
    const result = await generateDocxWithImages(documentData, content, contentType);

    if (result.success) {
      // Download the generated DOCX file
      const filename = `${documentData.title || 'document'}.docx`;
      downloadDocxFile(result.blob, filename);

      // Create detailed success message with image statistics
      let message = 'DOCX export completed successfully with embedded images';
      if (result.imageStats && result.imageStats.totalImages > 0) {
        const { totalImages, successfulImages, failedImages } = result.imageStats;
        message += `. Images: ${successfulImages}/${totalImages} embedded successfully`;
        if (failedImages > 0) {
          message += `, ${failedImages} failed to download`;
        }
      }

      return {
        success: true,
        message: message,
        imageStats: result.imageStats,
        userMessage: result.userMessage || message
      };
    } else {
      return {
        success: false,
        error: result.error || 'DOCX generation failed',
        userMessage: result.userMessage || 'Failed to generate DOCX file'
      };
    }
  } catch (error) {
    console.error('DOCX export failed:', error);
    return {
      success: false,
      error: error.message,
      userMessage: 'An unexpected error occurred during DOCX export. Please try again.'
    };
  }
};

// Export as ePub (simplified - creates a basic ePub structure)
export const exportAsEpub = async (documentData, generatedContent) => {
  try {
    // For now, we'll export as HTML with ePub-like structure
    // A full ePub implementation would require a library like epub-gen
    const htmlContent = generateHtmlContent(documentData, generatedContent);
    const filename = `${documentData.title || 'document'}_epub.html`;
    downloadFile(htmlContent, filename, 'text/html');

    return { success: true, message: 'ePub export completed (HTML format - use Calibre to convert to proper ePub)' };
  } catch (error) {
    console.error('ePub export failed:', error);
    return { success: false, error: error.message };
  }
};

// Main export function
export const exportDocument = async (format, documentData, generatedContent, options = {}) => {
  if (!documentData || !generatedContent) {
    return { success: false, error: 'Missing document data or content', userMessage: 'Unable to export: missing document data or content' };
  }

  try {
    const { editorInstance } = options;

    // Log export attempt with format and editor information
    console.log(`Exporting document "${documentData.title || 'Untitled'}" as ${format}${editorInstance ? ' with TipTap editor' : ' with legacy content'}`);

    switch (format.toLowerCase()) {
      case 'pdf':
        return await exportAsPdf(documentData, generatedContent);
      case 'html':
        return await exportAsHtml(documentData, generatedContent);
      case 'docx':
      case 'word':
        return await exportAsDocx(documentData, generatedContent, editorInstance);
      case 'epub':
      case 'flipbook':
      case 'kindle':
        return await exportAsEpub(documentData, generatedContent);
      default:
        return {
          success: false,
          error: `Unsupported export format: ${format}`,
          userMessage: `Export format "${format}" is not supported`
        };
    }
  } catch (error) {
    console.error('Export failed:', error);
    return {
      success: false,
      error: error.message,
      userMessage: 'An error occurred during export. Please try again.'
    };
  }
};

// Get document statistics
export const getDocumentStatistics = (generatedContent) => {
  if (!generatedContent || !generatedContent.chapters) {
    return { pages: 0, chapters: 0, words: 0, readTime: 0 };
  }

  const chapters = generatedContent.chapters.length;
  const words = generatedContent.chapters.reduce((total, chapter) =>
    total + (chapter.wordCount || 0), 0);
  const pages = Math.ceil(words / 250); // Approximate 250 words per page
  const readTime = Math.ceil(words / 200); // Approximate 200 words per minute

  return { pages, chapters, words, readTime };
};
