/**
 * Content Processing Service for DOCX Export
 * 
 * Handles conversion of HTML (from TipTap editor) and markdown content
 * to DOCX-compatible format using the docx library.
 * 
 * This service provides utilities to:
 * - Parse HTML content from TipTap editor
 * - Convert markdown content to DOCX format
 * - Extract and process images from both HTML and markdown
 * - Create unified content processing pipeline
 */

import DOMPurify from 'dompurify';
import { Paragraph, TextRun, HeadingLevel, ImageRun, ExternalHyperlink } from 'docx';
import { validateDocumentData } from './errorHandlingService.js';

// Dynamic imports for unified ecosystem to avoid Jest ES module issues
let unified, remarkParse, remarkRehype, rehypeStringify, rehypeParse;

const loadUnifiedDependencies = async () => {
    if (!unified) {
        try {
            const unifiedModule = await import('unified');
            const remarkParseModule = await import('remark-parse');
            const remarkRehypeModule = await import('remark-rehype');
            const rehypeStringifyModule = await import('rehype-stringify');
            const rehypeParseModule = await import('rehype-parse');

            unified = unifiedModule.unified;
            remarkParse = remarkParseModule.default;
            remarkRehype = remarkRehypeModule.default;
            rehypeStringify = rehypeStringifyModule.default;
            rehypeParse = rehypeParseModule.default;
        } catch (error) {
            console.warn('Failed to load unified dependencies:', error);
            // Fallback to simple markdown processing
            return false;
        }
    }
    return true;
};

/**
 * Content types for processed content
 */
export const CONTENT_TYPES = {
    PARAGRAPH: 'paragraph',
    HEADING: 'heading',
    IMAGE: 'image',
    LIST: 'list',
    LIST_ITEM: 'list_item'
};

/**
 * Heading levels mapping
 */
export const HEADING_LEVELS = {
    1: HeadingLevel.HEADING_1,
    2: HeadingLevel.HEADING_2,
    3: HeadingLevel.HEADING_3,
    4: HeadingLevel.HEADING_4,
    5: HeadingLevel.HEADING_5,
    6: HeadingLevel.HEADING_6
};

/**
 * Parse HTML content from TipTap editor and convert to DOCX-compatible format
 * @param {string} htmlContent - HTML content from TipTap editor
 * @returns {Array} Array of processed content objects
 */
export const parseHTMLContent = (htmlContent) => {
    if (!htmlContent || typeof htmlContent !== 'string') {
        return [];
    }

    try {
        // Sanitize HTML content
        const sanitizedHTML = DOMPurify.sanitize(htmlContent, {
            ALLOWED_TAGS: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'strong', 'em', 'u', 'img', 'ul', 'ol', 'li', 'a', 'br'],
            ALLOWED_ATTR: ['src', 'alt', 'href', 'class', 'width', 'height']
        });

        // Create a DOM parser
        const parser = new DOMParser();
        const doc = parser.parseFromString(sanitizedHTML, 'text/html');

        const processedContent = [];
        const bodyElements = doc.body.children;

        for (let i = 0; i < bodyElements.length; i++) {
            const element = bodyElements[i];
            const processed = processHTMLElement(element);
            if (processed) {
                if (Array.isArray(processed)) {
                    processedContent.push(...processed);
                } else {
                    processedContent.push(processed);
                }
            }
        }

        return processedContent;
    } catch (error) {
        console.error('Error parsing HTML content:', error);
        return [{
            type: CONTENT_TYPES.PARAGRAPH,
            text: 'Error processing HTML content',
            formatting: {}
        }];
    }
};

/**
 * Process individual HTML element and convert to DOCX format
 * @param {Element} element - HTML element to process
 * @returns {Object|Array|null} Processed content object(s)
 */
export const processHTMLElement = (element) => {
    const tagName = element.tagName.toLowerCase();

    switch (tagName) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
            return processHeading(element);

        case 'p':
            return processParagraph(element);

        case 'img':
            return processImage(element);

        case 'ul':
        case 'ol':
            return processList(element);

        default:
            // For other elements, try to extract text content
            const textContent = element.textContent?.trim();
            if (textContent) {
                return {
                    type: CONTENT_TYPES.PARAGRAPH,
                    text: textContent,
                    formatting: {}
                };
            }
            return null;
    }
};

/**
 * Process heading element
 * @param {Element} element - Heading element (h1-h6)
 * @returns {Object} Processed heading object
 */
export const processHeading = (element) => {
    const level = parseInt(element.tagName.charAt(1));
    const textRuns = processTextFormatting(element);

    return {
        type: CONTENT_TYPES.HEADING,
        level: level,
        textRuns: textRuns,
        text: element.textContent?.trim() || ''
    };
};

/**
 * Process paragraph element
 * @param {Element} element - Paragraph element
 * @returns {Object} Processed paragraph object
 */
export const processParagraph = (element) => {
    const textRuns = processTextFormatting(element);

    return {
        type: CONTENT_TYPES.PARAGRAPH,
        textRuns: textRuns,
        text: element.textContent?.trim() || ''
    };
};

/**
 * Process image element
 * @param {Element} element - Image element
 * @returns {Object} Processed image object
 */
export const processImage = (element) => {
    const src = element.getAttribute('src');
    const alt = element.getAttribute('alt') || '';
    const width = element.getAttribute('width');
    const height = element.getAttribute('height');

    return {
        type: CONTENT_TYPES.IMAGE,
        src: src,
        alt: alt,
        width: width ? parseInt(width) : null,
        height: height ? parseInt(height) : null,
        originalElement: element.outerHTML
    };
};

/**
 * Process list element (ul/ol)
 * @param {Element} element - List element
 * @returns {Object} Processed list object
 */
export const processList = (element) => {
    const isOrdered = element.tagName.toLowerCase() === 'ol';
    const listItems = [];

    const liElements = element.querySelectorAll('li');
    liElements.forEach(li => {
        const textRuns = processTextFormatting(li);
        listItems.push({
            type: CONTENT_TYPES.LIST_ITEM,
            textRuns: textRuns,
            text: li.textContent?.trim() || ''
        });
    });

    return {
        type: CONTENT_TYPES.LIST,
        ordered: isOrdered,
        items: listItems
    };
};

/**
 * Process text formatting within an element (bold, italic, etc.)
 * @param {Element} element - Element to process for text formatting
 * @returns {Array} Array of TextRun objects for DOCX
 */
export const processTextFormatting = (element) => {
    const textRuns = [];

    // Handle simple case - plain text
    if (element.children.length === 0) {
        const text = element.textContent?.trim();
        if (text) {
            textRuns.push({
                text: text,
                bold: false,
                italic: false,
                underline: false
            });
        }
        return textRuns;
    }

    // Process child nodes for formatting
    const processNode = (node) => {
        if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent?.trim();
            if (text) {
                textRuns.push({
                    text: text,
                    bold: false,
                    italic: false,
                    underline: false
                });
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const tagName = node.tagName.toLowerCase();
            const text = node.textContent?.trim();

            if (text) {
                textRuns.push({
                    text: text,
                    bold: tagName === 'strong' || tagName === 'b',
                    italic: tagName === 'em' || tagName === 'i',
                    underline: tagName === 'u'
                });
            }
        }
    };

    // Process all child nodes
    for (let i = 0; i < element.childNodes.length; i++) {
        processNode(element.childNodes[i]);
    }

    return textRuns;
};

/**
 * Simple markdown to HTML converter for basic markdown elements
 * @param {string} markdownContent - Markdown content to convert
 * @returns {string} HTML content
 */
export const convertMarkdownToHTML = (markdownContent) => {
    if (!markdownContent || typeof markdownContent !== 'string') {
        return '<p></p>';
    }

    try {
        let html = markdownContent;

        // Convert headings (# ## ### etc.)
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

        // Convert bold text (**text** or __text__)
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');

        // Convert italic text (*text* or _text_)
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
        html = html.replace(/_(.*?)_/g, '<em>$1</em>');

        // Convert images (![alt text](image-url))
        html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" />');

        // Convert links ([text](url))
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

        // Convert unordered lists (- item or * item)
        html = html.replace(/^\s*[-*]\s+(.*$)/gim, '<li>$1</li>');
        html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

        // Convert ordered lists (1. item)
        html = html.replace(/^\s*\d+\.\s+(.*$)/gim, '<li>$1</li>');

        // Fix nested lists (basic approach)
        html = html.replace(/<\/ul>\s*<ul>/g, '');
        html = html.replace(/<\/ol>\s*<ol>/g, '');

        // Convert line breaks to paragraphs
        const paragraphs = html.split(/\n\s*\n/);
        html = paragraphs
            .map(paragraph => {
                paragraph = paragraph.trim();
                if (!paragraph) return '';

                // Skip if already wrapped in HTML tags
                if (paragraph.startsWith('<') && paragraph.endsWith('>')) {
                    return paragraph;
                }

                // Skip if it's a list item
                if (paragraph.includes('<li>')) {
                    return paragraph;
                }

                return `<p>${paragraph}</p>`;
            })
            .filter(p => p)
            .join('');

        // Clean up any empty paragraphs
        html = html.replace(/<p>\s*<\/p>/g, '');

        // Ensure we have at least one paragraph
        if (!html.trim()) {
            html = '<p></p>';
        }

        return html;

    } catch (error) {
        console.error('Error converting markdown to HTML:', error);
        return '<p>Error loading content</p>';
    }
};

/**
 * Parse markdown content and convert to DOCX-compatible format
 * @param {string} markdownContent - Markdown content to process
 * @returns {Promise<Array>} Array of processed content objects
 */
export const parseMarkdownContent = async (markdownContent) => {
    if (!markdownContent || typeof markdownContent !== 'string') {
        return [];
    }

    try {
        // Try to use unified if available, otherwise fall back to simple conversion
        const hasUnified = await loadUnifiedDependencies();

        if (hasUnified) {
            // Convert markdown to HTML using unified
            const htmlResult = await unified()
                .use(remarkParse)
                .use(remarkRehype)
                .use(rehypeStringify)
                .process(markdownContent);

            const htmlContent = String(htmlResult);
            return parseHTMLContent(htmlContent);
        } else {
            // Fallback to simple markdown conversion
            const htmlContent = convertMarkdownToHTML(markdownContent);
            return parseHTMLContent(htmlContent);
        }
    } catch (error) {
        console.error('Error parsing markdown content:', error);
        // Fallback to simple conversion
        try {
            const htmlContent = convertMarkdownToHTML(markdownContent);
            return parseHTMLContent(htmlContent);
        } catch (fallbackError) {
            console.error('Fallback markdown processing also failed:', fallbackError);
            return [{
                type: CONTENT_TYPES.PARAGRAPH,
                text: 'Error processing markdown content',
                formatting: {}
            }];
        }
    }
};

/**
 * Extract images from HTML content
 * @param {string} htmlContent - HTML content to extract images from
 * @returns {Array} Array of image objects with src, alt, and metadata
 */
export const extractImagesFromHTML = (htmlContent) => {
    if (!htmlContent) return [];

    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const imgElements = doc.querySelectorAll('img');

        const images = [];
        imgElements.forEach((img, index) => {
            const src = img.getAttribute('src');
            if (src) {
                images.push({
                    src: src.trim(),
                    alt: img.getAttribute('alt') || `Image ${index + 1}`,
                    width: img.getAttribute('width'),
                    height: img.getAttribute('height'),
                    class: img.getAttribute('class'),
                    index: index,
                    type: 'html'
                });
            }
        });

        return images;
    } catch (error) {
        console.error('Error extracting images from HTML:', error);
        return [];
    }
};

/**
 * Extract images from markdown content
 * @param {string} markdownContent - Markdown content to extract images from
 * @returns {Array} Array of image objects with src, alt, and metadata
 */
export const extractImagesFromMarkdown = (markdownContent) => {
    if (!markdownContent) return [];

    try {
        const images = [];
        // Regex to match markdown image syntax: ![alt text](image-url)
        const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
        let match;
        let index = 0;

        while ((match = imageRegex.exec(markdownContent)) !== null) {
            const src = match[2].trim();
            if (src) {
                images.push({
                    src: src,
                    alt: match[1] || `Image ${index + 1}`,
                    markdownSyntax: match[0],
                    index: index,
                    type: 'markdown'
                });
            }
            index++;
        }

        return images;
    } catch (error) {
        console.error('Error extracting images from markdown:', error);
        return [];
    }
};

/**
 * Unified content processing pipeline that handles both HTML and markdown
 * @param {string} content - Content to process (HTML or markdown)
 * @param {string} contentType - Type of content ('html' or 'markdown')
 * @returns {Promise<Object>} Processed content with text and images
 */
export const processContent = async (content, contentType = 'html') => {
    if (!content) {
        return {
            processedContent: [],
            images: [],
            contentType: contentType
        };
    }

    try {
        let processedContent = [];
        let images = [];

        if (contentType === 'html') {
            processedContent = parseHTMLContent(content);
            images = extractImagesFromHTML(content);
        } else if (contentType === 'markdown') {
            processedContent = await parseMarkdownContent(content);
            images = extractImagesFromMarkdown(content);
        } else {
            throw new Error(`Unsupported content type: ${contentType}`);
        }

        return {
            processedContent,
            images,
            contentType,
            originalContent: content
        };
    } catch (error) {
        console.error('Error in unified content processing:', error);
        return {
            processedContent: [{
                type: CONTENT_TYPES.PARAGRAPH,
                text: 'Error processing content',
                formatting: {}
            }],
            images: [],
            contentType: contentType,
            error: error.message
        };
    }
};

/**
 * Convert processed content to DOCX Paragraph objects
 * @param {Array} processedContent - Array of processed content objects
 * @returns {Array} Array of DOCX Paragraph objects
 */
export const convertToDocxParagraphs = (processedContent) => {
    if (!Array.isArray(processedContent)) {
        return [];
    }

    const paragraphs = [];

    processedContent.forEach(content => {
        switch (content.type) {
            case CONTENT_TYPES.HEADING:
                paragraphs.push(createHeadingParagraph(content));
                break;

            case CONTENT_TYPES.PARAGRAPH:
                paragraphs.push(createTextParagraph(content));
                break;

            case CONTENT_TYPES.LIST:
                // Try enhanced list formatting first, fallback to basic if needed
                try {
                    paragraphs.push(...createListParagraphs(content));
                } catch (error) {
                    console.warn('Enhanced list formatting failed, using fallback:', error);
                    paragraphs.push(...createEnhancedListParagraphs(content));
                }
                break;

            case CONTENT_TYPES.IMAGE:
                // Images will be handled separately in image processing task
                paragraphs.push(createImagePlaceholderParagraph(content));
                break;

            default:
                // Fallback to paragraph
                paragraphs.push(createTextParagraph(content));
                break;
        }
    });

    return paragraphs;
};

/**
 * Create DOCX heading paragraph
 * @param {Object} content - Processed heading content
 * @returns {Paragraph} DOCX Paragraph object
 */
export const createHeadingParagraph = (content) => {
    const textRuns = content.textRuns?.map(run =>
        new TextRun({
            text: run.text,
            bold: run.bold,
            italics: run.italic,
            underline: run.underline ? {} : undefined
        })
    ) || [new TextRun(content.text || '')];

    return new Paragraph({
        children: textRuns,
        heading: HEADING_LEVELS[content.level] || HeadingLevel.HEADING_1,
        spacing: { after: 200 }
    });
};

/**
 * Create DOCX text paragraph
 * @param {Object} content - Processed paragraph content
 * @returns {Paragraph} DOCX Paragraph object
 */
export const createTextParagraph = (content) => {
    const textRuns = content.textRuns?.map(run =>
        new TextRun({
            text: run.text,
            bold: run.bold,
            italics: run.italic,
            underline: run.underline ? {} : undefined
        })
    ) || [new TextRun(content.text || '')];

    return new Paragraph({
        children: textRuns,
        spacing: { after: 120 }
    });
};

/**
 * Create DOCX list paragraphs with proper formatting and numbering
 * @param {Object} content - Processed list content
 * @returns {Array} Array of DOCX Paragraph objects
 */
export const createListParagraphs = (content) => {
    if (!content.items || !Array.isArray(content.items)) {
        return [];
    }

    return content.items.map((item, index) => {
        const textRuns = item.textRuns?.map(run =>
            new TextRun({
                text: run.text,
                bold: run.bold,
                italics: run.italic,
                underline: run.underline ? {} : undefined
            })
        ) || [new TextRun(item.text || '')];

        // Create proper list formatting
        if (content.ordered) {
            // Ordered list with proper numbering
            return new Paragraph({
                children: textRuns,
                numbering: {
                    reference: "ordered-list",
                    level: 0
                },
                spacing: {
                    after: 80,
                    before: 40
                },
                indent: {
                    left: 720,  // 0.5 inch indent
                    hanging: 360 // 0.25 inch hanging indent
                }
            });
        } else {
            // Unordered list with bullet points
            return new Paragraph({
                children: textRuns,
                numbering: {
                    reference: "bullet-list",
                    level: 0
                },
                spacing: {
                    after: 80,
                    before: 40
                },
                indent: {
                    left: 720,  // 0.5 inch indent
                    hanging: 360 // 0.25 inch hanging indent
                }
            });
        }
    });
};

/**
 * Create enhanced list paragraphs with fallback formatting
 * @param {Object} content - Processed list content
 * @returns {Array} Array of DOCX Paragraph objects
 */
export const createEnhancedListParagraphs = (content) => {
    if (!content.items || !Array.isArray(content.items)) {
        return [];
    }

    return content.items.map((item, index) => {
        const textRuns = item.textRuns?.map(run =>
            new TextRun({
                text: run.text,
                bold: run.bold,
                italics: run.italic,
                underline: run.underline ? {} : undefined
            })
        ) || [new TextRun(item.text || '')];

        // Add bullet or number prefix as fallback
        const prefix = content.ordered ? `${index + 1}. ` : '• ';
        const prefixRun = new TextRun({
            text: prefix,
            bold: false
        });

        return new Paragraph({
            children: [prefixRun, ...textRuns],
            spacing: {
                after: 80,
                before: 40
            },
            indent: {
                left: 720,  // 0.5 inch indent
                firstLine: -360 // Negative first line indent for hanging
            }
        });
    });
};

/**
 * Create image placeholder paragraph (actual image embedding handled in image processing task)
 * @param {Object} content - Processed image content
 * @returns {Paragraph} DOCX Paragraph object with image placeholder
 */
export const createImagePlaceholderParagraph = (content) => {
    return new Paragraph({
        children: [
            new TextRun({
                text: `[Image: ${content.alt || 'Image'}]`,
                italics: true,
                color: '666666'
            })
        ],
        spacing: { after: 200 }
    });
};

/**
 * Detect content type (HTML vs Markdown) based on content analysis
 * @param {string} content - Content to analyze
 * @returns {string} Detected content type ('html' or 'markdown')
 */
export const detectContentType = (content) => {
    if (!content || typeof content !== 'string') {
        return 'html';
    }

    // Check for HTML tags
    const htmlTagRegex = /<[^>]+>/;
    if (htmlTagRegex.test(content)) {
        return 'html';
    }

    // Check for markdown patterns
    const markdownPatterns = [
        /^#{1,6}\s+/m,  // Headers
        /\*\*.*?\*\*/,  // Bold
        /\*.*?\*/,      // Italic
        /!\[.*?\]\(.*?\)/, // Images
        /\[.*?\]\(.*?\)/, // Links
        /^[-*+]\s+/m,   // Unordered lists
        /^\d+\.\s+/m    // Ordered lists
    ];

    const hasMarkdownPatterns = markdownPatterns.some(pattern => pattern.test(content));
    return hasMarkdownPatterns ? 'markdown' : 'html';
};

/**
 * Image processing configuration
 */
export const IMAGE_CONFIG = {
    MAX_RETRIES: 3,
    RETRY_DELAY_BASE: 1000, // Base delay in milliseconds
    TIMEOUT: 10000, // 10 seconds timeout
    MAX_SIZE: 10 * 1024 * 1024, // 10MB max file size
    SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    DEFAULT_WIDTH: 600,
    DEFAULT_HEIGHT: 400
};

/**
 * Sleep utility for retry delays
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after the delay
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Validate image URL format and accessibility
 * @param {string} url - Image URL to validate
 * @returns {Object} Validation result with isValid and error message
 */
export const validateImageUrl = (url) => {
    if (!url || typeof url !== 'string') {
        return { isValid: false, error: 'Invalid URL: URL is empty or not a string' };
    }

    const trimmedUrl = url.trim();
    if (!trimmedUrl) {
        return { isValid: false, error: 'Invalid URL: URL is empty after trimming' };
    }

    // Check if URL is properly formatted
    try {
        const urlObj = new URL(trimmedUrl);

        // Check protocol
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
            return { isValid: false, error: 'Invalid URL: Only HTTP and HTTPS protocols are supported' };
        }

        // Check for common image file extensions
        const pathname = urlObj.pathname.toLowerCase();
        const hasImageExtension = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'].some(ext =>
            pathname.endsWith(ext)
        );

        // If no extension, we'll still try to download and validate by content type
        return {
            isValid: true,
            hasImageExtension,
            url: trimmedUrl
        };
    } catch (error) {
        return { isValid: false, error: `Invalid URL format: ${error.message}` };
    }
};

/**
 * Validate image format based on content type
 * @param {string} contentType - Content type from HTTP response
 * @returns {Object} Validation result with isValid and normalized format
 */
export const validateImageFormat = (contentType) => {
    if (!contentType) {
        return { isValid: false, error: 'No content type provided' };
    }

    const normalizedType = contentType.toLowerCase().split(';')[0].trim();

    if (IMAGE_CONFIG.SUPPORTED_FORMATS.includes(normalizedType)) {
        return {
            isValid: true,
            format: normalizedType,
            extension: getExtensionFromContentType(normalizedType)
        };
    }

    return {
        isValid: false,
        error: `Unsupported image format: ${normalizedType}. Supported formats: ${IMAGE_CONFIG.SUPPORTED_FORMATS.join(', ')}`
    };
};

/**
 * Get file extension from content type
 * @param {string} contentType - Content type
 * @returns {string} File extension
 */
export const getExtensionFromContentType = (contentType) => {
    const typeMap = {
        'image/jpeg': '.jpg',
        'image/jpg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp'
    };
    return typeMap[contentType] || '.jpg';
};

/**
 * Download image with comprehensive retry logic and error handling
 * @param {string} url - Image URL to download
 * @param {number} retryCount - Current retry attempt (internal use)
 * @returns {Promise<Object>} Download result with success status and data/error
 */
export const downloadImage = async (url, retryCount = 0) => {
    // Validate URL first
    const urlValidation = validateImageUrl(url);
    if (!urlValidation.isValid) {
        return {
            success: false,
            error: urlValidation.error,
            url: url,
            retryable: false
        };
    }

    try {
        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), IMAGE_CONFIG.TIMEOUT);

        const response = await fetch(urlValidation.url, {
            method: 'GET',
            signal: controller.signal,
            headers: {
                'Accept': 'image/*',
                'User-Agent': 'DocForge-AI/1.0',
                'Cache-Control': 'no-cache'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
            error.status = response.status;
            error.statusText = response.statusText;
            throw error;
        }

        // Validate content type
        const contentType = response.headers.get('content-type');
        const formatValidation = validateImageFormat(contentType);

        if (!formatValidation.isValid) {
            const error = new Error(formatValidation.error);
            error.retryable = false;
            throw error;
        }

        // Check content length
        const contentLength = response.headers.get('content-length');
        if (contentLength && parseInt(contentLength) > IMAGE_CONFIG.MAX_SIZE) {
            const error = new Error(`Image too large: ${contentLength} bytes (max: ${IMAGE_CONFIG.MAX_SIZE} bytes)`);
            error.retryable = false;
            throw error;
        }

        // Download the image data
        const arrayBuffer = await response.arrayBuffer();

        // Final size check
        if (arrayBuffer.byteLength > IMAGE_CONFIG.MAX_SIZE) {
            const error = new Error(`Image too large: ${arrayBuffer.byteLength} bytes (max: ${IMAGE_CONFIG.MAX_SIZE} bytes)`);
            error.retryable = false;
            throw error;
        }

        return {
            success: true,
            data: arrayBuffer,
            contentType: formatValidation.format,
            extension: formatValidation.extension,
            size: arrayBuffer.byteLength,
            url: urlValidation.url,
            attempts: retryCount + 1
        };

    } catch (error) {
        const isRetryable = shouldRetryImageDownload(error);

        console.warn(`Image download attempt ${retryCount + 1} failed for ${url}:`, error.message);

        // Check if we should retry
        if (retryCount < IMAGE_CONFIG.MAX_RETRIES - 1 && isRetryable) {
            // Calculate exponential backoff delay with jitter
            const baseDelay = IMAGE_CONFIG.RETRY_DELAY_BASE * Math.pow(2, retryCount);
            const jitter = Math.random() * 0.1 * baseDelay;
            const delay = Math.min(baseDelay + jitter, 10000); // Cap at 10 seconds

            console.log(`Retrying image download in ${Math.round(delay)}ms...`);

            await sleep(delay);
            return downloadImage(url, retryCount + 1);
        }

        // All retries exhausted or non-retryable error
        return {
            success: false,
            error: error.message,
            url: url,
            retryable: isRetryable,
            attempts: retryCount + 1,
            finalError: true
        };
    }
};

/**
 * Determine if an image download error should be retried
 * @param {Error} error - Error from image download attempt
 * @returns {boolean} Whether the error is retryable
 */
export const shouldRetryImageDownload = (error) => {
    // Don't retry if explicitly marked as non-retryable
    if (error.retryable === false) {
        return false;
    }

    // Don't retry client errors (4xx) except for specific cases
    if (error.status >= 400 && error.status < 500) {
        // Retry on 408 (timeout), 429 (rate limit), and 499 (client closed)
        return [408, 429, 499].includes(error.status);
    }

    // Retry on server errors (5xx)
    if (error.status >= 500) {
        return true;
    }

    // Retry on network errors
    if (error.name === 'AbortError' || error.name === 'TimeoutError') {
        return true;
    }

    // Retry on fetch errors (network issues)
    if (error.message.includes('fetch') || error.message.includes('network')) {
        return true;
    }

    // Don't retry validation errors, format errors, or size errors
    if (error.message.includes('format') ||
        error.message.includes('too large') ||
        error.message.includes('Invalid URL')) {
        return false;
    }

    // Default to retryable for unknown errors
    return true;
};

/**
 * Categorize error types for better error handling
 * @param {Error} error - Error to categorize
 * @returns {string} Error category
 */
export const categorizeError = (error) => {
    if (error.name === 'AbortError' || error.name === 'TimeoutError') {
        return 'timeout';
    }

    if (error.status) {
        if (error.status >= 400 && error.status < 500) {
            return 'client_error';
        }
        if (error.status >= 500) {
            return 'server_error';
        }
    }

    if (error.message.includes('network') || error.message.includes('fetch')) {
        return 'network_error';
    }

    if (error.message.includes('format') || error.message.includes('Invalid URL')) {
        return 'validation_error';
    }

    if (error.message.includes('too large')) {
        return 'size_error';
    }

    return 'unknown_error';
};

/**
 * Create user-friendly error messages based on error type
 * @param {Error} error - Error to create message for
 * @returns {string} User-friendly error message
 */
export const createUserFriendlyErrorMessage = (error) => {
    const errorType = categorizeError(error);

    switch (errorType) {
        case 'timeout':
            return 'Image download timed out. The image server may be slow or unresponsive.';

        case 'client_error':
            if (error.status === 404) {
                return 'Image not found. The image may have been moved or deleted.';
            }
            if (error.status === 403) {
                return 'Access denied. The image server is blocking access to this image.';
            }
            if (error.status === 429) {
                return 'Too many requests. The image server is rate limiting downloads.';
            }
            return `Image server error (${error.status}). The image cannot be accessed.`;

        case 'server_error':
            return 'Image server is experiencing problems. Please try again later.';

        case 'network_error':
            return 'Network connection problem. Please check your internet connection.';

        case 'validation_error':
            return 'Invalid image URL or unsupported image format.';

        case 'size_error':
            return 'Image is too large to embed in the document.';

        default:
            return `Image download failed: ${error.message}`;
    }
};

/**
 * Determine if an error is retryable based on comprehensive error analysis
 * @param {Error} error - Error to analyze
 * @returns {boolean} Whether the error should be retried
 */
export const isRetryableError = (error) => {
    const errorType = categorizeError(error);

    // Never retry validation or size errors
    if (errorType === 'validation_error' || errorType === 'size_error') {
        return false;
    }

    // Don't retry most client errors except specific ones
    if (errorType === 'client_error') {
        return [408, 429, 499].includes(error.status);
    }

    // Retry server errors, network errors, and timeouts
    return ['server_error', 'network_error', 'timeout', 'unknown_error'].includes(errorType);
};

/**
 * Process multiple images with concurrent downloads and error handling
 * @param {Array} images - Array of image objects with src, alt, etc.
 * @param {number} concurrency - Maximum concurrent downloads (default: 3)
 * @returns {Promise<Object>} Processing result with successful and failed downloads
 */
export const processImages = async (images, concurrency = 3) => {
    if (!Array.isArray(images) || images.length === 0) {
        return {
            success: true,
            processedImages: [],
            failedImages: [],
            totalImages: 0
        };
    }

    const processedImages = [];
    const failedImages = [];
    const downloadPromises = [];

    // Process images in batches to control concurrency
    for (let i = 0; i < images.length; i += concurrency) {
        const batch = images.slice(i, i + concurrency);

        const batchPromises = batch.map(async (image) => {
            const downloadResult = await downloadImage(image.src);

            if (downloadResult.success) {
                processedImages.push({
                    ...image,
                    data: downloadResult.data,
                    contentType: downloadResult.contentType,
                    extension: downloadResult.extension,
                    size: downloadResult.size,
                    downloadSuccess: true
                });
            } else {
                failedImages.push({
                    ...image,
                    error: downloadResult.error,
                    downloadSuccess: false
                });
            }
        });

        // Wait for current batch to complete before starting next batch
        await Promise.all(batchPromises);
    }

    return {
        success: failedImages.length === 0,
        processedImages,
        failedImages,
        totalImages: images.length,
        successCount: processedImages.length,
        failureCount: failedImages.length
    };
};

/**
 * Convert image data to DOCX-compatible format
 * @param {ArrayBuffer} imageData - Raw image data
 * @param {string} contentType - Image content type
 * @returns {Promise<Object>} Conversion result with converted data or error
 */
export const convertImageForDocx = async (imageData, contentType) => {
    try {
        // For now, we'll pass through supported formats as-is
        // In the future, we could add image conversion/compression here
        const supportedFormats = ['image/jpeg', 'image/png'];

        if (supportedFormats.includes(contentType)) {
            return {
                success: true,
                data: imageData,
                contentType: contentType
            };
        }

        // For unsupported formats, we'll need to convert them
        // This is a placeholder for future image conversion functionality
        console.warn(`Image format ${contentType} may not be fully supported in DOCX`);

        return {
            success: true,
            data: imageData,
            contentType: contentType,
            warning: `Format ${contentType} may not display correctly in all DOCX viewers`
        };

    } catch (error) {
        return {
            success: false,
            error: `Image conversion failed: ${error.message}`
        };
    }
};

/**
 * Extract and process all images from content (both HTML and markdown)
 * @param {string} content - Content to extract images from
 * @param {string} contentType - Type of content ('html' or 'markdown')
 * @returns {Promise<Object>} Complete image processing result
 */
export const extractAndProcessImages = async (content, contentType = 'html') => {
    try {
        // Extract images based on content type
        let extractedImages = [];

        if (contentType === 'html') {
            extractedImages = extractImagesFromHTML(content);
        } else if (contentType === 'markdown') {
            extractedImages = extractImagesFromMarkdown(content);
        } else {
            // Try both extraction methods
            const htmlImages = extractImagesFromHTML(content);
            const markdownImages = extractImagesFromMarkdown(content);
            extractedImages = [...htmlImages, ...markdownImages];
        }

        if (extractedImages.length === 0) {
            return {
                success: true,
                images: [],
                processedImages: [],
                failedImages: [],
                totalImages: 0,
                message: 'No images found in content'
            };
        }

        // Process (download) all extracted images
        const processingResult = await processImages(extractedImages);

        return {
            success: processingResult.success,
            images: extractedImages,
            processedImages: processingResult.processedImages,
            failedImages: processingResult.failedImages,
            totalImages: processingResult.totalImages,
            successCount: processingResult.successCount,
            failureCount: processingResult.failureCount,
            contentType: contentType
        };

    } catch (error) {
        console.error('Error in extractAndProcessImages:', error);
        return {
            success: false,
            error: error.message,
            images: [],
            processedImages: [],
            failedImages: [],
            totalImages: 0
        };
    }
};

// Export all functions for easy importing
export default {
    parseHTMLContent,
    parseMarkdownContent,
    convertMarkdownToHTML,
    processContent,
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    convertToDocxParagraphs,
    detectContentType,
    validateImageUrl,
    validateImageFormat,
    downloadImage,
    processImages,
    convertImageForDocx,
    extractAndProcessImages,
    CONTENT_TYPES,
    HEADING_LEVELS,
    IMAGE_CONFIG
};

/**
 * Process individual image with comprehensive error handling
 * @param {Object} image - Image object to process
 * @param {number} index - Image index for tracking
 * @returns {Promise<Object>} Processing result for the image
 */
export const processImageWithErrorHandling = async (image, index) => {
    try {
        if (!image.src) {
            return {
                success: false,
                error: 'Image source URL is missing',
                src: image.src,
                alt: image.alt,
                index
            };
        }

        console.log(`Processing image ${index + 1}: ${image.src}`);

        const downloadResult = await downloadImage(image.src);

        if (downloadResult.success) {
            return {
                success: true,
                downloadSuccess: true,
                data: downloadResult.data,
                src: image.src,
                alt: image.alt || `Image ${index + 1}`,
                contentType: downloadResult.contentType,
                extension: downloadResult.extension,
                size: downloadResult.size,
                width: image.width,
                height: image.height,
                class: image.class,
                index,
                attempts: downloadResult.attempts
            };
        } else {
            return {
                success: false,
                downloadSuccess: false,
                error: downloadResult.error,
                src: image.src,
                alt: image.alt || `Image ${index + 1}`,
                index,
                attempts: downloadResult.attempts,
                retryable: downloadResult.retryable
            };
        }

    } catch (error) {
        console.error(`Error processing image ${index + 1}:`, error);
        return {
            success: false,
            error: error.message,
            src: image.src,
            alt: image.alt,
            index
        };
    }
};

/**
 * Create graceful error messages for different failure scenarios
 * @param {Array} failedImages - Array of failed image processing results
 * @returns {Object} Categorized error messages
 */
export const createImageErrorSummary = (failedImages) => {
    const summary = {
        networkErrors: [],
        formatErrors: [],
        sizeErrors: [],
        unknownErrors: [],
        totalFailed: failedImages.length
    };

    failedImages.forEach(failed => {
        const error = failed.error || '';
        const lowerError = error.toLowerCase();

        if (lowerError.includes('timeout') ||
            lowerError.includes('network') ||
            lowerError.includes('fetch') ||
            lowerError.includes('cors')) {
            summary.networkErrors.push(failed);
        } else if (lowerError.includes('format') ||
            lowerError.includes('unsupported')) {
            summary.formatErrors.push(failed);
        } else if (lowerError.includes('too large') ||
            lowerError.includes('size')) {
            summary.sizeErrors.push(failed);
        } else {
            summary.unknownErrors.push(failed);
        }
    });

    return summary;
};

/**
 * Generate user-friendly error message for image processing failures
 * @param {Object} imageStats - Image processing statistics
 * @returns {string} User-friendly error message
 */
export const generateImageErrorMessage = (imageStats) => {
    if (!imageStats || imageStats.failureCount === 0) {
        return '';
    }

    const { totalImages, successCount, failureCount, failedImages } = imageStats;

    if (failureCount === totalImages) {
        return `All ${totalImages} images failed to process. The document will be generated without images.`;
    }

    const errorSummary = createImageErrorSummary(failedImages);
    let message = `${failureCount} of ${totalImages} images could not be processed. `;

    const errorDetails = [];
    if (errorSummary.networkErrors.length > 0) {
        errorDetails.push(`${errorSummary.networkErrors.length} due to network issues`);
    }
    if (errorSummary.formatErrors.length > 0) {
        errorDetails.push(`${errorSummary.formatErrors.length} due to unsupported formats`);
    }
    if (errorSummary.sizeErrors.length > 0) {
        errorDetails.push(`${errorSummary.sizeErrors.length} due to size limitations`);
    }
    if (errorSummary.unknownErrors.length > 0) {
        errorDetails.push(`${errorSummary.unknownErrors.length} due to other issues`);
    }

    if (errorDetails.length > 0) {
        message += `Failures: ${errorDetails.join(', ')}.`;
    }

    message += ` The document will be generated with ${successCount} images and placeholders for failed images.`;

    return message;
};

/**
 * Validate document data before processing
 * @param {Object} documentData - Document metadata to validate
 * @returns {Object} Validation result
 */
// First implementation removed to fix duplicate declaration
const validateDocumentDataLegacy = (documentData) => {
    const errors = [];
    const warnings = [];

    if (!documentData || typeof documentData !== 'object') {
        errors.push('Document data is required and must be an object');
        return { isValid: false, errors, warnings };
    }

    // Check required fields
    if (!documentData.title || typeof documentData.title !== 'string') {
        warnings.push('Document title is missing or invalid, using default');
    }

    if (!documentData.author || typeof documentData.author !== 'string') {
        warnings.push('Document author is missing or invalid, using default');
    }

    // Validate title length
    if (documentData.title && documentData.title.length > 200) {
        warnings.push('Document title is very long and may be truncated');
    }

    // Validate description length
    if (documentData.description && documentData.description.length > 1000) {
        warnings.push('Document description is very long and may be truncated');
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
};

/**
 * Validate content before processing
 * @param {string} content - Content to validate
 * @param {string} contentType - Expected content type
 * @returns {Object} Validation result
 */
const validateContentLegacy = (content, contentType = 'html') => {
    const errors = [];
    const warnings = [];

    if (!content) {
        errors.push('Content is required');
        return { isValid: false, errors, warnings };
    }

    if (typeof content !== 'string') {
        errors.push('Content must be a string');
        return { isValid: false, errors, warnings };
    }

    if (content.trim().length === 0) {
        warnings.push('Content appears to be empty');
    }

    // Content length check
    if (content.length > 1000000) { // 1MB limit
        warnings.push('Content is very large and may take longer to process');
    }

    // Basic format validation
    if (contentType === 'html') {
        // Check for basic HTML structure
        if (!content.includes('<') && !content.includes('>')) {
            warnings.push('Content does not appear to contain HTML tags');
        }
    } else if (contentType === 'markdown') {
        // Check for basic markdown patterns
        const hasMarkdownPatterns = [
            /^#{1,6}\s+/m,  // Headers
            /\*\*.*?\*\*/,  // Bold
            /\*.*?\*/,      // Italic
            /!\[.*?\]\(.*?\)/, // Images
            /\[.*?\]\(.*?\)/, // Links
        ].some(pattern => pattern.test(content));

        if (!hasMarkdownPatterns) {
            warnings.push('Content does not appear to contain markdown formatting');
        }
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
};

/**
 * Enhanced content processing with comprehensive error handling
 * @param {string} content - Content to process
 * @param {string} contentType - Type of content ('html' or 'markdown')
 * @returns {Promise<Object>} Enhanced processing result with error handling
 */
// First implementation removed to fix duplicate declaration
const processContentWithErrorHandlingLegacy = async (content, contentType = 'html') => {
    const result = {
        success: false,
        processedContent: [],
        images: [],
        contentType,
        errors: [],
        warnings: []
    };

    try {
        // Validate input
        const validation = validateContentLegacy(content, contentType);
        if (!validation.isValid) {
            result.errors.push(...validation.errors);
            return result;
        }
        result.warnings.push(...validation.warnings);

        // Process content with fallback
        let processedContent = [];
        let images = [];

        try {
            if (contentType === 'html') {
                processedContent = parseHTMLContent(content);
                images = extractImagesFromHTML(content);
            } else if (contentType === 'markdown') {
                processedContent = await parseMarkdownContent(content);
                images = extractImagesFromMarkdown(content);
            } else {
                throw new Error(`Unsupported content type: ${contentType}`);
            }
        } catch (processingError) {
            console.warn('Primary content processing failed, attempting fallback:', processingError.message);

            // Fallback to simple text processing
            processedContent = [{
                type: CONTENT_TYPES.PARAGRAPH,
                text: content.substring(0, 10000), // Limit to prevent memory issues
                formatting: {}
            }];
            images = [];
            result.warnings.push('Content processing failed, using simplified text format');
        }

        result.success = true;
        result.processedContent = processedContent;
        result.images = images;
        result.originalContent = content;

        return result;

    } catch (error) {
        console.error('Error in processContentWithErrorHandling:', error);
        result.errors.push(error.message);

        // Provide minimal fallback content
        result.processedContent = [{
            type: CONTENT_TYPES.PARAGRAPH,
            text: 'Error processing content',
            formatting: {}
        }];

        return result;
    }
};
/*
*
 * Enhanced content processing with comprehensive error handling
 * @param {string} content - Content to process
 * @param {string} contentType - Type of content ('html' or 'markdown')
 * @returns {Promise<Object>} Processing result with detailed error information
 */
export const processContentWithErrorHandling = async (content, contentType = 'html') => {
    const result = {
        success: false,
        processedContent: [],
        images: [],
        errors: [],
        warnings: [],
        contentType: contentType,
        originalContent: content
    };

    try {
        // Validate input
        if (!content || typeof content !== 'string') {
            result.errors.push('Invalid content: Content must be a non-empty string');
            return result;
        }

        if (!['html', 'markdown'].includes(contentType)) {
            result.warnings.push(`Unknown content type '${contentType}', defaulting to HTML processing`);
            contentType = 'html';
        }

        // Process content with error handling
        let processedContent = [];
        let images = [];

        try {
            if (contentType === 'html') {
                processedContent = parseHTMLContent(content);
                images = extractImagesFromHTML(content);
            } else {
                processedContent = await parseMarkdownContent(content);
                images = extractImagesFromMarkdown(content);
            }
        } catch (processingError) {
            result.errors.push(`Content processing failed: ${processingError.message}`);

            // Provide fallback content
            processedContent = [{
                type: CONTENT_TYPES.PARAGRAPH,
                text: content.substring(0, 1000) + (content.length > 1000 ? '...' : ''),
                formatting: {}
            }];
        }

        // Validate processed content
        if (!Array.isArray(processedContent) || processedContent.length === 0) {
            result.warnings.push('No content was processed, creating fallback paragraph');
            processedContent = [{
                type: CONTENT_TYPES.PARAGRAPH,
                text: 'No content available',
                formatting: {}
            }];
        }

        result.success = true;
        result.processedContent = processedContent;
        result.images = images;
        result.originalContent = content;

        return result;

    } catch (error) {
        console.error('Error in processContentWithErrorHandling:', error);
        result.errors.push(error.message);

        // Provide minimal fallback content
        result.processedContent = [{
            type: CONTENT_TYPES.PARAGRAPH,
            text: 'Error processing content',
            formatting: {}
        }];

        return result;
    }
};

/**
 * Enhanced image processing with comprehensive error handling and retry logic
 * @param {Array} images - Array of image objects to process
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} Processing result with detailed error information
 */
export const processImagesWithErrorHandling = async (images, options = {}) => {
    const {
        concurrency = 3,
        maxRetries = IMAGE_CONFIG.MAX_RETRIES,
        timeout = IMAGE_CONFIG.TIMEOUT,
        skipOnError = true
    } = options;

    const result = {
        success: false,
        processedImages: [],
        failedImages: [],
        errors: [],
        warnings: [],
        totalImages: 0,
        successCount: 0,
        failureCount: 0,
        processingTime: 0
    };

    const startTime = Date.now();

    try {
        if (!Array.isArray(images)) {
            result.errors.push('Invalid input: images must be an array');
            return result;
        }

        if (images.length === 0) {
            result.success = true;
            result.warnings.push('No images to process');
            return result;
        }

        result.totalImages = images.length;

        // Validate all image URLs first
        const validImages = [];
        const invalidImages = [];

        images.forEach((image, index) => {
            const validation = validateImageUrl(image.src);
            if (validation.isValid) {
                validImages.push({ ...image, index });
            } else {
                invalidImages.push({
                    ...image,
                    index,
                    error: validation.error,
                    errorType: 'validation_error'
                });
            }
        });

        // Add invalid images to failed list
        result.failedImages.push(...invalidImages);
        result.failureCount += invalidImages.length;

        if (invalidImages.length > 0) {
            result.warnings.push(`${invalidImages.length} images have invalid URLs and will be skipped`);
        }

        if (validImages.length === 0) {
            result.errors.push('No valid images to process');
            result.processingTime = Date.now() - startTime;
            return result;
        }

        // Process valid images in batches
        const processedImages = [];
        const failedImages = [];

        for (let i = 0; i < validImages.length; i += concurrency) {
            const batch = validImages.slice(i, i + concurrency);

            const batchPromises = batch.map(async (image) => {
                try {
                    const downloadResult = await downloadImageWithRetry(image.src, {
                        maxRetries,
                        timeout
                    });

                    if (downloadResult.success) {
                        processedImages.push({
                            ...image,
                            data: downloadResult.data,
                            contentType: downloadResult.contentType,
                            extension: downloadResult.extension,
                            size: downloadResult.size,
                            downloadSuccess: true,
                            attempts: downloadResult.attempts
                        });
                    } else {
                        failedImages.push({
                            ...image,
                            error: downloadResult.error,
                            errorType: downloadResult.errorType,
                            downloadSuccess: false,
                            attempts: downloadResult.attempts,
                            retryable: downloadResult.retryable
                        });
                    }
                } catch (error) {
                    failedImages.push({
                        ...image,
                        error: `Unexpected error: ${error.message}`,
                        errorType: 'unknown_error',
                        downloadSuccess: false,
                        attempts: 1
                    });
                }
            });

            // Wait for current batch to complete
            await Promise.all(batchPromises);
        }

        result.processedImages = processedImages;
        result.failedImages.push(...failedImages);
        result.successCount = processedImages.length;
        result.failureCount = result.failedImages.length;
        result.processingTime = Date.now() - startTime;

        // Determine overall success
        if (skipOnError) {
            result.success = processedImages.length > 0;
            if (failedImages.length > 0) {
                result.warnings.push(`${failedImages.length} images failed to download but processing continued`);
            }
        } else {
            result.success = failedImages.length === 0;
            if (failedImages.length > 0) {
                result.errors.push(`${failedImages.length} images failed to download`);
            }
        }

        return result;

    } catch (error) {
        console.error('Error in processImagesWithErrorHandling:', error);
        result.errors.push(`Image processing failed: ${error.message}`);
        result.processingTime = Date.now() - startTime;
        return result;
    }
};

/**
 * Download image with enhanced retry logic and error handling
 * @param {string} url - Image URL to download
 * @param {Object} options - Download options
 * @returns {Promise<Object>} Download result with comprehensive error information
 */
export const downloadImageWithRetry = async (url, options = {}) => {
    const {
        maxRetries = IMAGE_CONFIG.MAX_RETRIES,
        timeout = IMAGE_CONFIG.TIMEOUT,
        retryDelayBase = IMAGE_CONFIG.RETRY_DELAY_BASE
    } = options;

    let lastError = null;
    let attempts = 0;

    for (let retryCount = 0; retryCount <= maxRetries; retryCount++) {
        attempts++;

        try {
            const result = await downloadImage(url, retryCount);

            if (result.success) {
                return {
                    ...result,
                    attempts
                };
            }

            lastError = result;

            // Don't retry if explicitly marked as non-retryable
            if (!result.retryable) {
                break;
            }

            // Don't retry on last attempt
            if (retryCount === maxRetries) {
                break;
            }

            // Calculate delay for next retry
            const delay = retryDelayBase * Math.pow(2, retryCount);
            const jitter = Math.random() * 0.1 * delay;
            const totalDelay = Math.min(delay + jitter, 10000); // Cap at 10 seconds

            console.log(`Retrying image download (${retryCount + 1}/${maxRetries}) in ${Math.round(totalDelay)}ms: ${url}`);
            await sleep(totalDelay);

        } catch (error) {
            lastError = {
                success: false,
                error: error.message,
                errorType: categorizeError(error),
                retryable: isRetryableError(error),
                url
            };

            if (!lastError.retryable || retryCount === maxRetries) {
                break;
            }
        }
    }

    // All retries exhausted
    return {
        success: false,
        error: createUserFriendlyErrorMessage(lastError),
        originalError: lastError?.error || 'Unknown error',
        errorType: lastError?.errorType || 'unknown_error',
        retryable: lastError?.retryable || false,
        attempts,
        url
    };
};

/**
 * This function has been moved to errorHandlingService.js to avoid duplication
 * @see errorHandlingService.js for the implementation
 */
// Using the imported validateDocumentData from errorHandlingService.js
const validateDocumentDataExample = (documentData) => {
    const result = {
        isValid: true,
        sanitizedData: {},
        errors: [],
        warnings: []
    };

    try {
        if (!documentData || typeof documentData !== 'object') {
            result.isValid = false;
            result.errors.push('Document data must be an object');
            return result;
        }

        // Sanitize and validate title
        const title = documentData.title;
        if (title && typeof title === 'string') {
            const sanitizedTitle = title.trim().substring(0, 255);
            if (sanitizedTitle.length === 0) {
                result.warnings.push('Document title is empty after sanitization');
                result.sanitizedData.title = 'Untitled Document';
            } else {
                result.sanitizedData.title = sanitizedTitle;
                if (sanitizedTitle.length < title.length) {
                    result.warnings.push('Document title was truncated to 255 characters');
                }
            }
        } else {
            result.sanitizedData.title = 'Untitled Document';
            if (title) {
                result.warnings.push('Invalid title format, using default');
            }
        }

        // Sanitize and validate author
        const author = documentData.author;
        if (author && typeof author === 'string') {
            const sanitizedAuthor = author.trim().substring(0, 100);
            if (sanitizedAuthor.length > 0) {
                result.sanitizedData.author = sanitizedAuthor;
                if (sanitizedAuthor.length < author.length) {
                    result.warnings.push('Author name was truncated to 100 characters');
                }
            } else {
                result.sanitizedData.author = 'Unknown Author';
                result.warnings.push('Author name is empty after sanitization');
            }
        } else {
            result.sanitizedData.author = 'Unknown Author';
            if (author) {
                result.warnings.push('Invalid author format, using default');
            }
        }

        // Sanitize and validate description
        const description = documentData.description;
        if (description && typeof description === 'string') {
            const sanitizedDescription = description.trim().substring(0, 500);
            if (sanitizedDescription.length > 0) {
                result.sanitizedData.description = sanitizedDescription;
                if (sanitizedDescription.length < description.length) {
                    result.warnings.push('Description was truncated to 500 characters');
                }
            } else {
                result.sanitizedData.description = '';
            }
        } else {
            result.sanitizedData.description = '';
            if (description) {
                result.warnings.push('Invalid description format, using empty string');
            }
        }

        return result;

    } catch (error) {
        console.error('Error validating document data:', error);
        result.isValid = false;
        result.errors.push(`Validation failed: ${error.message}`);
        return result;
    }
};

/**
 * Create comprehensive error summary for user display
 * @param {Array} errors - Array of error messages
 * @param {Array} warnings - Array of warning messages
 * @param {Object} context - Additional context information
 * @returns {Object} Error summary with user-friendly messages
 */
export const createErrorSummary = (errors = [], warnings = [], context = {}) => {
    const summary = {
        hasErrors: errors.length > 0,
        hasWarnings: warnings.length > 0,
        errorCount: errors.length,
        warningCount: warnings.length,
        userMessage: '',
        technicalDetails: {
            errors,
            warnings,
            context
        }
    };

    // Create user-friendly message
    if (summary.hasErrors) {
        if (errors.length === 1) {
            summary.userMessage = `Export failed: ${errors[0]}`;
        } else {
            summary.userMessage = `Export failed with ${errors.length} errors. Please check the document and try again.`;
        }
    } else if (summary.hasWarnings) {
        if (warnings.length === 1) {
            summary.userMessage = `Export completed with a warning: ${warnings[0]}`;
        } else {
            summary.userMessage = `Export completed with ${warnings.length} warnings. The document was generated but some issues were encountered.`;
        }
    } else {
        summary.userMessage = 'Export completed successfully.';
    }

    return summary;
};

/**
 * Validate content before processing
 * @param {string} content - Content to validate
 * @param {string} contentType - Type of content
 * @returns {Object} Validation result
 */
export const validateContent = (content, contentType) => {
    const result = {
        isValid: true,
        errors: [],
        warnings: []
    };

    if (!content || typeof content !== 'string') {
        result.isValid = false;
        result.errors.push('Content must be a non-empty string');
        return result;
    }

    if (content.trim().length === 0) {
        result.isValid = false;
        result.errors.push('Content cannot be empty');
        return result;
    }

    if (!['html', 'markdown'].includes(contentType)) {
        result.warnings.push(`Unknown content type '${contentType}', will attempt auto-detection`);
    }

    return result;
};

/**
 * Generate user-friendly error message for image processing failures
 * @param {Array} failedImages - Array of failed image objects
 * @returns {string} User-friendly error message
 */
const generateImageErrorMessageLegacy = (failedImages) => {
    if (!failedImages || failedImages.length === 0) {
        return '';
    }

    if (failedImages.length === 1) {
        const image = failedImages[0];
        return `Image "${image.alt || 'Image'}" could not be downloaded: ${createUserFriendlyErrorMessage({ message: image.error })}`;
    }

    const errorTypes = {};
    failedImages.forEach(image => {
        const errorType = categorizeError({ message: image.error });
        errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
    });

    const errorSummary = Object.entries(errorTypes)
        .map(([type, count]) => `${count} ${type.replace('_', ' ')} error${count > 1 ? 's' : ''}`)
        .join(', ');

    return `${failedImages.length} images could not be downloaded (${errorSummary}). The document was generated with placeholders for missing images.`;
};