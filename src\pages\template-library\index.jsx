import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import FilterSidebar from './components/FilterSidebar';
import SearchBar from './components/SearchBar';
import CategoryTabs from './components/CategoryTabs';
import FeaturedCarousel from './components/FeaturedCarousel';
import TemplateGrid from './components/TemplateGrid';
import TemplatePreviewModal from './components/TemplatePreviewModal';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const TemplateLibrary = () => {
  const navigate = useNavigate();
  const { contentMargin } = useSidebar();
  const [isFilterCollapsed, setIsFilterCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [filters, setFilters] = useState({});
  const [sortBy, setSortBy] = useState('popular');
  const [templates, setTemplates] = useState([]);
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Mock data for templates
  const mockTemplates = [
    {
      id: 1,
      title: "Professional Business Plan Template",
      category: "Business Documents",
      description: "Comprehensive business plan template with financial projections, market analysis, and executive summary sections.",
      thumbnail: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop",
      formats: ["PDF", "DOCX", "EPUB"],
      usageCount: 15420,
      rating: 4.8,
      estimatedTime: "2-3 hours",
      complexity: "intermediate",
      language: "English",
      credits: 5,
      isNew: false,
      isPremium: true,
      isFavorite: false,
      industry: "business",
      documentType: "business",
      features: [
        "Executive summary template",
        "Financial projection tables",
        "Market analysis framework",
        "Competitive analysis section"
      ],
      samplePages: [
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop",
        "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=600&fit=crop",
        "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=400&h=600&fit=crop"
      ]
    },
    {
      id: 2,
      title: "Academic Research Paper Format",
      category: "Academic Documents",
      description: "APA-style research paper template with proper citations, bibliography, and academic formatting standards.",
      thumbnail: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=400&fit=crop",
      formats: ["PDF", "DOCX"],
      usageCount: 28350,
      rating: 4.9,
      estimatedTime: "1-2 hours",
      complexity: "advanced",
      language: "English",
      credits: 3,
      isNew: true,
      isPremium: false,
      isFavorite: true,
      industry: "education",
      documentType: "academic",
      features: [
        "APA formatting guidelines",
        "Citation templates",
        "Bibliography automation",
        "Abstract and keywords section"
      ]
    },
    {
      id: 3,
      title: "Modern eBook Template",
      category: "eBooks",
      description: "Clean and modern eBook template with chapter layouts, table of contents, and interactive elements.",
      thumbnail: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=400&fit=crop",
      formats: ["EPUB", "MOBI", "PDF"],
      usageCount: 12890,
      rating: 4.7,
      estimatedTime: "3-4 hours",
      complexity: "beginner",
      language: "English",
      credits: 0,
      isNew: false,
      isPremium: false,
      isFavorite: false,
      industry: "publishing",
      documentType: "ebook",
      features: [
        "Interactive table of contents",
        "Chapter templates",
        "Image placeholders",
        "Mobile-responsive design"
      ]
    },
    {
      id: 4,
      title: "Marketing Proposal Template",
      category: "Business Documents",
      description: "Professional marketing proposal template with campaign strategies, budget breakdowns, and timeline planning.",
      thumbnail: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop",
      formats: ["PDF", "DOCX"],
      usageCount: 9876,
      rating: 4.6,
      estimatedTime: "2-3 hours",
      complexity: "intermediate",
      language: "English",
      credits: 4,
      isNew: false,
      isPremium: true,
      isFavorite: false,
      industry: "marketing",
      documentType: "business"
    },
    {
      id: 5,
      title: "Technical Documentation Template",
      category: "Business Documents",
      description: "Comprehensive technical documentation template with API references, code examples, and user guides.",
      thumbnail: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop",
      formats: ["PDF", "DOCX"],
      usageCount: 7654,
      rating: 4.8,
      estimatedTime: "4-5 hours",
      complexity: "advanced",
      language: "English",
      credits: 6,
      isNew: true,
      isPremium: true,
      isFavorite: false,
      industry: "technology",
      documentType: "business"
    },
    {
      id: 6,
      title: "Thesis Dissertation Template",
      category: "Academic Documents",
      description: "Complete thesis template with chapters, appendices, and academic formatting for graduate students.",
      thumbnail: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&h=400&fit=crop",
      formats: ["PDF", "DOCX"],
      usageCount: 18765,
      rating: 4.9,
      estimatedTime: "5-6 hours",
      complexity: "advanced",
      language: "English",
      credits: 8,
      isNew: false,
      isPremium: true,
      isFavorite: true,
      industry: "education",
      documentType: "academic"
    },
    {
      id: 7,
      title: "Creative Writing eBook",
      category: "eBooks",
      description: "Artistic eBook template perfect for novels, poetry collections, and creative writing projects.",
      thumbnail: "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=800&h=400&fit=crop",
      formats: ["EPUB", "MOBI", "PDF"],
      usageCount: 5432,
      rating: 4.5,
      estimatedTime: "2-3 hours",
      complexity: "beginner",
      language: "English",
      credits: 0,
      isNew: false,
      isPremium: false,
      isFavorite: false,
      industry: "publishing",
      documentType: "ebook"
    },
    {
      id: 8,
      title: "Financial Report Template",
      category: "Business Documents",
      description: "Professional financial report template with charts, graphs, and comprehensive analysis sections.",
      thumbnail: "https://images.unsplash.com/photo-1554224154-26032fced8bd?w=400&h=300&fit=crop",
      bannerImage: "https://images.unsplash.com/photo-1554224154-26032fced8bd?w=800&h=400&fit=crop",
      formats: ["PDF", "DOCX"],
      usageCount: 11234,
      rating: 4.7,
      estimatedTime: "3-4 hours",
      complexity: "intermediate",
      language: "English",
      credits: 5,
      isNew: false,
      isPremium: true,
      isFavorite: false,
      industry: "finance",
      documentType: "business"
    }
  ];

  const categories = [
    { key: 'all', label: 'All Templates', icon: 'Grid3x3', count: mockTemplates.length },
    { key: 'ebook', label: 'eBooks', icon: 'Book', count: mockTemplates.filter(t => t.documentType === 'ebook').length },
    { key: 'academic', label: 'Academic', icon: 'GraduationCap', count: mockTemplates.filter(t => t.documentType === 'academic').length },
    { key: 'business', label: 'Business', icon: 'Briefcase', count: mockTemplates.filter(t => t.documentType === 'business').length }
  ];

  const featuredTemplates = mockTemplates.filter(t => t.rating >= 4.8).slice(0, 3);

  // Initialize templates
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setTemplates(mockTemplates);
      setFilteredTemplates(mockTemplates);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and search logic
  useEffect(() => {
    let filtered = [...templates];

    // Category filter
    if (activeCategory !== 'all') {
      filtered = filtered.filter(template => template.documentType === activeCategory);
    }

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply filters
    Object.entries(filters).forEach(([key, values]) => {
      if (values && values.length > 0) {
        switch (key) {
          case 'documentType':
            filtered = filtered.filter(template => values.includes(template.documentType));
            break;
          case 'industry':
            filtered = filtered.filter(template => values.includes(template.industry));
            break;
          case 'language':
            filtered = filtered.filter(template => values.includes(template.language.toLowerCase()));
            break;
          case 'complexity':
            filtered = filtered.filter(template => values.includes(template.complexity));
            break;
        }
      }
    });

    // Sort templates
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.usageCount - a.usageCount;
        case 'recent':
          return b.isNew - a.isNew;
        case 'rating':
          return b.rating - a.rating;
        case 'alphabetical':
          return a.title.localeCompare(b.title);
        case 'credits':
          return a.credits - b.credits;
        default:
          return 0;
      }
    });

    setFilteredTemplates(filtered);
  }, [templates, activeCategory, searchQuery, filters, sortBy]);

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const handleFilterChange = (category, values) => {
    if (category === 'all') {
      setFilters({});
    } else if (category === 'quick') {
      // Handle quick filters
      switch (values) {
        case 'popular': setSortBy('popular');
          break;
        case 'recent':
          setFilters({ ...filters, isNew: [true] });
          break;
        case 'free':
          setFilters({ ...filters, credits: [0] });
          break;
      }
    } else {
      setFilters({ ...filters, [category]: values });
    }
  };

  const handleSelectTemplate = (template) => {
    console.log('Selected template:', template.title);
    navigate('/document-creator', { state: { template } });
  };

  const handlePreviewTemplate = (template) => {
    setPreviewTemplate(template);
    setIsPreviewOpen(true);
  };

  const handleFavoriteTemplate = (template) => {
    setTemplates(prev =>
      prev.map(t =>
        t.id === template.id ? { ...t, isFavorite: !t.isFavorite } : t
      )
    );
  };

  const handleLoadMore = () => {
    setLoading(true);
    setTimeout(() => {
      setHasMore(false);
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <QuickActionSidebar />
      
      <div className={`${contentMargin} pt-16 transition-all duration-300 ease-in-out`}>
        <div className="flex">
          {/* Filter Sidebar */}
          <FilterSidebar
            filters={filters}
            onFilterChange={handleFilterChange}
            isCollapsed={isFilterCollapsed}
            onToggleCollapse={() => setIsFilterCollapsed(!isFilterCollapsed)}
          />

          {/* Main Content */}
          <div className={`flex-1 transition-all duration-300 ${isFilterCollapsed ? 'ml-16' : 'ml-18'}`}>
            <div className="p-6 lg:p-8">
              <Breadcrumbs />

              {/* Page Header */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-text-primary mb-2">Template Library</h1>
                    <p className="text-text-secondary">
                      Choose from 100+ professional templates to jumpstart your document creation
                    </p>
                  </div>
                  <Button
                    variant="primary"
                    onClick={() => navigate('/document-creator')}
                    className="hidden lg:flex"
                  >
                    <Icon name="Plus" size={16} />
                    Create from Scratch
                  </Button>
                </div>

                {/* Search Bar */}
                <SearchBar
                  onSearch={handleSearch}
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                />
              </div>

              {/* Featured Templates Carousel */}
              {!searchQuery && activeCategory === 'all' && (
                <FeaturedCarousel
                  featuredTemplates={featuredTemplates}
                  onSelectTemplate={handleSelectTemplate}
                />
              )}

              {/* Category Tabs */}
              <CategoryTabs
                activeCategory={activeCategory}
                onCategoryChange={setActiveCategory}
                categories={categories}
              />

              {/* Templates Grid */}
              <div className="mt-8">
                <TemplateGrid
                  templates={filteredTemplates}
                  loading={loading}
                  onSelectTemplate={handleSelectTemplate}
                  onPreviewTemplate={handlePreviewTemplate}
                  onFavoriteTemplate={handleFavoriteTemplate}
                  onLoadMore={handleLoadMore}
                  hasMore={hasMore}
                  sortBy={sortBy}
                  onSortChange={setSortBy}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Template Preview Modal */}
      <TemplatePreviewModal
        template={previewTemplate}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        onSelect={handleSelectTemplate}
      />
    </div>
  );
};

export default TemplateLibrary;