/**
 * Document Validation Service
 * Provides comprehensive validation for document content, structure, and quality
 */

import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor';

// Create a logger for the validation service
const validationLogger = errorMonitor.createContextLogger('DocumentValidation');

// Validation rule definitions
const VALIDATION_RULES = {
  structure: {
    minWordCount: 500,
    maxWordCount: 50000,
    minChapters: 1,
    maxChapters: 50,
    requiredSections: ['introduction', 'conclusion'],
    minWordsPerChapter: 100
  },
  content: {
    minSentenceLength: 5,
    maxSentenceLength: 50,
    minParagraphLength: 20,
    maxParagraphLength: 500,
    readabilityThreshold: 60, // Flesch Reading Ease score
    duplicateContentThreshold: 0.8
  },
  formatting: {
    consistentHeadingLevels: true,
    properCapitalization: true,
    consistentPunctuation: true,
    noExcessiveWhitespace: true
  }
};

/**
 * Main document validation function
 * @param {Object} documentData - The document data to validate
 * @param {Object} generatedContent - The generated content to validate
 * @returns {Object} Validation results with scores and issues
 */
export const validateDocument = (documentData, generatedContent) => {
  validationLogger.info('Starting document validation', {
    hasDocumentData: !!documentData,
    hasGeneratedContent: !!generatedContent,
    documentType: documentData?.documentPurpose?.primaryType || 'unknown'
  });

  if (!generatedContent) {
    validationLogger.warn('No content to validate', {
      documentId: documentData?.id || 'unknown'
    });

    return {
      isValid: false,
      overallScore: 0,
      issues: [{ type: 'error', category: 'structure', message: 'No content to validate' }],
      scores: {},
      recommendations: []
    };
  }

  try {
    validationLogger.debug('Running validation checks');

    const validationResults = {
      structure: validateStructure(generatedContent),
      content: validateContent(generatedContent),
      formatting: validateFormatting(generatedContent),
      completeness: validateCompleteness(generatedContent)
    };

    const overallScore = calculateOverallScore(validationResults);
    const allIssues = collectAllIssues(validationResults);
    const recommendations = generateRecommendations(validationResults);

    // Log validation results
    validationLogger.info('Document validation completed', {
      isValid: overallScore >= 70,
      overallScore,
      issueCount: allIssues.length,
      errorCount: allIssues.filter(issue => issue.type === 'error').length,
      warningCount: allIssues.filter(issue => issue.type === 'warning').length,
      infoCount: allIssues.filter(issue => issue.type === 'info').length
    });

    return {
      isValid: overallScore >= 70, // 70% threshold for valid document
      overallScore,
      issues: allIssues,
      scores: {
        structure: validationResults.structure.score,
        content: validationResults.content.score,
        formatting: validationResults.formatting.score,
        completeness: validationResults.completeness.score
      },
      recommendations,
      details: validationResults
    };
  } catch (error) {
    validationLogger.error(error, {
      action: 'document_validation',
      documentId: documentData?.id || 'unknown'
    });

    // Return a safe fallback result
    return {
      isValid: false,
      overallScore: 0,
      issues: [{
        type: 'error',
        category: 'system',
        message: 'Validation failed due to system error'
      }],
      scores: {},
      recommendations: ['Contact support if this issue persists']
    };
  }
};

/**
 * Validate document structure
 */
const validateStructure = (content) => {
  const issues = [];
  let score = 100;

  // Check word count
  const totalWords = calculateTotalWordCount(content);
  if (totalWords < VALIDATION_RULES.structure.minWordCount) {
    issues.push({
      type: 'warning',
      category: 'structure',
      message: `Document is too short (${totalWords} words). Minimum recommended: ${VALIDATION_RULES.structure.minWordCount} words.`
    });
    score -= 20;
  }

  // Check chapter count
  const chapterCount = content.chapters?.length || 0;
  if (chapterCount < VALIDATION_RULES.structure.minChapters) {
    issues.push({
      type: 'error',
      category: 'structure',
      message: `Document needs at least ${VALIDATION_RULES.structure.minChapters} chapter(s). Found: ${chapterCount}`
    });
    score -= 30;
  }

  // Check required sections
  VALIDATION_RULES.structure.requiredSections.forEach(section => {
    if (!content[section] || !content[section].content) {
      issues.push({
        type: 'error',
        category: 'structure',
        message: `Missing required section: ${section}`
      });
      score -= 25;
    }
  });

  // Check chapter word counts
  if (content.chapters) {
    content.chapters.forEach((chapter, index) => {
      const chapterWords = countWords(chapter.content || '');
      if (chapterWords < VALIDATION_RULES.structure.minWordsPerChapter) {
        issues.push({
          type: 'warning',
          category: 'structure',
          message: `Chapter ${index + 1} is too short (${chapterWords} words). Minimum recommended: ${VALIDATION_RULES.structure.minWordsPerChapter} words.`
        });
        score -= 5;
      }
    });
  }

  return {
    score: Math.max(0, score),
    issues,
    metrics: {
      totalWords,
      chapterCount,
      averageWordsPerChapter: chapterCount > 0 ? Math.round(totalWords / chapterCount) : 0
    }
  };
};

/**
 * Validate content quality
 */
const validateContent = (content) => {
  const issues = [];
  let score = 100;

  // Analyze all text content
  const allText = extractAllText(content);

  // Check for duplicate content
  const duplicateScore = checkDuplicateContent(content);
  if (duplicateScore > VALIDATION_RULES.content.duplicateContentThreshold) {
    issues.push({
      type: 'warning',
      category: 'content',
      message: `High content similarity detected (${Math.round(duplicateScore * 100)}%). Consider adding more variety.`
    });
    score -= 15;
  }

  // Check readability
  const readabilityScore = calculateReadabilityScore(allText);
  if (readabilityScore < VALIDATION_RULES.content.readabilityThreshold) {
    issues.push({
      type: 'info',
      category: 'content',
      message: `Readability could be improved. Current score: ${readabilityScore}/100`
    });
    score -= 10;
  }

  // Check sentence structure
  const sentences = allText.split(/[.!?]+/).filter(s => s.trim().length > 0);
  sentences.forEach((sentence, index) => {
    const words = sentence.trim().split(/\s+/).length;
    if (words < VALIDATION_RULES.content.minSentenceLength) {
      issues.push({
        type: 'info',
        category: 'content',
        message: `Very short sentence detected (${words} words). Consider expanding for clarity.`
      });
      score -= 2;
    } else if (words > VALIDATION_RULES.content.maxSentenceLength) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: `Very long sentence detected (${words} words). Consider breaking into shorter sentences.`
      });
      score -= 3;
    }
  });

  return {
    score: Math.max(0, score),
    issues,
    metrics: {
      readabilityScore,
      duplicateContentScore: duplicateScore,
      sentenceCount: sentences.length,
      averageWordsPerSentence: sentences.length > 0 ? Math.round(countWords(allText) / sentences.length) : 0
    }
  };
};

/**
 * Validate formatting consistency
 */
const validateFormatting = (content) => {
  const issues = [];
  let score = 100;

  // Check heading consistency
  if (content.chapters) {
    const headingLevels = content.chapters.map(ch => ch.title?.length || 0);
    const inconsistentHeadings = headingLevels.some(level => level === 0);
    if (inconsistentHeadings) {
      issues.push({
        type: 'warning',
        category: 'formatting',
        message: 'Some chapters have missing or inconsistent titles'
      });
      score -= 15;
    }
  }

  // Check for excessive whitespace
  const allText = extractAllText(content);
  if (allText.includes('  ') || allText.includes('\n\n\n')) {
    issues.push({
      type: 'info',
      category: 'formatting',
      message: 'Excessive whitespace detected. Consider cleaning up formatting.'
    });
    score -= 5;
  }

  return {
    score: Math.max(0, score),
    issues,
    metrics: {
      headingConsistency: score >= 85
    }
  };
};

/**
 * Validate document completeness
 */
const validateCompleteness = (content) => {
  const issues = [];
  let score = 100;

  // Check if all sections have content
  const sections = ['introduction', 'conclusion'];
  sections.forEach(section => {
    if (!content[section]?.content || content[section].content.trim().length === 0) {
      issues.push({
        type: 'error',
        category: 'completeness',
        message: `${section.charAt(0).toUpperCase() + section.slice(1)} section is empty`
      });
      score -= 30;
    }
  });

  // Check chapter completeness
  if (content.chapters) {
    const emptyChapters = content.chapters.filter(ch => !ch.content || ch.content.trim().length === 0);
    if (emptyChapters.length > 0) {
      issues.push({
        type: 'error',
        category: 'completeness',
        message: `${emptyChapters.length} chapter(s) are empty`
      });
      score -= emptyChapters.length * 20;
    }
  }

  return {
    score: Math.max(0, score),
    issues,
    metrics: {
      completionPercentage: score
    }
  };
};

// Helper functions
const calculateTotalWordCount = (content) => {
  let total = 0;

  if (content.introduction?.content) {
    total += countWords(content.introduction.content);
  }

  if (content.chapters) {
    total += content.chapters.reduce((sum, chapter) => {
      return sum + countWords(chapter.content || '');
    }, 0);
  }

  if (content.conclusion?.content) {
    total += countWords(content.conclusion.content);
  }

  return total;
};

const countWords = (text) => {
  if (!text) return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

const extractAllText = (content) => {
  let allText = '';

  if (content.introduction?.content) {
    allText += content.introduction.content + ' ';
  }

  if (content.chapters) {
    content.chapters.forEach(chapter => {
      if (chapter.content) {
        allText += chapter.content + ' ';
      }
    });
  }

  if (content.conclusion?.content) {
    allText += content.conclusion.content + ' ';
  }

  return allText.trim();
};

const checkDuplicateContent = (content) => {
  // Simple duplicate detection - could be enhanced with more sophisticated algorithms
  const texts = [];

  if (content.introduction?.content) texts.push(content.introduction.content);
  if (content.chapters) {
    content.chapters.forEach(ch => {
      if (ch.content) texts.push(ch.content);
    });
  }
  if (content.conclusion?.content) texts.push(content.conclusion.content);

  // Calculate similarity between sections
  let maxSimilarity = 0;
  for (let i = 0; i < texts.length; i++) {
    for (let j = i + 1; j < texts.length; j++) {
      const similarity = calculateTextSimilarity(texts[i], texts[j]);
      maxSimilarity = Math.max(maxSimilarity, similarity);
    }
  }

  return maxSimilarity;
};

const calculateTextSimilarity = (text1, text2) => {
  // Simple word-based similarity
  const words1 = new Set(text1.toLowerCase().split(/\s+/));
  const words2 = new Set(text2.toLowerCase().split(/\s+/));

  const intersection = new Set([...words1].filter(x => words2.has(x)));
  const union = new Set([...words1, ...words2]);

  return intersection.size / union.size;
};

const calculateReadabilityScore = (text) => {
  // Simplified Flesch Reading Ease calculation
  if (!text) return 0;

  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  const words = countWords(text);
  const syllables = estimateSyllables(text);

  if (sentences === 0 || words === 0) return 0;

  const avgSentenceLength = words / sentences;
  const avgSyllablesPerWord = syllables / words;

  const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
  return Math.max(0, Math.min(100, Math.round(score)));
};

const estimateSyllables = (text) => {
  // Simple syllable estimation
  return text.toLowerCase().match(/[aeiouy]+/g)?.length || 1;
};

const calculateOverallScore = (results) => {
  const weights = {
    structure: 0.3,
    content: 0.3,
    formatting: 0.2,
    completeness: 0.2
  };

  return Math.round(
    results.structure.score * weights.structure +
    results.content.score * weights.content +
    results.formatting.score * weights.formatting +
    results.completeness.score * weights.completeness
  );
};

const collectAllIssues = (results) => {
  return [
    ...results.structure.issues,
    ...results.content.issues,
    ...results.formatting.issues,
    ...results.completeness.issues
  ].sort((a, b) => {
    const priority = { error: 3, warning: 2, info: 1 };
    return priority[b.type] - priority[a.type];
  });
};

const generateRecommendations = (results) => {
  const recommendations = [];

  if (results.structure.score < 80) {
    recommendations.push('Consider expanding your content to meet recommended word counts');
    recommendations.push('Ensure all required sections (introduction, conclusion) are present');
  }

  if (results.content.score < 80) {
    recommendations.push('Review content for readability and sentence structure');
    recommendations.push('Add more variety to avoid repetitive content');
  }

  if (results.formatting.score < 80) {
    recommendations.push('Check heading consistency across chapters');
    recommendations.push('Clean up excessive whitespace and formatting issues');
  }

  if (results.completeness.score < 80) {
    recommendations.push('Complete all empty sections and chapters');
    recommendations.push('Review document for missing content');
  }

  return recommendations;
};

export default {
  validateDocument,
  VALIDATION_RULES
};
