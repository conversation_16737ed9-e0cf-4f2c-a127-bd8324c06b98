import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const ProfileSection = () => {
  const { user, profile, updateProfile, loading, error } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [tempData, setTempData] = useState({});
  const [updateError, setUpdateError] = useState('');

  // Initialize profile data from auth context
  useEffect(() => {
    if (profile) {
      const profileData = {
        full_name: profile.full_name || '',
        email: user?.email || '',
        phone: profile.phone || '',
        organization: profile.organization || '',
        user_type: profile.user_type || '',
        bio: profile.bio || '',
        location: profile.location || '',
        website: profile.website || '',
        avatar_url: profile.avatar_url || ''
      };
      setTempData(profileData);
    }
  }, [profile, user]);

  const userTypes = [
    { value: "student", label: "Student" },
    { value: "educator", label: "Educator" },
    { value: "researcher", label: "Researcher" },
    { value: "business", label: "Business Professional" },
    { value: "entrepreneur", label: "Entrepreneur" },
    { value: "content_creator", label: "Content Creator" }
  ];

  const handleEdit = () => {
    setIsEditing(true);
    setUpdateError('');
  };

  const handleSave = async () => {
    try {
      setUpdateError('');
      const result = await updateProfile(tempData);

      if (result.success) {
        setIsEditing(false);
      } else {
        setUpdateError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      setUpdateError('An error occurred while updating profile');
    }
  };

  const handleCancel = () => {
    // Reset to current profile data
    if (profile) {
      const profileData = {
        full_name: profile.full_name || '',
        email: user?.email || '',
        phone: profile.phone || '',
        organization: profile.organization || '',
        user_type: profile.user_type || '',
        bio: profile.bio || '',
        location: profile.location || '',
        website: profile.website || '',
        avatar_url: profile.avatar_url || ''
      };
      setTempData(profileData);
    }
    setIsEditing(false);
    setUpdateError('');
  };

  const handleInputChange = (field, value) => {
    setTempData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePhotoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        handleInputChange('avatar_url', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="bg-surface rounded-lg border border-border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-text-primary">Profile Information</h2>
          <p className="text-sm text-text-secondary">Manage your personal information and preferences</p>
        </div>
        {!isEditing ? (
          <Button variant="outline" onClick={handleEdit} iconName="Edit2" iconPosition="left">
            Edit Profile
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button variant="ghost" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSave}
              iconName="Save"
              iconPosition="left"
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        )}
      </div>

      {/* Error Display */}
      {(error || updateError) && (
        <div className="mb-6 p-4 bg-error/10 border border-error/20 rounded-lg">
          <div className="flex items-center space-x-2">
            <Icon name="AlertCircle" size={16} color="var(--color-error)" />
            <p className="text-sm text-error">{error || updateError}</p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Photo Section */}
        <div className="lg:col-span-1">
          <div className="text-center">
            <div className="relative inline-block">
              <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-border bg-background">
                {(tempData.avatar_url || profile?.avatar_url) ? (
                  <Image
                    src={isEditing ? tempData.avatar_url : profile?.avatar_url}
                    alt="Profile Photo"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
                    <span className="text-white text-3xl font-bold">
                      {(profile?.full_name || user?.email || 'U').charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
              {isEditing && (
                <label className="absolute bottom-0 right-0 w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center cursor-pointer hover:bg-primary/90 transition-micro">
                  <Icon name="Camera" size={16} />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    className="hidden"
                  />
                </label>
              )}
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-text-primary">
                {profile?.full_name || user?.email?.split('@')[0] || 'User'}
              </h3>
              <p className="text-sm text-text-secondary capitalize">
                {profile?.user_type ? profile.user_type.replace('_', ' ') : 'User'}
              </p>
            </div>
          </div>
        </div>

        {/* Profile Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Basic Information</h4>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Full Name</label>
                {isEditing ? (
                  <Input
                    type="text"
                    value={tempData.full_name || ''}
                    onChange={(e) => handleInputChange('full_name', e.target.value)}
                    placeholder="Enter your full name"
                    disabled={loading}
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                    {profile?.full_name || 'Not provided'}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Contact Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Email Address</label>
                <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                  {user?.email} <span className="text-xs text-text-muted">(Cannot be changed)</span>
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Phone Number</label>
                {isEditing ? (
                  <Input
                    type="tel"
                    value={tempData.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter phone number"
                    disabled={loading}
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                    {profile?.phone || 'Not provided'}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Professional Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Organization</label>
                {isEditing ? (
                  <Input
                    type="text"
                    value={tempData.organization || ''}
                    onChange={(e) => handleInputChange('organization', e.target.value)}
                    placeholder="Enter organization name"
                    disabled={loading}
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                    {profile?.organization || 'Not provided'}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">User Type</label>
                {isEditing ? (
                  <select
                    value={tempData.user_type || ''}
                    onChange={(e) => handleInputChange('user_type', e.target.value)}
                    disabled={loading}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                  >
                    <option value="">Select user type</option>
                    {userTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg capitalize">
                    {userTypes.find(type => type.value === profile?.user_type)?.label || 'Not specified'}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Bio Section */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">Bio</label>
            {isEditing ? (
              <textarea
                value={tempData.bio || ''}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={4}
                disabled={loading}
                className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none disabled:opacity-50"
              />
            ) : (
              <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                {profile?.bio || 'No bio provided'}
              </p>
            )}
          </div>

          {/* Additional Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Location</label>
              {isEditing ? (
                <Input
                  type="text"
                  value={tempData.location || ''}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="Enter your location"
                  disabled={loading}
                />
              ) : (
                <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                  {profile?.location || 'Not provided'}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Website</label>
              {isEditing ? (
                <Input
                  type="url"
                  value={tempData.website || ''}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="Enter your website URL"
                  disabled={loading}
                />
              ) : (
                <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                  {profile?.website ? (
                    <a href={profile.website} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                      {profile.website}
                    </a>
                  ) : (
                    'Not provided'
                  )}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSection;