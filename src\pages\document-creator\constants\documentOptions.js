/**
 * Static Document Creation Options
 * Centralized constants for document types, formats, and workflow configuration
 */

/**
 * Available document types with metadata
 */
export const DOCUMENT_TYPES = [
  { 
    id: 'ebook', 
    name: 'eBook', 
    icon: 'Book', 
    description: 'Digital book with chapters and sections' 
  },
  { 
    id: 'academic', 
    name: 'Academic Paper', 
    icon: 'GraduationCap', 
    description: 'Research papers, essays, theses' 
  },
  { 
    id: 'business', 
    name: 'Business Document', 
    icon: 'Briefcase', 
    description: 'Reports, proposals, presentations' 
  },
  {
    id: 'guide',
    name: 'Guide',
    icon: 'Map',
    description: 'Step-by-step instructional content'
  }
];

/**
 * Available output formats
 */
export const FORMAT_OPTIONS = [
  { 
    id: 'pdf', 
    name: 'PDF', 
    description: 'Portable Document Format' 
  },
  { 
    id: 'epub', 
    name: 'EPUB', 
    description: 'Electronic Publication' 
  },
  { 
    id: 'word', 
    name: 'Word Document', 
    description: 'Microsoft Word format' 
  },
  { 
    id: 'html', 
    name: 'Web Page', 
    description: 'HTML format for web' 
  }
];

/**
 * Baseline content options for document creation
 */
export const BASELINE_OPTIONS = [
  { 
    id: 'template', 
    name: 'select', 
    description: 'Choose from our templates' 
  },
  { 
    id: 'upload', 
    name: 'upload', 
    description: 'Upload your own content' 
  },
  { 
    id: 'scratch', 
    name: 'start from scratch', 
    description: 'Begin with a blank document' 
  },
  { 
    id: 'import-url', 
    name: 'Import from Blog post or URL', 
    description: 'Extract content from web URL' 
  },
  {
    id: 'import-docx',
    name: 'Import from DOCX',
    description: 'Upload and extract content from DOCX file'
  },
  {
    id: 'import-pdf',
    name: 'Import from PDF',
    description: 'Upload and extract content from PDF file'
  }
];

/**
 * High-level workflow phases for document creation
 */
export const WORKFLOW_PHASES = [
  { 
    id: 1, 
    title: 'Generate', 
    icon: 'Sparkles', 
    description: 'AI content generation' 
  },
  { 
    id: 2, 
    title: 'Edit Content', 
    icon: 'Edit3', 
    description: 'Review and modify content' 
  },
  { 
    id: 3, 
    title: 'Review', 
    icon: 'Eye', 
    description: 'Review document content' 
  },
  { 
    id: 4, 
    title: 'Publish', 
    icon: 'Send', 
    description: 'Export and share' 
  }
];

/**
 * Default document type mapping for auto-inference
 */
export const DOCUMENT_TYPE_MAPPING = {
  'academic-paper': 'academic',
  'business-report': 'business',
  'guide': 'guide',
  'ebook': 'ebook'
};

/**
 * Helper function to get document type by ID
 * @param {string} id - Document type ID
 * @returns {Object|null} Document type object or null if not found
 */
export const getDocumentTypeById = (id) => {
  return DOCUMENT_TYPES.find(type => type.id === id) || null;
};

/**
 * Helper function to get format option by ID
 * @param {string} id - Format option ID
 * @returns {Object|null} Format option object or null if not found
 */
export const getFormatOptionById = (id) => {
  return FORMAT_OPTIONS.find(format => format.id === id) || null;
};

/**
 * Helper function to get baseline option by ID
 * @param {string} id - Baseline option ID
 * @returns {Object|null} Baseline option object or null if not found
 */
export const getBaselineOptionById = (id) => {
  return BASELINE_OPTIONS.find(baseline => baseline.id === id) || null;
};

/**
 * Helper function to get workflow phase by ID
 * @param {number} id - Phase ID
 * @returns {Object|null} Workflow phase object or null if not found
 */
export const getWorkflowPhaseById = (id) => {
  return WORKFLOW_PHASES.find(phase => phase.id === id) || null;
};
