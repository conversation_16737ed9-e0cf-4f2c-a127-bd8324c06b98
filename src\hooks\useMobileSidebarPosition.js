import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook to manage sidebar positioning based on page layout
 * Sets CSS custom properties for both mobile and desktop sidebar positioning
 * to ensure sidebar appears below page headers rather than covering them
 */
const useSidebarPosition = () => {
  const location = useLocation();

  useEffect(() => {
    const updateSidebarPosition = () => {
      // Default values for pages without special headers
      let mobileTopOffset = '4rem'; // Default to standard header height
      let mobileHeight = 'calc(100vh - 4rem)';
      let desktopTopOffset = '4rem'; // Default desktop positioning below standard header

      // Document editor pages - position below DocumentWorkflowHeader only
      if (location.pathname.includes('/document-editor/')) {
        // DocumentWorkflowHeader: standardized to 4rem (64px)
        mobileTopOffset = '4rem'; // Mobile: below workflow header
        mobileHeight = 'calc(100vh - 4rem)';
        desktopTopOffset = '4rem'; // Desktop: below workflow header
      }
      // Document creator pages - position below DesignrrStepIndicator
      else if (location.pathname.includes('/document-creator')) {
        // DesignrrStepIndicator header: standardized to 4rem (64px)
        mobileTopOffset = '4rem'; // Mobile: below step indicator
        mobileHeight = 'calc(100vh - 4rem)';
        desktopTopOffset = '4rem'; // Desktop: below step indicator
      }
      // Dashboard and other pages with fixed header
      else if (location.pathname === '/dashboard' ||
               location.pathname === '/projects' ||
               location.pathname === '/template-library' ||
               location.pathname === '/plagiarism-checker') {
        // Fixed header height: 4rem (64px)
        mobileTopOffset = '4rem';
        mobileHeight = 'calc(100vh - 4rem)';
        desktopTopOffset = '4rem'; // Desktop: below fixed header
      }

      // Set CSS custom properties on document root for both mobile and desktop
      document.documentElement.style.setProperty('--mobile-sidebar-top', mobileTopOffset);
      document.documentElement.style.setProperty('--mobile-sidebar-height', mobileHeight);
      document.documentElement.style.setProperty('--desktop-sidebar-top', desktopTopOffset);
    };

    // Update on route change
    updateSidebarPosition();

    // Update on window resize (in case header heights change)
    const handleResize = () => {
      // Debounce resize events
      clearTimeout(window.sidebarResizeTimeout);
      window.sidebarResizeTimeout = setTimeout(updateSidebarPosition, 150);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(window.sidebarResizeTimeout);
    };
  }, [location.pathname]);
};

export default useSidebarPosition;
