import React from 'react';
import Icon from '../AppIcon';
import Button from './Button';

/**
 * Enhanced modal for DOCX extraction results and error handling
 * Provides informative feedback for both success and failure cases
 * Follows the same pattern as URLExtractionModal for consistency
 */
const DocxExtractionModal = ({ 
  isOpen, 
  onClose, 
  type = 'error', // 'error', 'success', 'loading'
  title,
  message,
  extractedData = null,
  onRetry = null,
  onContinue = null,
  progress = 0
}) => {
  if (!isOpen) return null;

  const getModalIcon = () => {
    switch (type) {
      case 'success':
        return <Icon name="CheckCircle" size={48} className="text-green-500" />;
      case 'loading':
        return <Icon name="Loader2" size={48} className="text-blue-500 animate-spin" />;
      case 'error':
      default:
        return <Icon name="AlertCircle" size={48} className="text-red-500" />;
    }
  };

  const getModalColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'loading':
        return 'bg-blue-50 border-blue-200';
      case 'error':
      default:
        return 'bg-red-50 border-red-200';
    }
  };

  const renderLoadingContent = () => (
    <div className="text-center space-y-6">
      {/* Loading Icon and Message */}
      <div className="space-y-4">
        {getModalIcon()}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600">{message}</p>
        </div>
      </div>

      {/* Progress Bar */}
      {progress > 0 && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Processing document...</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Processing Steps */}
      <div className="text-left space-y-2">
        <h4 className="font-medium text-gray-900">Processing Steps:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li className="flex items-center">
            <Icon name="CheckCircle" size={16} className="text-green-500 mr-2" />
            File validation completed
          </li>
          <li className="flex items-center">
            <Icon name="Loader2" size={16} className="text-blue-500 animate-spin mr-2" />
            Extracting content and structure
          </li>
          <li className="flex items-center text-gray-400">
            <Icon name="Circle" size={16} className="mr-2" />
            Analyzing document type
          </li>
          <li className="flex items-center text-gray-400">
            <Icon name="Circle" size={16} className="mr-2" />
            Preparing content preview
          </li>
        </ul>
      </div>
    </div>
  );

  const renderErrorContent = () => (
    <div className="space-y-6">
      {/* Error Icon and Message */}
      <div className="text-center space-y-4">
        {getModalIcon()}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600">{message}</p>
        </div>
      </div>

      {/* Debug Information */}
      {extractedData?.debugInfo && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Icon name="Bug" size={20} className="text-gray-600 mt-0.5 flex-shrink-0" />
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Debug Information</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <p><strong>Error Type:</strong> {extractedData.debugInfo.errorType}</p>
                <p><strong>Original Error:</strong> {extractedData.debugInfo.originalError}</p>
                {extractedData.debugInfo.stack && (
                  <div>
                    <strong>Stack Trace:</strong>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                      {extractedData.debugInfo.stack}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Common Issues and Solutions */}
      <div className={`p-4 rounded-lg ${getModalColor()}`}>
        <div className="flex items-start space-x-3">
          <Icon name="Info" size={20} className="text-red-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Common Issues</h4>
            <ul className="text-sm text-gray-700 space-y-1 ml-4">
              <li>• File may be corrupted or password-protected</li>
              <li>• Document format might not be supported (.doc vs .docx)</li>
              <li>• File size exceeds the 10MB limit</li>
              <li>• Document contains complex formatting that couldn't be processed</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Suggested Actions */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Icon name="Lightbulb" size={20} className="text-yellow-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-gray-900">Try These Solutions</h4>
            <ul className="text-sm text-gray-700 mt-1 space-y-1 ml-4">
              <li>• Ensure the file is a valid .docx format</li>
              <li>• Try saving the document again from Word</li>
              <li>• Check if the file opens correctly in Microsoft Word</li>
              <li>• Reduce file size by removing images or complex formatting</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        {onRetry && (
          <Button
            variant="primary"
            onClick={onRetry}
            iconName="RefreshCw"
            iconPosition="left"
            className="flex-1"
          >
            Try Another File
          </Button>
        )}
        <Button
          variant="ghost"
          onClick={onClose}
          className="flex-1"
        >
          Cancel
        </Button>
      </div>
    </div>
  );

  const renderSuccessContent = () => (
    <div className="space-y-6">
      {/* Success Icon and Message */}
      <div className="text-center space-y-4">
        {getModalIcon()}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600">{message}</p>
        </div>
      </div>

      {/* Extracted Content Summary */}
      {extractedData && (
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Document Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Title:</span>
                <p className="font-medium text-gray-900 truncate">{extractedData.originalTitle}</p>
              </div>
              <div>
                <span className="text-gray-600">Word Count:</span>
                <p className="font-medium text-gray-900">{extractedData.wordCount?.toLocaleString()}</p>
              </div>
              <div>
                <span className="text-gray-600">Document Type:</span>
                <p className="font-medium text-gray-900 capitalize">{extractedData.documentType?.replace('-', ' ')}</p>
              </div>
              <div>
                <span className="text-gray-600">Structure:</span>
                <p className="font-medium text-gray-900">
                  {extractedData.headings?.length || 0} headings
                </p>
              </div>
            </div>
          </div>

          {/* Content Preview */}
          {extractedData.extractedContent && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Content Preview</h4>
              <p className="text-sm text-gray-700 leading-relaxed">
                {extractedData.extractedContent.substring(0, 300)}
                {extractedData.extractedContent.length > 300 && '...'}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Next Steps */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Icon name="ArrowRight" size={20} className="text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-gray-900">Next Steps</h4>
            <p className="text-sm text-gray-700 mt-1">
              The extracted content will be used to enhance your document creation process. 
              Continue with the wizard to customize your document.
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        {onContinue && (
          <Button
            variant="primary"
            onClick={onContinue}
            iconName="ArrowRight"
            iconPosition="right"
            className="flex-1"
          >
            Continue to Next Step
          </Button>
        )}
        <Button
          variant="ghost"
          onClick={onClose}
          className="flex-1"
        >
          Close
        </Button>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            DOCX Content Extraction
          </h2>
          {type !== 'loading' && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Icon name="X" size={20} className="text-gray-500" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {type === 'error' && renderErrorContent()}
          {type === 'success' && renderSuccessContent()}
          {type === 'loading' && renderLoadingContent()}
        </div>
      </div>
    </div>
  );
};

export default DocxExtractionModal;
