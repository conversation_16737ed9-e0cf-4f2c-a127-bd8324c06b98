# Implementation Plan

- [x] 1. Add expanded menu ref and click-outside detection hook
  - Create expandedMenuRef using useRef hook in DocumentCanvasMinimal component
  - Implement useEffect hook for click-outside detection with mousedown and touchstart events
  - Add conditional event listener attachment only when isMenuExpanded is true
  - Include proper cleanup function to remove event listeners
  - _Requirements: 1.2, 1.3, 2.4, 4.1, 4.2_

- [x] 2. Implement click-outside handler logic
  - Create handleClickOutside function that checks if click target is outside menu
  - Add null reference protection for expandedMenuRef.current
  - Integrate with existing preventAutoClose state to respect menu locking
  - Add event.target validation to ensure robust event handling
  - _Requirements: 1.2, 3.1, 3.2, 4.3_

- [x] 3. Attach ref to expanded menu container
  - Locate the expanded menu JSX in the floating menu render section
  - Add ref={expandedMenuRef} to the expanded menu container div
  - Ensure ref attachment doesn't interfere with existing positioning styles
  - Verify menu container properly receives the ref for click detection
  - _Requirements: 1.2, 2.3, 3.1_

- [ ] 4. Add mobile touch event support
  - Include touchstart event listener alongside mousedown in useEffect
  - Test touch event handling for mobile devices
  - Ensure touch events properly trigger click-outside detection
  - Add cleanup for both mouse and touch event listeners
  - _Requirements: 2.4, 5.1_

- [x] 5. Integrate with existing menu state management
  - Ensure click-outside detection works with existing setIsMenuExpanded calls
  - Verify integration with preventAutoClose flag functionality
  - Test coordination with existing menu toggle behavior
  - Ensure menu closing preserves editor focus state
  - _Requirements: 1.4, 4.3, 5.3_

- [x] 6. Add error handling and edge case protection
  - Wrap click detection logic in try-catch block for robustness
  - Add validation for event.target existence and type
  - Handle cases where menu repositions while open
  - Add protection against rapid clicking scenarios
  - _Requirements: 4.3, 5.4_

- [ ] 7. Test click-outside functionality
  - Write unit tests for click-outside detection behavior
  - Test that clicks inside menu do not close it
  - Test that clicks outside menu close it immediately
  - Verify event listener cleanup when menu closes
  - _Requirements: 1.2, 3.1, 4.2, 5.1_

- [ ] 8. Test integration with existing systems
  - Test click-outside behavior during scroll events
  - Verify coordination with scroll-aware positioning system
  - Test menu behavior with multiple UI elements present
  - Ensure no interference with other editor functionality
  - _Requirements: 2.1, 2.3, 4.3_

- [ ] 9. Test mobile and cross-browser compatibility
  - Test touch events on mobile devices and tablets
  - Verify behavior across different screen sizes
  - Test rapid touch/click interactions
  - Ensure consistent behavior across supported browsers
  - _Requirements: 2.2, 2.4, 5.4_

- [ ] 10. Performance testing and optimization
  - Verify no performance impact when menu is closed
  - Test memory usage and ensure no memory leaks
  - Monitor event listener attachment/detachment efficiency
  - Optimize if any performance issues are detected
  - _Requirements: 4.1, 4.2, 5.1_
## A
dditional Bug Fix: Floating Menu Disappearing During Scroll

- [x] 11. Fix Intersection Observer visibility detection
  - Modified threshold from 0.1 to 0 for more forgiving detection
  - Increased rootMargin from '50px 0px' to '100px 0px' to prevent premature hiding
  - Added visibilityChanged flag to distinguish visibility changes from scroll updates
  - _Requirements: 2.1, 4.3, 5.1_

- [x] 12. Implement debounced menu hiding during scroll
  - Added menuVisibilityTimeoutRef and lastVisibilityStateRef for state tracking
  - Implemented 150ms delay before hiding menu when element goes out of view
  - Added immediate restoration when element comes back into view
  - _Requirements: 2.1, 5.1, 5.4_

- [x] 13. Add robust error handling for scroll events
  - Added DOM containment check to ensure target element is still valid
  - Improved handling of invalid getBoundingClientRect() results during scroll
  - Added proper cleanup for visibility timeout on component unmount
  - _Requirements: 4.2, 4.3, 5.4_

- [x] 14. Prevent menu flicker during rapid scroll transitions
  - Separated visibility change handling from normal scroll position updates
  - Added timeout clearing to prevent race conditions
  - Maintained menu state during temporary visibility loss
  - _Requirements: 2.1, 5.1, 5.4_