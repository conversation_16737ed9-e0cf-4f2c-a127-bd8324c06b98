import React from 'react'
import Button from '../ui/Button'
import Icon from '../AppIcon'

class AuthErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Auth Error Boundary caught an error:', error, errorInfo)
    this.setState({
      error: error,
      errorInfo: errorInfo
    })
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
    // Optionally reload the page or reset auth state
    if (this.props.onRetry) {
      this.props.onRetry()
    } else {
      window.location.reload()
    }
  }

  handleGoHome = () => {
    window.location.href = '/auth'
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <div className="bg-surface rounded-2xl border border-border p-8 shadow-card text-center">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-error/10 rounded-2xl flex items-center justify-center">
                  <Icon name="AlertTriangle" size={32} color="var(--color-error)" />
                </div>
              </div>
              
              <h1 className="text-2xl font-bold text-text-primary mb-4">
                Authentication Error
              </h1>
              
              <p className="text-text-secondary mb-6">
                Something went wrong with the authentication system. This might be a temporary issue.
              </p>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="mb-6 p-4 bg-error/5 border border-error/20 rounded-lg text-left">
                  <h3 className="text-sm font-medium text-error mb-2">Error Details:</h3>
                  <pre className="text-xs text-error/80 overflow-auto">
                    {this.state.error.toString()}
                  </pre>
                </div>
              )}

              <div className="space-y-3">
                <Button
                  variant="primary"
                  size="lg"
                  fullWidth
                  onClick={this.handleRetry}
                  iconName="RefreshCw"
                  iconPosition="left"
                >
                  Try Again
                </Button>
                
                <Button
                  variant="ghost"
                  size="lg"
                  fullWidth
                  onClick={this.handleGoHome}
                  iconName="Home"
                  iconPosition="left"
                >
                  Go to Login
                </Button>
              </div>

              <div className="mt-6 pt-6 border-t border-border">
                <p className="text-xs text-text-muted">
                  If this problem persists, please contact support.
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default AuthErrorBoundary
