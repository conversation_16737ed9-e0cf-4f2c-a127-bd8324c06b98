// Environment Variables Checker
export const checkEnvironment = () => {
  console.log('🔍 Checking environment variables...')
  
  const requiredVars = {
    'VITE_SUPABASE_URL': import.meta.env.VITE_SUPABASE_URL,
    'VITE_SUPABASE_ANON_KEY': import.meta.env.VITE_SUPABASE_ANON_KEY
  }
  
  console.log('Environment variables:')
  Object.entries(requiredVars).forEach(([key, value]) => {
    if (value) {
      console.log(`✅ ${key}: ${value.substring(0, 20)}...`)
    } else {
      console.log(`❌ ${key}: NOT SET`)
    }
  })
  
  const allSet = Object.values(requiredVars).every(<PERSON><PERSON>an)
  
  if (allSet) {
    console.log('✅ All required environment variables are set')
  } else {
    console.log('❌ Some required environment variables are missing')
  }
  
  return { success: allSet, variables: requiredVars }
}

// Auto-check on import in development
if (import.meta.env.DEV) {
  checkEnvironment()
}

export default checkEnvironment
