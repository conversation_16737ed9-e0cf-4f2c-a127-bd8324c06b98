/**
 * Document Data Diagnostic Tool
 * Helps identify data synchronization issues between editor and export
 */

// Diagnostic function to analyze document data
export const diagnoseDocumentData = (documentId) => {
  console.log('🔍 DOCUMENT DATA DIAGNOSTIC REPORT');
  console.log('=====================================');
  
  try {
    // Get raw localStorage data
    const rawData = localStorage.getItem(`document-${documentId}`);
    if (!rawData) {
      console.log('❌ No document data found in localStorage');
      return null;
    }
    
    const documentData = JSON.parse(rawData);
    const generatedContent = documentData.generatedContent;
    
    console.log('📊 Document Overview:');
    console.log('- Document ID:', documentId);
    console.log('- Created:', documentData.createdAt);
    console.log('- Last Modified:', documentData.lastModified);
    console.log('- Has Generated Content:', !!generatedContent);
    
    if (!generatedContent) {
      console.log('❌ No generated content found');
      return documentData;
    }
    
    // Analyze legacy placedImages
    console.log('\n🖼️ Legacy PlacedImages Analysis:');
    if (generatedContent.placedImages) {
      console.log('- Has placedImages:', true);
      const placedImageChapters = Object.keys(generatedContent.placedImages);
      console.log('- Chapters with placedImages:', placedImageChapters);
      
      placedImageChapters.forEach(chapterId => {
        const images = generatedContent.placedImages[chapterId];
        console.log(`  - ${chapterId}: ${images.length} images`);
        images.forEach((img, index) => {
          console.log(`    ${index + 1}. ${img.description || 'No description'} (${img.url})`);
        });
      });
    } else {
      console.log('- Has placedImages:', false);
    }
    
    // Analyze chapter content for block-based images
    console.log('\n📝 Chapter Content Analysis:');
    if (generatedContent.chapters) {
      console.log('- Total chapters:', generatedContent.chapters.length);
      
      generatedContent.chapters.forEach((chapter, index) => {
        console.log(`\n📖 Chapter ${chapter.number || index + 1}: ${chapter.title || 'Untitled'}`);
        console.log('- Has content:', !!chapter.content);
        
        if (chapter.content) {
          // Look for markdown images
          const imageMatches = chapter.content.match(/!\[.*?\]\(.*?\)/g);
          console.log('- Block-based images found:', imageMatches ? imageMatches.length : 0);
          
          if (imageMatches) {
            imageMatches.forEach((match, imgIndex) => {
              const imageMatch = match.match(/!\[(.*?)\]\((.*?)\)/);
              if (imageMatch) {
                const [, alt, src] = imageMatch;
                console.log(`  ${imgIndex + 1}. ${alt || 'No alt'} (${src})`);
              }
            });
          }
          
          // Show first 200 characters of content
          const preview = chapter.content.substring(0, 200);
          console.log('- Content preview:', preview + (chapter.content.length > 200 ? '...' : ''));
        }
      });
    } else {
      console.log('- No chapters found');
    }
    
    // Check for migration status
    console.log('\n🔄 Migration Status:');
    console.log('- Has been migrated:', !!documentData.migrated);
    console.log('- Migration date:', documentData.migratedAt || 'N/A');
    
    // Identify potential conflicts
    console.log('\n⚠️ Potential Issues:');
    const issues = [];
    
    if (generatedContent.placedImages && Object.keys(generatedContent.placedImages).length > 0) {
      issues.push('Legacy placedImages data still present - should be migrated');
    }
    
    if (generatedContent.chapters) {
      const chaptersWithBlockImages = generatedContent.chapters.filter(ch => 
        ch.content && ch.content.includes('![')
      );
      const chaptersWithPlacedImages = generatedContent.placedImages ? 
        Object.keys(generatedContent.placedImages) : [];
      
      if (chaptersWithBlockImages.length > 0 && chaptersWithPlacedImages.length > 0) {
        issues.push('Both block-based and legacy images present - data conflict possible');
      }
    }
    
    if (issues.length === 0) {
      console.log('✅ No obvious issues detected');
    } else {
      issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    console.log('\n=====================================');
    return documentData;
    
  } catch (error) {
    console.error('❌ Error during diagnostic:', error);
    return null;
  }
};

// Function to clear problematic cached data
export const clearDocumentCache = (documentId) => {
  console.log('🧹 CLEARING DOCUMENT CACHE');
  console.log('===========================');
  
  try {
    // Clear main document data
    const documentKey = `document-${documentId}`;
    localStorage.removeItem(documentKey);
    console.log('✅ Cleared document data:', documentKey);
    
    // Clear any related cached data
    const keysToCheck = [
      'titleSelection.generatedTitles',
      'topicAndNiche.availableSubNiches', 
      'documentOutline.generatedOutline'
    ];
    
    keysToCheck.forEach(key => {
      const storageKey = `docforge_cached_${key}`;
      if (localStorage.getItem(storageKey)) {
        localStorage.removeItem(storageKey);
        console.log('✅ Cleared cached data:', storageKey);
      }
    });
    
    // Clear session metadata
    if (localStorage.getItem('docforge_session_metadata')) {
      localStorage.removeItem('docforge_session_metadata');
      console.log('✅ Cleared session metadata');
    }
    
    console.log('✅ Cache clearing complete');
    console.log('💡 Recommendation: Refresh the page and recreate your document');
    
  } catch (error) {
    console.error('❌ Error clearing cache:', error);
  }
};

// Function to force migration of document data
export const forceMigrateDocument = (documentId) => {
  console.log('🔄 FORCING DOCUMENT MIGRATION');
  console.log('==============================');
  
  try {
    const rawData = localStorage.getItem(`document-${documentId}`);
    if (!rawData) {
      console.log('❌ No document data found');
      return false;
    }
    
    const documentData = JSON.parse(rawData);
    
    // Import migration function
    import('../pages/document-editor/utils/imageMigration.js').then(({ migrateDocumentIfNeeded }) => {
      const migratedData = migrateDocumentIfNeeded(documentData);
      
      // Force save the migrated data
      localStorage.setItem(`document-${documentId}`, JSON.stringify(migratedData));
      console.log('✅ Document migration forced and saved');
      console.log('💡 Recommendation: Refresh the page to see changes');
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ Error during forced migration:', error);
    return false;
  }
};

// Export diagnostic functions for console use
if (typeof window !== 'undefined') {
  window.DocForgeDiagnostic = {
    diagnose: diagnoseDocumentData,
    clearCache: clearDocumentCache,
    forceMigrate: forceMigrateDocument
  };
  
  console.log('🔧 DocForge Diagnostic Tools Available:');
  console.log('- window.DocForgeDiagnostic.diagnose(documentId)');
  console.log('- window.DocForgeDiagnostic.clearCache(documentId)');
  console.log('- window.DocForgeDiagnostic.forceMigrate(documentId)');
}
