import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import CloudSaveIndicator from '../../../components/ui/CloudSaveIndicator';

/**
 * DocumentInfoHeader - Header component showing document information and action buttons
 * Displays document title, metadata, and provides quick access to key actions like Review, Export, and Zoom
 * Responsive design optimized for mobile, tablet, and desktop views
 */
const DocumentInfoHeader = ({
  documentTitle = 'Untitled Document',
  documentData = null,
  generatedContent = null,
  currentPhase = 'Edit Content',
  onReviewClick = null,
  onExportClick = null,
  saveStatus = 'saved',
  lastSaved = null,
  className = '',
  // New props for customizing buttons based on phase
  primaryButtonText = null,
  primaryButtonIcon = null,
  primaryButtonAction = null,
  secondaryButtonText = null,
  secondaryButtonIcon = null,
  secondaryButtonAction = null,
  // Prop to control save status visibility
  showSaveStatus = true
}) => {
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // Utility function to format time ago
  const formatTimeAgo = (date) => {
    if (!date) return '';
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 10) return 'just now';
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Get document title from various sources
  const getDocumentTitle = () => {
    if (generatedContent?.title) return generatedContent.title;
    if (documentData?.documentPurpose?.title) return documentData.documentPurpose.title;
    return documentTitle;
  };

  // Export options
  const exportOptions = [
    { label: 'Export as PDF', icon: 'FileText', action: () => onExportClick?.('pdf') },
    { label: 'Export as DOCX', icon: 'FileText', action: () => onExportClick?.('docx') },
    { label: 'Export as HTML', icon: 'Code', action: () => onExportClick?.('html') },
  ];

  return (
    <div className={`w-full bg-white border-b border-gray-200 shadow-sm ${className}`}>
      <div className="px-4 sm:px-6 lg:px-8 py-3">
        <div className="flex items-center justify-between">
          {/* Left Side - Document Title */}
          <div className="flex-1 min-w-0">
            <h1 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">
              {getDocumentTitle()}
            </h1>
            {/* Document metadata - hidden on mobile */}
            <div className="hidden sm:flex items-center space-x-4 mt-1 text-sm text-gray-500">
              {generatedContent?.metadata?.generatedAt && (
                <span className="flex items-center space-x-1">
                  <Icon name="Calendar" size={14} />
                  <span>{new Date(generatedContent.metadata.generatedAt).toLocaleDateString()}</span>
                </span>
              )}
            </div>
          </div>

          {/* Right Side - Action Buttons */}
          <div className="flex items-center space-x-2 ml-4">
            {/* Desktop Actions */}
            <div className="hidden md:flex items-center space-x-2">
              {/* Auto-save status indicator - only show if showSaveStatus is true */}
              {showSaveStatus && (
                <div className="flex items-center space-x-2 mr-2">
                  <CloudSaveIndicator
                    size="small"
                    showCheckmark={saveStatus === 'saved'}
                    title={
                      saveStatus === 'saving' ? 'Saving document...' :
                      saveStatus === 'saved' ? 'Document saved' :
                      'Save failed - please try again'
                    }
                    color="text-gray-600"
                  />
                  {saveStatus === 'saved' && lastSaved && (
                    <span className="text-xs text-gray-500">
                      Saved {formatTimeAgo(lastSaved)}
                    </span>
                  )}
                  {saveStatus === 'saving' && (
                    <span className="text-xs text-gray-500">
                      Saving...
                    </span>
                  )}
                  {saveStatus === 'error' && (
                    <span className="text-xs text-gray-500">
                      Save failed
                    </span>
                  )}
                </div>
              )}

              {/* Secondary Action Button (Back/Navigation) */}
              {secondaryButtonAction && (
                <Button
                  variant="outline"
                  onClick={secondaryButtonAction}
                  className="px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} className="mr-2" />
                  {secondaryButtonText || "Back"}
                </Button>
              )}

              {/* Primary Action Button */}
              <Button
                onClick={primaryButtonAction || onReviewClick}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 text-sm font-medium touch-manipulation"
              >
                <Icon name={primaryButtonIcon || "Eye"} size={16} className="mr-2" />
                {primaryButtonText || "Review"}
              </Button>

              {/* Export Dropdown (fallback when no secondary action) */}
              {!secondaryButtonAction && (
                <div className="relative">
                  <Button
                    variant="outline"
                    onClick={() => setShowExportDropdown(!showExportDropdown)}
                    className="px-3 py-2 text-sm font-medium"
                  >
                    <Icon name="Download" size={16} className="mr-2" />
                    Export
                    <Icon name="ChevronDown" size={14} className="ml-1" />
                  </Button>

                  {showExportDropdown && (
                    <>
                      <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                        <div className="py-1">
                          {exportOptions.map((option, index) => (
                            <button
                              key={index}
                              onClick={() => {
                                option.action();
                                setShowExportDropdown(false);
                              }}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                            >
                              <Icon name={option.icon} size={16} />
                              <span>{option.label}</span>
                            </button>
                          ))}
                        </div>
                      </div>
                      <div
                        className="fixed inset-0 z-40"
                        onClick={() => setShowExportDropdown(false)}
                      />
                    </>
                  )}
                </div>
              )}

            </div>

            {/* Tablet Actions - Touch-friendly sizing */}
            <div className="hidden sm:flex md:hidden items-center space-x-2">
              {/* Auto-save status indicator for tablet - only show if showSaveStatus is true */}
              {showSaveStatus && (
                <div className="flex items-center space-x-2 mr-2">
                  <CloudSaveIndicator
                    size="small"
                    showCheckmark={saveStatus === 'saved'}
                    title={
                      saveStatus === 'saving' ? 'Saving document...' :
                      saveStatus === 'saved' ? 'Document saved' :
                      'Save failed - please try again'
                    }
                    color="text-gray-600"
                  />
                </div>
              )}

              {/* Secondary Action Button (Back/Navigation) */}
              {secondaryButtonAction && (
                <Button
                  variant="outline"
                  onClick={secondaryButtonAction}
                  className="px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} />
                </Button>
              )}

              {/* Primary Action Button */}
              <Button
                onClick={primaryButtonAction || onReviewClick}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 text-sm font-medium touch-manipulation"
              >
                <Icon name={primaryButtonIcon || "Eye"} size={16} />
              </Button>

              {/* Export Button (fallback when no secondary action) */}
              {!secondaryButtonAction && (
                <Button
                  variant="outline"
                  onClick={() => setShowExportDropdown(!showExportDropdown)}
                  className="px-4 py-2.5 touch-manipulation"
                >
                  <Icon name="Download" size={16} />
                </Button>
              )}

            </div>

            {/* Mobile Menu Button - Touch-friendly */}
            <div className="sm:hidden relative">
              <Button
                variant="ghost"
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-3 touch-manipulation"
                aria-label="More options"
              >
                <Icon name="MoreVertical" size={20} />
              </Button>

              {/* Mobile Menu - Overlay dropdown */}
              {showMobileMenu && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-1100">
                  <div className="flex flex-col">
                    {/* Secondary Action Button (Back/Navigation) */}
                    {secondaryButtonAction && (
                      <button
                        onClick={() => {
                          secondaryButtonAction?.();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 flex items-center touch-manipulation"
                      >
                        <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} className="mr-3" />
                        {secondaryButtonText || "Back"}
                      </button>
                    )}

                    {/* Primary Action Button */}
                    <button
                      onClick={() => {
                        (primaryButtonAction || onReviewClick)?.();
                        setShowMobileMenu(false);
                      }}
                      className="w-full text-left px-3 py-2.5 text-sm text-blue-600 hover:bg-blue-50 flex items-center touch-manipulation"
                    >
                      <Icon name={primaryButtonIcon || "Eye"} size={16} className="mr-3" />
                      {primaryButtonText || "Review"}
                    </button>

                    {/* Export Button (fallback when no secondary action) */}
                    {!secondaryButtonAction && (
                      <button
                        onClick={() => {
                          setShowExportDropdown(!showExportDropdown);
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 flex items-center touch-manipulation"
                      >
                        <Icon name="Download" size={16} className="mr-3" />
                        Export
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Backdrop overlay to close mobile menu */}
        {showMobileMenu && (
          <div
            className="fixed inset-0 z-1090"
            onClick={() => setShowMobileMenu(false)}
          />
        )}
      </div>
    </div>
  );
};

export default DocumentInfoHeader;
