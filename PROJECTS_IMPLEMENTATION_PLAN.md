# DocForge AI Projects Page - Simplified Implementation Plan

## Overview

This implementation plan focuses on completing the core project management functionality for DocForge AI without collaboration or sharing features. The approach uses a simplified single-table database design that matches the current localStorage structure.

## Database Schema (Completed ✅)

### Single Projects Table Design
- **projects**: Contains both metadata and content in JSONB columns
- **project_activities**: Audit trail for project actions
- No collaboration or sharing tables (simplified approach)

**Key Benefits:**
- Matches current localStorage structure
- Simpler queries (no joins needed)
- Easier to implement and maintain
- Can add features later if needed

## Implementation Phases

### Phase 1: Database Setup (15-20 minutes)

#### 1.1 Run Database Migration ✅
```bash
# In Supabase SQL Editor, run:
database/migrate-projects.sql
```

**Manual Testing Checklist:**
- [ ] Verify tables created: `projects`, `project_activities`
- [ ] Check RLS policies are active
- [ ] Test basic insert/select operations
- [ ] Verify indexes are created

**Rollback:** Drop tables if issues arise:
```sql
DROP TABLE IF EXISTS public.project_activities;
DROP TABLE IF EXISTS public.projects;
```

#### 1.2 Test Database Schema (10 minutes)
- Insert sample project data
- Verify RLS policies work correctly
- Test JSONB queries for content

### Phase 2: API Service Integration (20-25 minutes)

#### 2.1 Integrate Projects Service ✅
- **File**: `src/services/projectsService.js` (completed)
- **Features**: CRUD operations, duplicate functionality, word count calculation

#### 2.2 Test API Service (15 minutes)
**Manual Testing:**
```javascript
// Test in browser console
import { projectsService } from './services/projectsService';

// Test create
const result = await projectsService.createProject({
  title: "Test Project",
  document_type: "ebook",
  category: "eBooks",
  questionnaire_data: { test: "data" }
});

// Test fetch
const projects = await projectsService.getUserProjects();
```

**Rollback:** Revert to mock data in projects page if API fails

### Phase 3: Projects Page Integration (25-30 minutes)

#### 3.1 Replace Mock Data with Real API Calls
**File**: `src/pages/projects/index.jsx`

**Changes:**
1. Import projectsService
2. Replace mockProjects with API calls
3. Add error handling and loading states
4. Update filtering and sorting to work with real data

**Testing:**
- [ ] Projects load from database
- [ ] Filtering by category works
- [ ] Sorting functions correctly
- [ ] Loading states display properly
- [ ] Error handling works

**Rollback:** Restore original mock data implementation

#### 3.2 Update Document Creator Integration
**File**: `src/pages/document-creator/components/steps/ContentDetailsStep.jsx`

**Changes:**
1. Replace localStorage with projectsService.createProject()
2. Handle success/error responses
3. Navigate to projects page after creation

**Testing:**
- [ ] Document creation saves to database
- [ ] Navigation works after creation
- [ ] Error handling for failed saves

### Phase 4: Missing Features Implementation (45-60 minutes)

#### 4.1 Project Preview Modal (20 minutes)
**New File**: `src/pages/projects/components/ProjectPreviewModal.jsx`

**Features:**
- Display project content in modal
- Navigate through chapters
- Close/escape functionality

**Testing:**
- [ ] Modal opens on preview button click
- [ ] Content displays correctly
- [ ] Navigation works
- [ ] Modal closes properly

#### 4.2 Project Duplication (15 minutes)
**Update**: `src/pages/projects/index.jsx`

**Implementation:**
```javascript
const handleDuplicateProject = async (project) => {
  const result = await projectsService.duplicateProject(project.id);
  if (result.success) {
    // Refresh projects list
    loadProjects();
  }
};
```

**Testing:**
- [ ] Duplicate button creates new project
- [ ] New project has "(Copy)" suffix
- [ ] Original content is preserved
- [ ] Projects list refreshes

#### 4.3 Project Deletion with Confirmation (15 minutes)
**New Component**: `src/pages/projects/components/DeleteConfirmationModal.jsx`

**Features:**
- Confirmation dialog
- Project title display
- Cancel/confirm actions
- Loading state during deletion

**Testing:**
- [ ] Confirmation modal appears
- [ ] Cancel button works
- [ ] Delete button removes project
- [ ] Projects list updates

#### 4.4 Real-time Status Updates (15 minutes)
**Implementation**: Add Supabase real-time subscriptions

```javascript
useEffect(() => {
  const subscription = supabase
    .channel('projects')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'projects' },
      (payload) => {
        // Update projects list
        loadProjects();
      }
    )
    .subscribe();

  return () => subscription.unsubscribe();
}, []);
```

### Phase 5: Document Editor Integration (20-25 minutes)

#### 5.1 Update Document Editor to Load from Database
**File**: `src/pages/document-editor/index.jsx`

**Changes:**
1. Load project data from database instead of localStorage
2. Save changes back to database
3. Update progress and status

**Testing:**
- [ ] Editor loads existing projects
- [ ] Changes save to database
- [ ] Progress updates correctly

### Phase 6: Testing Strategy (30-40 minutes)

#### 6.1 Unit Tests
**Files to create:**
- `src/services/__tests__/projectsService.test.js`
- `src/pages/projects/__tests__/Projects.test.js`
- `src/pages/projects/components/__tests__/ProjectCard.test.js`

#### 6.2 Integration Tests
- Database operations
- API service with Supabase
- Component interactions

#### 6.3 End-to-End Tests
- Complete project creation workflow
- Project management operations
- Error scenarios

## Rollback Strategy

### Database Rollback
```sql
-- Remove projects tables
DROP TABLE IF EXISTS public.project_activities;
DROP TABLE IF EXISTS public.projects;

-- Remove RLS policies
DROP POLICY IF EXISTS "Users can view their own projects" ON public.projects;
-- ... (other policies)
```

### Code Rollback
1. **Projects Page**: Restore original mock data implementation
2. **Document Creator**: Restore localStorage implementation
3. **API Service**: Remove projectsService imports

### Incremental Rollback
Each phase can be rolled back independently:
- Phase 1: Drop database tables
- Phase 2: Remove API service integration
- Phase 3: Restore mock data
- Phase 4: Remove new features
- Phase 5: Restore localStorage in editor

## Success Criteria

### Functional Requirements ✅
- [x] Projects display from database
- [ ] Create new projects via Document Creator
- [ ] Edit existing projects
- [ ] Duplicate projects
- [ ] Delete projects with confirmation
- [ ] Preview project content
- [ ] Filter and sort projects

### Technical Requirements ✅
- [x] Database schema implemented
- [x] API service layer complete
- [ ] Error handling throughout
- [ ] Loading states for async operations
- [ ] Responsive design maintained
- [ ] Performance optimized

### User Experience ✅
- [x] Maintains existing UI/UX patterns
- [x] Floating menus and hover interactions preserved
- [ ] Smooth transitions and feedback
- [ ] Consistent with application design

## Next Steps After Implementation

1. **Performance Optimization**: Add caching and pagination
2. **Search Functionality**: Implement full-text search
3. **Export Features**: Add PDF/DOCX export from projects page
4. **Templates**: Convert projects to reusable templates
5. **Analytics**: Track project creation and usage metrics

This simplified approach focuses on core functionality while maintaining the flexibility to add advanced features later.
