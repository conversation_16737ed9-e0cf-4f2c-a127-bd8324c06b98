import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import DocumentCanvasMinimal from './components/DocumentCanvasMinimal';
import DocumentWorkflowHeader from './components/DocumentWorkflowHeader';
import DocumentInfoHeader from './components/DocumentInfoHeader';
import DocumentSidebar from './components/DocumentSidebar';
import LoadingScreen from './components/LoadingScreen';
import ImageSelectionModal from './components/ImageSelectionModal';
import { generateDocumentContent } from '../../services/aiService';
import useReviewMode from './hooks/useReviewMode';
import { generateImageSuggestions } from '../../services/unsplashService';
import { exportDocument } from '../../services/exportService';
// Image migration removed - using simple read-only document display
import '../../utils/documentDataDiagnostic'; // Load diagnostic tools
import '../../utils/cacheManager'; // Load cache management tools
import { useErrorMonitor } from '../../utils/useErrorMonitor';
import { handlePhaseTransition } from '../../utils/progressUtils';

/**
 * DocumentEditor - Main document editing interface
 * Canvas-style editor similar to Designrr with AI content generation
 */
const DocumentEditor = () => {
  const { documentId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { contentMargin } = useSidebar();
  
  // Initialize error monitoring for this component
  const logger = useErrorMonitor('DocumentEditor', { documentId });
  
  const [documentData, setDocumentData] = useState(null);
  const [generatedContent, setGeneratedContent] = useState(null);
  const [imageSuggestions, setImageSuggestions] = useState({});
  const [isGenerating, setIsGenerating] = useState(true);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');

  // Image selection modal state
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedChapterId, setSelectedChapterId] = useState(null);
  const [error, setError] = useState(null);
  const [saveStatus, setSaveStatus] = useState('saved'); // 'saving' | 'saved' | 'error'
  const [lastSaved, setLastSaved] = useState(null);

  // Review Phase State Management using custom hook
  const {
    currentPhase,
    reviewData,
    isReviewMode,
    isEditMode,
    reviewCompletionPercentage,
    isReviewReadyForCompletion,
    enterReviewMode,
    exitReviewMode,
    updateReviewData,
    setValidationResults,

    setExportReadiness,
    completeReview
  } = useReviewMode('Edit Content');

  // Using simple read-only document display

  // Load document data from localStorage or API
  useEffect(() => {
    const loadDocumentData = () => {
      try {
        logger.info('Loading document data', { documentId });
        
        // Try to get document data from localStorage (passed from questionnaire)
        const storedData = localStorage.getItem(`document-${documentId}`);
        if (storedData) {
          const data = JSON.parse(storedData);

          logger.info('Document data loaded from localStorage', {
            documentId,
            hasGeneratedContent: !!data.generatedContent,
            lastModified: data.lastModified,
            createdAt: data.createdAt
          });

          // Migration removed - using simple read-only document display
          
          // Clean up any legacy placedImages if they exist
          if (data.generatedContent && data.generatedContent.placedImages) {
            logger.info('Cleaning up legacy placedImages data', {
              documentId
            });
            delete data.generatedContent.placedImages;
            localStorage.setItem(`document-${documentId}`, JSON.stringify(data));
          }

          setDocumentData(data);
          return data;
        }
        
        // If no stored data, redirect back to document creator
        logger.warn('No document data found, redirecting to creator', { documentId });
        navigate('/document-creator');
        return null;
      } catch (error) {
        logger.error(error, {
          action: 'load_document_data',
          documentId,
          storageKey: `document-${documentId}`
        });
        setError('Failed to load document data');
        return null;
      }
    };

    const data = loadDocumentData();
    if (data) {
      generateDocument(data).catch(error => {
        console.error('Error in generateDocument:', error);
        setError(error.message || 'Failed to generate document');
      });
    }
  }, [documentId, navigate]);

  // Review mode activation removed - now handled by dedicated route

  // Review mode handlers removed - now handled by dedicated route

  // Handle navigation to publish phase
  const handleNavigateToPublish = () => {
    navigate(`/document-editor/${documentId}/publish`);
  };

  // Generate document content using AI
  const generateDocument = async (data) => {
    logger.info('Starting document generation', {
      documentType: data.documentPurpose?.primaryType || 'unknown',
      hasOutline: !!data.documentOutline?.generatedOutline,
      hasImportedContent: !!data.documentPurpose?.importedContent?.extractedContent
    });
    
    try {
      setIsGenerating(true);
      setError(null);
      
      // Step 1: Generate content
      setCurrentStep('Generating document content...');
      setGenerationProgress(20);
      logger.debug('Generating document content');
      
      const contentResult = await generateDocumentContent(data);
      
      if (!contentResult.success) {
        logger.error(new Error('Failed to generate document content'), {
          step: 'content_generation',
          success: false
        });
        throw new Error('Failed to generate document content');
      }
      
      logger.info('Document content generated successfully', {
        wordCount: contentResult.content.wordCount,
        chapterCount: contentResult.content.chapters?.length || 0
      });
      
      setGeneratedContent(contentResult.content);
      setGenerationProgress(60);

      // Step 2: Use image suggestions if enabled (auto-generated from DocumentOutlineStep)
      if (data.imageAddition?.enabled) {
        setCurrentStep('Loading image suggestions...');
        setGenerationProgress(80);
        logger.debug('Loading AI-generated image suggestions');

        try {
          // Check if suggestions were already generated in DocumentOutlineStep
          let imageSuggestions = data.imageAddition?.suggestions;

          if (!imageSuggestions || Object.keys(imageSuggestions).length === 0) {
            // Fallback: generate suggestions if not available (shouldn't happen with new workflow)
            logger.warn('Image suggestions not found, generating as fallback');
            imageSuggestions = await generateImageSuggestions(
              data,
              data.documentOutline?.generatedOutline
            );
          } else {
            logger.info('Using pre-generated image suggestions from outline step');
          }

          setImageSuggestions(imageSuggestions);

          // DEBUG: Log image suggestions structure
          console.log('🔍 DEBUG: Image suggestions set:', imageSuggestions);
          console.log('🔍 DEBUG: Image suggestions keys:', Object.keys(imageSuggestions));
          console.log('🔍 DEBUG: First chapter data:', imageSuggestions['chapter-1']);

          logger.info('Image suggestions generated', {
            suggestionCount: Object.keys(imageSuggestions).length
          });
        } catch (imageError) {
          // Non-fatal error - log but continue
          logger.warn('Failed to generate image suggestions', {
            error: imageError.message
          });
        }
      }
      
      // Step 3: Complete
      setCurrentStep('Finalizing document...');
      setGenerationProgress(100);
      logger.info('Document generation completed');

      // Update project progress to reflect transition from Generate → Edit Content
      try {
        await handlePhaseTransition(documentId, 'Edit Content', 'Generate');
        logger.info('Project progress updated to Edit Content phase (50%)');
      } catch (progressError) {
        // Non-fatal error - log but continue
        logger.warn('Failed to update project progress', {
          error: progressError.message,
          documentId
        });
      }

      // Small delay for smooth UX
      setTimeout(() => {
        setIsGenerating(false);
      }, 1000);
      
    } catch (error) {
      logger.error(error, {
        step: 'document_generation',
        documentId: documentId
      });
      setError(error.message || 'Failed to generate document');
      setIsGenerating(false);
    }
  };

  // Handle content updates from the editor
  const handleContentUpdate = (updatedContent) => {
    // Check if updatedContent is HTML string (from editor) or structured object
    if (typeof updatedContent === 'string') {
      // HTML content from editor - update the existing generatedContent structure
      const updatedGeneratedContent = {
        ...generatedContent,
        // Store the raw HTML for potential future use
        editorHTML: updatedContent,
        lastModified: new Date().toISOString()
      };

      setGeneratedContent(updatedGeneratedContent);

      logger.debug('Editor HTML content updated', {
        htmlLength: updatedContent.length,
        documentId
      });

      // Auto-save to localStorage with updated HTML
      setSaveStatus('saving');
      const documentToSave = {
        ...documentData,
        generatedContent: updatedGeneratedContent,
        lastModified: new Date().toISOString()
      };

      try {
        localStorage.setItem(`document-${documentId}`, JSON.stringify(documentToSave));
        setSaveStatus('saved');
        setLastSaved(new Date());
        logger.info('Document auto-saved with editor changes', {
          documentId,
          htmlLength: updatedContent.length
        });
      } catch (error) {
        setSaveStatus('error');
        logger.error(error, {
          action: 'auto_save_editor_content',
          documentId,
          storageKey: `document-${documentId}`
        });
      }

      return;
    }

    // Handle structured content updates (legacy path)
    setGeneratedContent(updatedContent);

    // Enhanced debugging for content updates
    const chaptersWithImages = updatedContent.chapters ?
      updatedContent.chapters.filter(ch => ch.content && ch.content.includes('![')).length : 0;

    const contentMetrics = {
      title: updatedContent.title,
      hasLegacyPlacedImages: !!updatedContent.placedImages,
      legacyPlacedImagesCount: updatedContent.placedImages ?
        Object.keys(updatedContent.placedImages).reduce((total, key) =>
          total + updatedContent.placedImages[key].length, 0
        ) : 0,
      chaptersWithBlockImages: chaptersWithImages,
      totalChapters: updatedContent.chapters ? updatedContent.chapters.length : 0
    };

    logger.debug('Structured content updated and auto-saving', contentMetrics);

    // Warn if legacy placedImages are still present
    if (updatedContent.placedImages) {
      logger.warn('Legacy placedImages detected in content update', {
        issue: 'legacy_images_present',
        documentId
      });
    }

    // Auto-save to localStorage
    const documentToSave = {
      ...documentData,
      generatedContent: updatedContent,
      lastModified: new Date().toISOString()
    };

    try {
      localStorage.setItem(`document-${documentId}`, JSON.stringify(documentToSave));
      logger.info('Document auto-saved successfully', {
        documentId,
        hasPlacedImages: !!updatedContent.placedImages,
        wordCount: updatedContent.wordCount || 'unknown'
      });
    } catch (error) {
      logger.error(error, {
        action: 'auto_save',
        documentId,
        storageKey: `document-${documentId}`
      });
    }
  };

  // Image selection modal handlers
  const handleOpenImageModal = (chapterId) => {
    setSelectedChapterId(chapterId);
    setIsImageModalOpen(true);
    logger.info('Opening image selection modal for chapter:', chapterId);
  };

  const handleCloseImageModal = () => {
    setIsImageModalOpen(false);
    setSelectedChapterId(null);
  };

  const handleImageSelect = (imageData) => {
    logger.info('Image selected:', imageData);
    // TODO: Implement image insertion into document content
    // This would integrate with the Tiptap editor to insert the selected image
    console.log('Selected image for chapter:', selectedChapterId, imageData);
    handleCloseImageModal();
  };

  // Export document
  const handleExport = async (format) => {
    if (!documentData || !generatedContent) {
      alert('Document data not available for export');
      return;
    }

    try {
      console.log(`Exporting document as ${format}`);
      const result = await exportDocument(format, documentData, generatedContent);

      if (result.success) {
        console.log('Export successful:', result.message);
        // The export service handles the download automatically
      } else {
        console.error('Export failed:', result.error);
        alert(`Export failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Export error:', error);
      alert(`Export failed: ${error.message}`);
    }
  };

  // Handle review button click
  const handleReviewClick = () => {
    handlePhaseNavigation('Review');
  };

  // Handle phase navigation in workflow header
  const handlePhaseNavigation = (phase) => {
    console.log(`Navigating to phase: ${phase}`);

    switch (phase) {
      case 'Generate':
        // Clear any cached AI-generated content before navigating back to wizard
        console.log('Clearing cached AI content before returning to Generate phase');

        // Clear cached AI-generated content from localStorage
        const aiContentKeys = [
          'titleSelection.generatedTitles',
          'topicAndNiche.availableSubNiches',
          'documentOutline.generatedOutline'
        ];

        aiContentKeys.forEach(key => {
          const storageKey = `docforge_cached_${key}`;
          localStorage.removeItem(storageKey);
        });

        // Navigate back to document creator with reset flag
        navigate('/document-creator', {
          state: { resetAIContent: true }
        });
        break;
      case 'Edit Content':
        // Already on edit content phase
        console.log('Already on Edit Content phase');
        break;
      case 'Review':
        // Save current document state before navigating to review phase
        console.log('Saving document before navigating to Review phase');
        if (documentData && generatedContent) {
          const documentToSave = {
            ...documentData,
            generatedContent: generatedContent,
            lastModified: new Date().toISOString()
          };

          try {
            localStorage.setItem(`document-${documentId}`, JSON.stringify(documentToSave));
            console.log('Document saved successfully before Review navigation');
          } catch (error) {
            console.error('Failed to save document before Review navigation:', error);
          }
        }

        // Update progress before navigating to review phase
        handlePhaseTransition(documentId, 'Review', 'Edit Content')
          .then(() => {
            console.log('Progress updated for Review phase');
            navigate(`/document-editor/${documentId}/review`);
          })
          .catch(error => {
            console.warn('Failed to update progress for Review phase:', error);
            // Still navigate even if progress update fails
            navigate(`/document-editor/${documentId}/review`);
          });
        break;
      case 'Publish':
        // Update progress before navigating to publish phase
        handlePhaseTransition(documentId, 'Publish', 'Review')
          .then(() => {
            console.log('Progress updated for Publish phase');
            navigate(`/document-editor/${documentId}/publish`);
          })
          .catch(error => {
            console.warn('Failed to update progress for Publish phase:', error);
            // Still navigate even if progress update fails
            navigate(`/document-editor/${documentId}/publish`);
          });
        break;
      default:
        console.warn(`Unknown phase: ${phase}`);
    }
  };

  if (isGenerating) {
    return (
      <LoadingScreen 
        progress={generationProgress}
        currentStep={currentStep}
        documentTitle={documentData?.titleSelection?.selectedTitle || 'Your Document'}
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <h3 className="font-bold">Error</h3>
            <p>{error}</p>
          </div>
          <button
            onClick={() => navigate('/document-creator')}
            className="bg-primary text-white px-6 py-2 rounded hover:bg-primary/90"
          >
            Back to Document Creator
          </button>
        </div>
      </div>
    );
  }

  if (!generatedContent) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0 pt-16`}>
          {/* Document Workflow Header */}
          <DocumentWorkflowHeader
            currentPhase={currentPhase}
            onPhaseClick={handlePhaseNavigation}
          />

          {/* Document Info Header */}
          <DocumentInfoHeader
            documentTitle="Loading..."
            documentData={documentData}
            generatedContent={null}
            currentPhase={currentPhase}
            onReviewClick={handleReviewClick}
            onExportClick={handleExport}
            saveStatus={saveStatus}
            lastSaved={lastSaved}
          />

          {/* Loading Content */}
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading document...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />

      <main className={`${contentMargin} ml-0 pt-16 sidebar-layout`}>
        {/* Document Workflow Header */}
        <DocumentWorkflowHeader
          currentPhase={currentPhase}
          onPhaseClick={handlePhaseNavigation}
        />

        {/* Document Info Header */}
        <DocumentInfoHeader
          documentTitle={generatedContent?.title}
          documentData={documentData}
          generatedContent={generatedContent}
          currentPhase={currentPhase}
          onReviewClick={handleReviewClick}
          onExportClick={handleExport}
          saveStatus={saveStatus}
          lastSaved={lastSaved}
        />

        {/* Document Canvas Container - Optimized height without zoom controls */}
        <div className="bg-gray-50 overflow-hidden" style={{
          height: 'calc(100vh - 8.5rem)', // Desktop: workflow header (5rem) + simplified info header (3.5rem)
          minHeight: '400px' // Ensure minimum usable height on small screens
        }}>
          {/* Minimal Canvas with AI Content Integration */}
          <DocumentCanvasMinimal
            content={generatedContent}
            onContentChange={handleContentUpdate}
            isLoading={isGenerating}
            imageSuggestions={imageSuggestions}
            onOpenImageModal={handleOpenImageModal}
          />
        </div>
      </main>

      {/* Image Selection Modal */}
      <ImageSelectionModal
        isOpen={isImageModalOpen}
        onClose={handleCloseImageModal}
        onImageSelect={handleImageSelect}
        imageSuggestions={imageSuggestions}
        chapterId={selectedChapterId}
        isReviewMode={false}
      />
    </div>
  );
};

export default DocumentEditor;
