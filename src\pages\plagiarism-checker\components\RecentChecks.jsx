import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RecentChecks = ({ onSelectCheck, onDeleteCheck }) => {
  const [sortBy, setSortBy] = useState('date');
  const [filterBy, setFilterBy] = useState('all');

  const recentChecks = [
    {
      id: 1,
      name: "Research Paper - Climate Change.docx",
      date: new Date(Date.now() - 2 * 60 * 60 * 1000),
      similarity: 12,
      aiContent: 8,
      status: 'completed',
      wordCount: 3250,
      sources: 15,
      type: 'document'
    },
    {
      id: 2,
      name: "Business Proposal Draft",
      date: new Date(Date.now() - 5 * 60 * 60 * 1000),
      similarity: 28,
      aiContent: 45,
      status: 'completed',
      wordCount: 1890,
      sources: 8,
      type: 'text'
    },
    {
      id: 3,
      name: "Literature Review - AI Ethics.pdf",
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      similarity: 35,
      aiContent: 15,
      status: 'completed',
      wordCount: 4120,
      sources: 23,
      type: 'document'
    },
    {
      id: 4,
      name: "Marketing Strategy Report",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      similarity: 8,
      aiContent: 62,
      status: 'completed',
      wordCount: 2750,
      sources: 5,
      type: 'text'
    },
    {
      id: 5,
      name: "Thesis Chapter 3 - Methodology.docx",
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      similarity: 18,
      aiContent: 12,
      status: 'processing',
      wordCount: 5680,
      sources: 0,
      type: 'document'
    }
  ];

  const getSimilarityColor = (percentage) => {
    if (percentage < 15) return 'text-success';
    if (percentage < 30) return 'text-warning';
    return 'text-error';
  };

  const getAIContentColor = (percentage) => {
    if (percentage < 25) return 'text-success';
    if (percentage < 50) return 'text-warning';
    return 'text-error';
  };

  const formatDate = (date) => {
    const now = new Date();
    const diff = now - date;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (hours < 1) return 'Just now';
    if (hours < 24) return `${hours}h ago`;
    if (days === 1) return 'Yesterday';
    return `${days} days ago`;
  };

  const getFileIcon = (type) => {
    switch (type) {
      case 'document':
        return 'FileText';
      case 'text':
        return 'Type';
      default:
        return 'File';
    }
  };

  const filteredChecks = recentChecks.filter(check => {
    if (filterBy === 'all') return true;
    if (filterBy === 'high-risk') return check.similarity > 30 || check.aiContent > 50;
    if (filterBy === 'processing') return check.status === 'processing';
    return true;
  });

  const sortedChecks = [...filteredChecks].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return b.date - a.date;
      case 'similarity':
        return b.similarity - a.similarity;
      case 'name':
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  return (
    <div className="bg-surface rounded-lg border border-border">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">Recent Checks</h3>
          <div className="flex items-center space-x-2">
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="text-sm border border-border rounded px-2 py-1 bg-surface text-text-primary"
            >
              <option value="all">All Checks</option>
              <option value="high-risk">High Risk</option>
              <option value="processing">Processing</option>
            </select>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="text-sm border border-border rounded px-2 py-1 bg-surface text-text-primary"
            >
              <option value="date">Sort by Date</option>
              <option value="similarity">Sort by Similarity</option>
              <option value="name">Sort by Name</option>
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-background rounded-lg">
            <div className="text-xl font-bold text-text-primary">{recentChecks.length}</div>
            <div className="text-xs text-text-secondary">Total Checks</div>
          </div>
          <div className="text-center p-3 bg-background rounded-lg">
            <div className="text-xl font-bold text-warning">
              {recentChecks.filter(c => c.similarity > 15).length}
            </div>
            <div className="text-xs text-text-secondary">Need Review</div>
          </div>
          <div className="text-center p-3 bg-background rounded-lg">
            <div className="text-xl font-bold text-success">
              {recentChecks.filter(c => c.similarity < 15).length}
            </div>
            <div className="text-xs text-text-secondary">Low Risk</div>
          </div>
        </div>
      </div>

      {/* Checks List */}
      <div className="max-h-96 overflow-y-auto">
        {sortedChecks.length === 0 ? (
          <div className="p-8 text-center">
            <Icon name="Search" size={32} className="mx-auto text-text-secondary mb-2" />
            <p className="text-text-secondary">No checks found</p>
          </div>
        ) : (
          <div className="divide-y divide-border">
            {sortedChecks.map((check) => (
              <div
                key={check.id}
                className="p-4 hover:bg-background cursor-pointer transition-micro"
                onClick={() => onSelectCheck(check)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="p-2 bg-background rounded-lg">
                      <Icon name={getFileIcon(check.type)} size={16} className="text-text-secondary" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium text-text-primary truncate">
                          {check.name}
                        </h4>
                        {check.status === 'processing' && (
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                            <span className="text-xs text-primary">Processing</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-xs text-text-secondary mb-2">
                        <span>{formatDate(check.date)}</span>
                        <span>•</span>
                        <span>{check.wordCount.toLocaleString()} words</span>
                        {check.sources > 0 && (
                          <>
                            <span>•</span>
                            <span>{check.sources} sources</span>
                          </>
                        )}
                      </div>

                      {check.status === 'completed' && (
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-1">
                            <Icon name="Search" size={12} className="text-text-secondary" />
                            <span className={`text-xs font-medium ${getSimilarityColor(check.similarity)}`}>
                              {check.similarity}% Similar
                            </span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Icon name="Bot" size={12} className="text-text-secondary" />
                            <span className={`text-xs font-medium ${getAIContentColor(check.aiContent)}`}>
                              {check.aiContent}% AI
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-1 ml-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log('Download report for', check.name);
                      }}
                    >
                      <Icon name="Download" size={14} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteCheck(check.id);
                      }}
                    >
                      <Icon name="Trash2" size={14} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentChecks;