/**
 * Unit tests for DOCX generation error handling
 */

import { jest } from '@jest/globals';

// Mock the docx library
jest.mock('docx', () => ({
    Document: jest.fn().mockImplementation((config) => ({ config })),
    Packer: {
        toBlob: jest.fn()
    },
    Paragraph: jest.fn().mockImplementation((config) => ({ type: 'paragraph', config })),
    TextRun: jest.fn().mockImplementation((config) => ({ type: 'textrun', config })),
    HeadingLevel: {
        HEADING_1: 'heading1',
        HEADING_2: 'heading2',
        HEADING_3: 'heading3'
    },
    AlignmentType: {
        CENTER: 'center'
    },
    ImageRun: jest.fn().mockImplementation((config) => ({ type: 'imagerun', config }))
}));

// Mock content processing service
jest.mock('../contentProcessingService.js', () => ({
    detectContentType: jest.fn().mockReturnValue('html'),
    validateDocumentData: jest.fn().mockReturnValue({ isValid: true, errors: [], warnings: [] }),
    validateContent: jest.fn().mockReturnValue({ isValid: true, errors: [], warnings: [] }),
    extractAndProcessImages: jest.fn().mockResolvedValue({
        totalImages: 0,
        successCount: 0,
        failureCount: 0,
        processedImages: [],
        failedImages: []
    }),
    processContentWithErrorHandling: jest.fn().mockResolvedValue({
        success: true,
        processedContent: [{ type: 'paragraph', text: 'Test content' }],
        errors: [],
        warnings: []
    }),
    generateImageErrorMessage: jest.fn().mockReturnValue(''),
    convertToDocxParagraphs: jest.fn().mockReturnValue([])
}));

import { Document, Packer } from 'docx';
import {
    generateDocxWithImages,
    generateAndDownloadDocxWithImages,
    createTitlePage,
    convertToDocxParagraphsWithImages
} from '../docxGenerationService.js';

describe('DOCX Generation Error Handling', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('generateDocxWithImages', () => {
        test('should handle invalid document data', async () => {
            const { validateDocumentData } = await import('../contentProcessingService.js');
            validateDocumentData.mockReturnValue({
                isValid: false,
                errors: ['Title is required'],
                warnings: []
            });

            const result = await generateDocxWithImages(
                null,
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.error).toContain('Document validation failed');
            expect(result.userMessage).toContain('document information');
        });

        test('should handle invalid content', async () => {
            const { validateContent } = await import('../contentProcessingService.js');
            validateContent.mockReturnValue({
                isValid: false,
                errors: ['Content is empty'],
                warnings: []
            });

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.error).toContain('Content validation failed');
            expect(result.userMessage).toContain('content appears to be invalid');
        });

        test('should handle content processing failure', async () => {
            const { processContentWithErrorHandling } = await import('../contentProcessingService.js');
            processContentWithErrorHandling.mockResolvedValue({
                success: false,
                errors: ['Processing failed'],
                warnings: []
            });

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.error).toContain('Content processing failed');
            expect(result.userMessage).toContain('process the document content');
        });

        test('should continue with image processing failure', async () => {
            const { extractAndProcessImages } = await import('../contentProcessingService.js');
            extractAndProcessImages.mockRejectedValue(new Error('Image processing failed'));

            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            expect(result.blob).toBeDefined();
            // Should continue without images
        });

        test('should handle DOCX blob generation failure', async () => {
            Packer.toBlob.mockRejectedValue(new Error('Memory error'));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.error).toContain('Document generation failed');
            expect(result.userMessage).toContain('too large or complex');
        });

        test('should handle DOCX blob generation timeout', async () => {
            Packer.toBlob.mockImplementation(() =>
                new Promise((resolve) => {
                    // Never resolve to simulate timeout
                })
            );

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.error).toContain('timeout');
        }, 35000); // Increase timeout for this test

        test('should use fallback for paragraph conversion failure', async () => {
            const { convertToDocxParagraphs } = await import('../contentProcessingService.js');

            // Mock convertToDocxParagraphsWithImages to throw error
            const originalConvert = convertToDocxParagraphsWithImages;
            jest.doMock('../docxGenerationService.js', () => ({
                ...jest.requireActual('../docxGenerationService.js'),
                convertToDocxParagraphsWithImages: jest.fn().mockRejectedValue(new Error('Conversion failed'))
            }));

            convertToDocxParagraphs.mockReturnValue([{ type: 'paragraph', text: 'Fallback content' }]);
            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            expect(convertToDocxParagraphs).toHaveBeenCalled();
        });

        test('should handle Document creation failure with fallback', async () => {
            Document.mockImplementationOnce(() => {
                throw new Error('Document creation failed');
            });

            // Second call should succeed (fallback)
            Document.mockImplementationOnce((config) => ({ config }));
            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            expect(Document).toHaveBeenCalledTimes(2);
        });

        test('should generate success message with image statistics', async () => {
            const { extractAndProcessImages, generateImageErrorMessage } = await import('../contentProcessingService.js');

            extractAndProcessImages.mockResolvedValue({
                totalImages: 3,
                successCount: 2,
                failureCount: 1,
                processedImages: [],
                failedImages: []
            });

            generateImageErrorMessage.mockReturnValue('1 image failed to process');
            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            expect(result.userMessage).toContain('1 image failed to process');
            expect(result.imageStats.totalImages).toBe(3);
            expect(result.imageStats.successfulImages).toBe(2);
        });

        test('should handle memory errors with appropriate message', async () => {
            Packer.toBlob.mockRejectedValue(new Error('Out of memory'));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.userMessage).toContain('too large to process');
        });

        test('should handle network errors with appropriate message', async () => {
            const { extractAndProcessImages } = await import('../contentProcessingService.js');
            extractAndProcessImages.mockRejectedValue(new Error('Network fetch failed'));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true); // Should continue despite image processing failure
        });
    });

    describe('generateAndDownloadDocxWithImages', () => {
        // Mock URL.createObjectURL and related DOM APIs
        beforeEach(() => {
            global.URL = {
                createObjectURL: jest.fn().mockReturnValue('blob:mock-url'),
                revokeObjectURL: jest.fn()
            };

            global.document = {
                createElement: jest.fn().mockReturnValue({
                    href: '',
                    download: '',
                    click: jest.fn(),
                    style: {}
                }),
                body: {
                    appendChild: jest.fn(),
                    removeChild: jest.fn()
                }
            };
        });

        test('should handle successful generation and download', async () => {
            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            const result = await generateAndDownloadDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            expect(result.message).toContain('successfully');
            expect(result.operationTime).toBeDefined();
        });

        test('should handle generation failure', async () => {
            const { validateDocumentData } = await import('../contentProcessingService.js');
            validateDocumentData.mockReturnValue({
                isValid: false,
                errors: ['Invalid document'],
                warnings: []
            });

            const result = await generateAndDownloadDocxWithImages(
                null,
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.userMessage).toBeDefined();
            expect(result.context).toBeDefined();
        });

        test('should handle download failure', async () => {
            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            // Mock download failure
            global.document.createElement.mockReturnValue({
                click: jest.fn().mockImplementation(() => {
                    throw new Error('Download blocked by browser');
                })
            });

            const result = await generateAndDownloadDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(false);
            expect(result.error).toContain('Download failed');
            expect(result.userMessage).toContain('download failed');
            expect(result.blob).toBeDefined(); // Should provide blob for manual handling
        });

        test('should provide timing information for long operations', async () => {
            // Mock slow operation
            Packer.toBlob.mockImplementation(() =>
                new Promise(resolve =>
                    setTimeout(() => resolve(new Blob(['test'])), 6000)
                )
            );

            const result = await generateAndDownloadDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            expect(result.message).toContain('completed in');
            expect(result.operationTime).toBeGreaterThan(5000);
        }, 10000);

        test('should categorize different error types', async () => {
            const testCases = [
                { error: 'Out of memory', expectedCategory: 'memory', expectedMessage: 'too large to process' },
                { error: 'Operation timeout', expectedCategory: 'timeout', expectedMessage: 'took too long' },
                { error: 'Network error', expectedCategory: 'network', expectedMessage: 'Network error' },
                { error: 'Permission denied', expectedCategory: 'permission', expectedMessage: 'Permission denied' },
                { error: 'Unknown error', expectedCategory: 'unknown', expectedMessage: 'unexpected error' }
            ];

            for (const testCase of testCases) {
                jest.clearAllMocks();
                Packer.toBlob.mockRejectedValue(new Error(testCase.error));

                const result = await generateAndDownloadDocxWithImages(
                    { title: 'Test Doc', author: 'Test Author' },
                    '<p>Test content</p>',
                    'html'
                );

                expect(result.success).toBe(false);
                expect(result.errorCategory).toBe(testCase.expectedCategory);
                expect(result.userMessage.toLowerCase()).toContain(testCase.expectedMessage.toLowerCase());
            }
        });
    });

    describe('Error Recovery and Fallbacks', () => {
        test('should recover from title page creation failure', async () => {
            // Mock createTitlePage to throw error
            const originalCreateTitlePage = createTitlePage;
            jest.doMock('../docxGenerationService.js', () => ({
                ...jest.requireActual('../docxGenerationService.js'),
                createTitlePage: jest.fn().mockImplementation(() => {
                    throw new Error('Title page creation failed');
                })
            }));

            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            // Should have created fallback title page
        });

        test('should handle empty content gracefully', async () => {
            const { convertToDocxParagraphs } = await import('../contentProcessingService.js');
            convertToDocxParagraphs.mockReturnValue([]);

            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

            const result = await generateDocxWithImages(
                { title: 'Test Doc', author: 'Test Author' },
                '<p>Test content</p>',
                'html'
            );

            expect(result.success).toBe(true);
            // Should have created fallback content paragraph
        });
    });
});

describe('DOCX Error Handling Integration', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('should handle complex failure and recovery scenario', async () => {
        const {
            extractAndProcessImages,
            processContentWithErrorHandling,
            convertToDocxParagraphs
        } = await import('../contentProcessingService.js');

        // Simulate image processing failure
        extractAndProcessImages.mockRejectedValue(new Error('Image processing failed'));

        // Simulate paragraph conversion failure, then success with fallback
        let conversionAttempts = 0;
        const mockConvertToDocxParagraphsWithImages = jest.fn().mockImplementation(() => {
            conversionAttempts++;
            if (conversionAttempts === 1) {
                throw new Error('Enhanced conversion failed');
            }
            return [{ type: 'paragraph', text: 'Fallback content' }];
        });

        // Mock successful basic conversion
        convertToDocxParagraphs.mockReturnValue([{ type: 'paragraph', text: 'Basic content' }]);

        // Mock successful document creation on second attempt
        let docAttempts = 0;
        Document.mockImplementation((config) => {
            docAttempts++;
            if (docAttempts === 1) {
                throw new Error('Enhanced document creation failed');
            }
            return { config };
        });

        Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));

        const result = await generateDocxWithImages(
            { title: 'Test Doc', author: 'Test Author' },
            '<p>Test content</p>',
            'html'
        );

        expect(result.success).toBe(true);
        expect(result.blob).toBeDefined();

        // Should have attempted enhanced features and fallen back to basic
        expect(Document).toHaveBeenCalledTimes(2);
        expect(convertToDocxParagraphs).toHaveBeenCalled();
    });
});