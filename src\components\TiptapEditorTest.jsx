import React, { useState } from 'react';
import DocumentCanvasMinimal from '../pages/document-editor/components/DocumentCanvasMinimal';

/**
 * TiptapEditorTest - Test component for verifying Tiptap editor functionality
 * 
 * This component provides a testing interface for:
 * - Step 1.1: Placeholder system testing
 * - Step 1.2: Content loading and conversion testing
 * - Integration testing with mock AI content
 */
const TiptapEditorTest = () => {
  const [currentContent, setCurrentContent] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [editorContent, setEditorContent] = useState('');

  // Mock AI content that matches the real structure
  const mockAIContent = {
    title: "Test Document: Digital Marketing Guide",
    introduction: {
      content: `# Introduction to Digital Marketing

Digital marketing has revolutionized how businesses connect with their audiences. In today's **digital-first world**, understanding the fundamentals of online marketing is crucial for success.

This guide will cover:
- Search Engine Optimization (SEO)
- Social Media Marketing
- Content Marketing strategies
- Email marketing best practices

Let's dive into the exciting world of digital marketing!`,
      wordCount: 65
    },
    chapters: [
      {
        id: "chapter-1",
        number: 1,
        title: "Search Engine Optimization",
        content: `## Chapter 1: Search Engine Optimization

SEO is the practice of optimizing your website to rank higher in search engine results. Here are the key components:

### On-Page SEO
- **Keyword research** and optimization
- *Meta descriptions* and title tags
- Content quality and relevance

### Off-Page SEO
- Link building strategies
- Social media signals
- Brand mentions

### Technical SEO
- Site speed optimization
- Mobile responsiveness
- XML sitemaps

Remember: SEO is a long-term strategy that requires patience and consistency.`,
        wordCount: 95,
        sections: ["On-Page SEO", "Off-Page SEO", "Technical SEO"]
      },
      {
        id: "chapter-2", 
        number: 2,
        title: "Social Media Marketing",
        content: `## Chapter 2: Social Media Marketing

Social media platforms offer unprecedented opportunities to engage with your audience directly.

### Platform Selection
Choose platforms based on your target audience:
- **Facebook**: Broad demographic reach
- **Instagram**: Visual content and younger audiences  
- **LinkedIn**: B2B and professional networking
- **Twitter**: Real-time engagement and news

### Content Strategy
- Share valuable, relevant content
- Maintain consistent posting schedule
- Engage with your community
- Use hashtags strategically

### Analytics and Measurement
Track key metrics like engagement rate, reach, and conversions to optimize your strategy.`,
        wordCount: 110,
        sections: ["Platform Selection", "Content Strategy", "Analytics"]
      }
    ],
    conclusion: {
      content: `# Conclusion

Digital marketing is an ever-evolving field that offers tremendous opportunities for businesses of all sizes. By implementing the strategies covered in this guide, you'll be well-equipped to:

- Improve your search engine visibility
- Build meaningful relationships on social media
- Create compelling content that converts
- Measure and optimize your marketing efforts

**Remember**: Success in digital marketing comes from consistent effort, continuous learning, and adapting to new trends and technologies.

Start implementing these strategies today, and watch your online presence grow!`,
      wordCount: 85
    },
    wordCount: 355,
    estimatedReadingTime: "2 minutes"
  };

  const handleContentChange = (html) => {
    setEditorContent(html);
  };

  const loadMockContent = () => {
    setIsLoading(true);

    // Simulate loading delay
    setTimeout(() => {
      setCurrentContent(mockAIContent);
      setIsLoading(false);
    }, 15);
  };

  const clearContent = () => {
    setCurrentContent(null);
    setEditorContent('');
  };

  const testPlaceholder = () => {
    setCurrentContent(null);
    setEditorContent('');
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Tiptap Editor Testing Interface
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={testPlaceholder}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Test Placeholder (Step 1.1)
            </button>
            
            <button
              onClick={loadMockContent}
              disabled={isLoading}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isLoading ? 'Loading...' : 'Load AI Content (Step 1.2)'}
            </button>
            
            <button
              onClick={clearContent}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Clear Content
            </button>
          </div>

          {/* Status Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Current Content:</strong> {currentContent ? 'AI Content Loaded' : 'Empty (Placeholder Mode)'}
            </div>
            <div>
              <strong>Loading State:</strong> {isLoading ? 'Loading...' : 'Ready'}
            </div>
            <div>
              <strong>Editor Content Length:</strong> {editorContent.length} characters
            </div>
            <div>
              <strong>Content Sections:</strong> {currentContent ? 
                `Title + ${currentContent.chapters?.length || 0} chapters + conclusion` : 'None'}
            </div>
          </div>
        </div>

        {/* Editor Container */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gray-50 px-6 py-3 border-b">
            <h2 className="text-lg font-semibold text-gray-900">
              Document Editor (DocumentCanvasMinimal)
            </h2>
          </div>
          
          <div style={{ height: '600px' }}>
            <DocumentCanvasMinimal
              content={currentContent}
              onContentChange={handleContentChange}
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Testing Checklist */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Manual Testing Checklist
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Step 1.1: Placeholder System</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>□ Click "Test Placeholder" - should show "regular text"</li>
                <li>□ Click in editor and type - placeholder disappears</li>
                <li>□ Delete all text - placeholder reappears</li>
                <li>□ Press Enter for new paragraph - shows placeholder</li>
                <li>□ Placeholder appears in gray italic text</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Step 1.2: Content Loading</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>□ Click "Load AI Content" - shows loading spinner</li>
                <li>□ Content loads with title, chapters, conclusion</li>
                <li>□ Markdown converts to proper HTML formatting</li>
                <li>□ Bold and italic text renders correctly</li>
                <li>□ Headings (H1, H2) display properly</li>
                <li>□ Lists render as bullet points</li>
                <li>□ Editing content triggers onContentChange</li>
              </ul>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default TiptapEditorTest;
