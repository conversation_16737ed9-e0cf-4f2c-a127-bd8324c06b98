import React, { useState } from 'react';
import Icon from '../../../../components/AppIcon';

/**
 * RatingScale - Flexible rating component supporting various scale types
 * (stars, numbers, emoji, custom icons) for questionnaire forms
 */
const RatingScale = ({
  value = null,
  onChange,
  min = 1,
  max = 5,
  step = 1,
  type = 'stars', // 'stars', 'numbers', 'emoji', 'thumbs', 'custom'
  size = 'medium', // 'small', 'medium', 'large'
  allowHalf = false,
  showLabels = true,
  labels = {},
  customIcons = {},
  required = false,
  disabled = false,
  className = '',
  label = '',
  description = '',
  helpText = '',
  showValue = true,
  allowClear = true,
  orientation = 'horizontal', // 'horizontal', 'vertical'
  spacing = 'normal', // 'tight', 'normal', 'loose'
  hoverEffect = true,
  color = 'primary', // 'primary', 'yellow', 'red', 'green', 'blue'
}) => {
  const [hoverValue, setHoverValue] = useState(null);

  // Generate scale values
  const scaleValues = [];
  for (let i = min; i <= max; i += step) {
    scaleValues.push(i);
    if (allowHalf && i < max) {
      scaleValues.push(i + 0.5);
    }
  }

  // Handle rating selection
  const handleRatingClick = (rating) => {
    if (disabled) return;
    
    if (allowClear && value === rating) {
      onChange(null);
    } else {
      onChange(rating);
    }
  };

  // Handle mouse events for hover effect
  const handleMouseEnter = (rating) => {
    if (hoverEffect && !disabled) {
      setHoverValue(rating);
    }
  };

  const handleMouseLeave = () => {
    if (hoverEffect) {
      setHoverValue(null);
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-4 h-4 text-sm';
      case 'large':
        return 'w-8 h-8 text-lg';
      default:
        return 'w-6 h-6 text-base';
    }
  };

  // Get spacing classes
  const getSpacingClasses = () => {
    const isVertical = orientation === 'vertical';
    switch (spacing) {
      case 'tight':
        return isVertical ? 'space-y-1' : 'space-x-1';
      case 'loose':
        return isVertical ? 'space-y-4' : 'space-x-4';
      default:
        return isVertical ? 'space-y-2' : 'space-x-2';
    }
  };

  // Get color classes
  const getColorClasses = (isActive) => {
    if (!isActive) return 'text-gray-300';
    
    switch (color) {
      case 'yellow':
        return 'text-yellow-400';
      case 'red':
        return 'text-red-500';
      case 'green':
        return 'text-green-500';
      case 'blue':
        return 'text-blue-500';
      default:
        return 'text-primary';
    }
  };

  // Render rating item based on type
  const renderRatingItem = (rating, index) => {
    const isActive = (hoverValue !== null ? hoverValue : value) >= rating;
    const isHalfValue = rating % 1 !== 0;
    
    let content;
    
    switch (type) {
      case 'stars':
        content = (
          <Icon 
            name={isActive ? 'Star' : 'Star'} 
            className={`${getSizeClasses()} ${getColorClasses(isActive)} ${
              isActive ? 'fill-current' : ''
            }`}
          />
        );
        break;
        
      case 'numbers':
        content = (
          <div className={`
            ${getSizeClasses()} flex items-center justify-center
            border-2 rounded-full font-semibold transition-all duration-200
            ${isActive 
              ? `border-${color} bg-${color} text-white` 
              : 'border-gray-300 text-gray-400 hover:border-gray-400'
            }
          `}>
            {Math.ceil(rating)}
          </div>
        );
        break;
        
      case 'emoji':
        const emojiMap = {
          1: '😞',
          2: '😕',
          3: '😐',
          4: '😊',
          5: '😍',
          ...customIcons
        };
        content = (
          <span className={`${getSizeClasses()} ${isActive ? 'opacity-100' : 'opacity-40'}`}>
            {emojiMap[Math.ceil(rating)] || '⭐'}
          </span>
        );
        break;
        
      case 'thumbs':
        content = (
          <Icon 
            name={rating <= max/2 ? 'ThumbsDown' : 'ThumbsUp'} 
            className={`${getSizeClasses()} ${getColorClasses(isActive)}`}
          />
        );
        break;
        
      case 'custom':
        const customIcon = customIcons[Math.ceil(rating)] || 'Circle';
        content = (
          <Icon 
            name={customIcon} 
            className={`${getSizeClasses()} ${getColorClasses(isActive)}`}
          />
        );
        break;
        
      default:
        content = (
          <div className={`
            ${getSizeClasses()} rounded-full border-2 transition-all duration-200
            ${isActive ? 'bg-primary border-primary' : 'border-gray-300'}
          `} />
        );
    }

    return (
      <button
        key={rating}
        type="button"
        onClick={() => handleRatingClick(rating)}
        onMouseEnter={() => handleMouseEnter(rating)}
        onMouseLeave={handleMouseLeave}
        disabled={disabled}
        className={`
          transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/20
          disabled:opacity-50 disabled:cursor-not-allowed
          ${hoverEffect ? 'hover:scale-110' : ''}
          ${isHalfValue ? 'relative' : ''}
        `}
        title={labels[rating] || `Rating: ${rating}`}
      >
        {content}
      </button>
    );
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Label and Description */}
      {(label || description) && (
        <div className="space-y-1">
          {label && (
            <label className="block text-sm font-medium text-text-primary">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          {description && (
            <p className="text-sm text-text-secondary">{description}</p>
          )}
        </div>
      )}

      {/* Rating Scale */}
      <div className={`
        flex items-center
        ${orientation === 'vertical' ? 'flex-col' : 'flex-row'}
        ${getSpacingClasses()}
      `}>
        {scaleValues.map((rating, index) => renderRatingItem(rating, index))}
      </div>

      {/* Labels */}
      {showLabels && Object.keys(labels).length > 0 && (
        <div className={`
          flex justify-between text-xs text-text-secondary
          ${orientation === 'vertical' ? 'flex-col space-y-1' : 'flex-row'}
        `}>
          {scaleValues.filter(v => labels[v]).map(rating => (
            <span key={rating} className="text-center">
              {labels[rating]}
            </span>
          ))}
        </div>
      )}

      {/* Current Value Display */}
      {showValue && value !== null && (
        <div className="text-sm text-text-secondary">
          Current rating: <span className="font-medium text-text-primary">{value}</span>
          {labels[value] && <span className="ml-1">({labels[value]})</span>}
        </div>
      )}

      {/* Help Text */}
      {helpText && (
        <p className="text-xs text-text-secondary">{helpText}</p>
      )}

      {/* Clear Button */}
      {allowClear && value !== null && !disabled && (
        <button
          type="button"
          onClick={() => onChange(null)}
          className="text-xs text-gray-500 hover:text-gray-700 flex items-center space-x-1"
        >
          <Icon name="X" size={12} />
          <span>Clear rating</span>
        </button>
      )}
    </div>
  );
};

// Predefined label sets for common use cases
export const ratingLabels = {
  satisfaction: {
    1: 'Very Dissatisfied',
    2: 'Dissatisfied', 
    3: 'Neutral',
    4: 'Satisfied',
    5: 'Very Satisfied'
  },
  agreement: {
    1: 'Strongly Disagree',
    2: 'Disagree',
    3: 'Neutral', 
    4: 'Agree',
    5: 'Strongly Agree'
  },
  frequency: {
    1: 'Never',
    2: 'Rarely',
    3: 'Sometimes',
    4: 'Often', 
    5: 'Always'
  },
  importance: {
    1: 'Not Important',
    2: 'Slightly Important',
    3: 'Moderately Important',
    4: 'Very Important',
    5: 'Extremely Important'
  },
  difficulty: {
    1: 'Very Easy',
    2: 'Easy',
    3: 'Moderate',
    4: 'Difficult',
    5: 'Very Difficult'
  },
  quality: {
    1: 'Poor',
    2: 'Fair',
    3: 'Good',
    4: 'Very Good',
    5: 'Excellent'
  }
};

export default RatingScale;
