/**
 * Unit tests for DOCX Generation Service - Image Embedding Functionality
 * Tests the image embedding, sizing, positioning, and alt text preservation features
 */

import {
    calculateImageDimensions,
    createImageParagraph,
    createImagePlaceholderParagraph,
    convertToDocxParagraphsWithImages,
    generateDocxWithImages,
    IMAGE_SIZE_CONFIG
} from '../docxGenerationService.js';

import { CONTENT_TYPES } from '../contentProcessingService.js';

// Mock the docx library
jest.mock('docx', () => ({
    Document: jest.fn(),
    Packer: {
        toBlob: jest.fn()
    },
    Paragraph: jest.fn(),
    TextRun: jest.fn(),
    ImageRun: jest.fn(),
    HeadingLevel: {
        HEADING_1: 'HEADING_1',
        HEADING_2: 'HEADING_2',
        HEADING_3: 'HEADING_3'
    },
    AlignmentType: {
        CENTER: 'CENTER'
    }
}));

// Mock the content processing service
jest.mock('../contentProcessingService.js', () => ({
    detectContentType: jest.fn(),
    extractAndProcessImages: jest.fn(),
    processContent: jest.fn(),
    createHeadingParagraph: jest.fn(),
    createTextParagraph: jest.fn(),
    createListParagraphs: jest.fn(),
    CONTENT_TYPES: {
        PARAGRAPH: 'paragraph',
        HEADING: 'heading',
        IMAGE: 'image',
        LIST: 'list'
    }
}));

describe('DOCX Generation Service - Image Embedding', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('calculateImageDimensions', () => {
        test('should return default dimensions when no size information provided', () => {
            const image = { src: 'test.jpg', alt: 'Test image' };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions).toEqual({
                width: IMAGE_SIZE_CONFIG.DEFAULT_WIDTH,
                height: IMAGE_SIZE_CONFIG.DEFAULT_HEIGHT
            });
        });

        test('should use small dimensions for small size class', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                class: 'image-small'
            };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions).toEqual({
                width: IMAGE_SIZE_CONFIG.SMALL_WIDTH,
                height: IMAGE_SIZE_CONFIG.SMALL_HEIGHT
            });
        });

        test('should use medium dimensions for medium size class', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                class: 'image-medium'
            };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions).toEqual({
                width: IMAGE_SIZE_CONFIG.MEDIUM_WIDTH,
                height: IMAGE_SIZE_CONFIG.MEDIUM_HEIGHT
            });
        });

        test('should use large dimensions for large size class', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                class: 'image-large'
            };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions).toEqual({
                width: IMAGE_SIZE_CONFIG.LARGE_WIDTH,
                height: IMAGE_SIZE_CONFIG.LARGE_HEIGHT
            });
        });

        test('should use explicit width and height when provided', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                width: '400',
                height: '300'
            };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions).toEqual({
                width: 400,
                height: 300
            });
        });

        test('should limit dimensions to maximum values', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                width: '1000',
                height: '800'
            };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions).toEqual({
                width: IMAGE_SIZE_CONFIG.MAX_WIDTH,
                height: IMAGE_SIZE_CONFIG.MAX_HEIGHT
            });
        });

        test('should calculate height from width maintaining aspect ratio', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                width: '400'
            };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions.width).toBe(400);
            expect(dimensions.height).toBe(300); // 400 * 0.75
        });

        test('should calculate width from height maintaining aspect ratio', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                height: '300'
            };
            const dimensions = calculateImageDimensions(image);

            expect(dimensions.height).toBe(300);
            expect(dimensions.width).toBe(399); // Math.round(300 * 1.33)
        });
    });

    describe('createImageParagraph', () => {
        const { ImageRun, Paragraph, AlignmentType } = require('docx');

        test('should create image paragraph with embedded data', () => {
            const mockImageData = new ArrayBuffer(1024);
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                data: mockImageData,
                downloadSuccess: true,
                width: '400',
                height: '300'
            };

            const mockImageRun = { type: 'ImageRun' };
            const mockParagraph = { type: 'Paragraph' };

            ImageRun.mockReturnValue(mockImageRun);
            Paragraph.mockReturnValue(mockParagraph);

            const result = createImageParagraph(image);

            expect(ImageRun).toHaveBeenCalledWith({
                data: mockImageData,
                transformation: {
                    width: 400,
                    height: 300
                },
                altText: {
                    title: 'Test image',
                    description: 'Test image'
                }
            });

            expect(Paragraph).toHaveBeenCalledWith({
                children: [mockImageRun],
                alignment: AlignmentType.CENTER,
                spacing: {
                    before: 200,
                    after: 200
                }
            });

            expect(result).toBe(mockParagraph);
        });

        test('should fallback to placeholder when image data is missing', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                downloadSuccess: false
            };

            const result = createImageParagraph(image);

            // Should call createImagePlaceholderParagraph instead
            expect(ImageRun).not.toHaveBeenCalled();
        });

        test('should use default alt text when not provided', () => {
            const mockImageData = new ArrayBuffer(1024);
            const image = {
                src: 'test.jpg',
                data: mockImageData,
                downloadSuccess: true
            };

            const mockImageRun = { type: 'ImageRun' };
            const mockParagraph = { type: 'Paragraph' };

            ImageRun.mockReturnValue(mockImageRun);
            Paragraph.mockReturnValue(mockParagraph);

            createImageParagraph(image);

            expect(ImageRun).toHaveBeenCalledWith(
                expect.objectContaining({
                    altText: {
                        title: 'Image',
                        description: 'Embedded image from document'
                    }
                })
            );
        });
    });

    describe('createImagePlaceholderParagraph', () => {
        const { TextRun, Paragraph, AlignmentType } = require('docx');

        test('should create placeholder paragraph for successful image', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image'
            };

            const mockTextRun = { type: 'TextRun' };
            const mockParagraph = { type: 'Paragraph' };

            TextRun.mockReturnValue(mockTextRun);
            Paragraph.mockReturnValue(mockParagraph);

            const result = createImagePlaceholderParagraph(image);

            expect(TextRun).toHaveBeenCalledWith({
                text: '[Image: Test image]',
                italics: true,
                color: '666666'
            });

            expect(Paragraph).toHaveBeenCalledWith({
                children: [mockTextRun],
                alignment: AlignmentType.CENTER,
                spacing: { after: 200 }
            });

            expect(result).toBe(mockParagraph);
        });

        test('should create error placeholder for failed image', () => {
            const image = {
                src: 'test.jpg',
                alt: 'Test image',
                error: 'Failed to download'
            };

            const mockTextRun = { type: 'TextRun' };
            TextRun.mockReturnValue(mockTextRun);

            createImagePlaceholderParagraph(image);

            expect(TextRun).toHaveBeenCalledWith({
                text: '[Image unavailable: Test image]',
                italics: true,
                color: '666666'
            });
        });

        test('should use default text when alt is missing', () => {
            const image = {
                src: 'test.jpg'
            };

            const mockTextRun = { type: 'TextRun' };
            TextRun.mockReturnValue(mockTextRun);

            createImagePlaceholderParagraph(image);

            expect(TextRun).toHaveBeenCalledWith({
                text: '[Image: Image]',
                italics: true,
                color: '666666'
            });
        });
    });

    describe('convertToDocxParagraphsWithImages', () => {
        const mockContentProcessing = require('../contentProcessingService.js');

        beforeEach(() => {
            mockContentProcessing.createHeadingParagraph.mockReturnValue({ type: 'heading' });
            mockContentProcessing.createTextParagraph.mockReturnValue({ type: 'paragraph' });
            mockContentProcessing.createListParagraphs.mockReturnValue([{ type: 'list' }]);
        });

        test('should convert content with embedded images', async () => {
            const processedContent = [
                {
                    type: CONTENT_TYPES.PARAGRAPH,
                    text: 'Some text'
                },
                {
                    type: CONTENT_TYPES.IMAGE,
                    src: 'test.jpg',
                    alt: 'Test image'
                }
            ];

            const processedImages = [
                {
                    src: 'test.jpg',
                    alt: 'Test image',
                    data: new ArrayBuffer(1024),
                    downloadSuccess: true
                }
            ];

            const result = await convertToDocxParagraphsWithImages(processedContent, processedImages);

            expect(result).toHaveLength(2);
            expect(mockContentProcessing.createTextParagraph).toHaveBeenCalledWith(processedContent[0]);
        });

        test('should handle missing processed images with placeholder', async () => {
            const processedContent = [
                {
                    type: CONTENT_TYPES.IMAGE,
                    src: 'missing.jpg',
                    alt: 'Missing image'
                }
            ];

            const processedImages = []; // No processed images

            const result = await convertToDocxParagraphsWithImages(processedContent, processedImages);

            expect(result).toHaveLength(1);
            // Should create placeholder for missing image
        });

        test('should handle different content types', async () => {
            const processedContent = [
                { type: CONTENT_TYPES.HEADING, text: 'Heading' },
                { type: CONTENT_TYPES.PARAGRAPH, text: 'Paragraph' },
                { type: CONTENT_TYPES.LIST, items: ['Item 1'] }
            ];

            const result = await convertToDocxParagraphsWithImages(processedContent, []);

            expect(result).toHaveLength(3);
            expect(mockContentProcessing.createHeadingParagraph).toHaveBeenCalled();
            expect(mockContentProcessing.createTextParagraph).toHaveBeenCalled();
            expect(mockContentProcessing.createListParagraphs).toHaveBeenCalled();
        });

        test('should return empty array for invalid input', async () => {
            const result = await convertToDocxParagraphsWithImages(null, []);
            expect(result).toEqual([]);
        });
    });

    describe('generateDocxWithImages', () => {
        const mockContentProcessing = require('../contentProcessingService.js');
        const { Document, Packer } = require('docx');

        beforeEach(() => {
            mockContentProcessing.detectContentType.mockReturnValue('html');
            mockContentProcessing.extractAndProcessImages.mockResolvedValue({
                totalImages: 2,
                successCount: 1,
                failureCount: 1,
                processedImages: [
                    {
                        src: 'test.jpg',
                        alt: 'Test image',
                        data: new ArrayBuffer(1024),
                        downloadSuccess: true
                    }
                ],
                failedImages: [
                    {
                        src: 'failed.jpg',
                        alt: 'Failed image',
                        error: 'Download failed'
                    }
                ]
            });
            mockContentProcessing.processContent.mockResolvedValue({
                processedContent: [
                    { type: CONTENT_TYPES.PARAGRAPH, text: 'Test content' }
                ]
            });

            Document.mockImplementation(() => ({ type: 'Document' }));
            Packer.toBlob.mockResolvedValue(new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));
        });

        test('should generate DOCX with embedded images successfully', async () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author',
                description: 'Test Description'
            };

            const content = '<p>Test content</p><img src="test.jpg" alt="Test image" />';

            const result = await generateDocxWithImages(documentData, content, 'html');

            expect(result.success).toBe(true);
            expect(result.blob).toBeInstanceOf(Blob);
            expect(result.imageStats).toEqual({
                totalImages: 2,
                successfulImages: 1,
                failedImages: 1,
                processedImages: expect.any(Array),
                failedImagesList: expect.any(Array)
            });

            expect(mockContentProcessing.extractAndProcessImages).toHaveBeenCalledWith(content, 'html');
            expect(mockContentProcessing.processContent).toHaveBeenCalledWith(content, 'html');
        });

        test('should handle content processing errors', async () => {
            const documentData = { title: 'Test Document' };
            const content = '<p>Test content</p>';

            mockContentProcessing.processContent.mockResolvedValue({
                error: 'Processing failed'
            });

            const result = await generateDocxWithImages(documentData, content);

            expect(result.success).toBe(false);
            expect(result.error).toBe('Content processing failed: Processing failed');
        });

        test('should auto-detect content type when not provided', async () => {
            const documentData = { title: 'Test Document' };
            const content = '# Markdown content';

            mockContentProcessing.detectContentType.mockReturnValue('markdown');

            await generateDocxWithImages(documentData, content);

            expect(mockContentProcessing.detectContentType).toHaveBeenCalledWith(content);
            expect(mockContentProcessing.extractAndProcessImages).toHaveBeenCalledWith(content, 'markdown');
        });

        test('should handle document generation errors', async () => {
            const documentData = { title: 'Test Document' };
            const content = '<p>Test content</p>';

            Packer.toBlob.mockRejectedValue(new Error('DOCX generation failed'));

            const result = await generateDocxWithImages(documentData, content);

            expect(result.success).toBe(false);
            expect(result.error).toBe('DOCX generation failed');
        });
    });
});