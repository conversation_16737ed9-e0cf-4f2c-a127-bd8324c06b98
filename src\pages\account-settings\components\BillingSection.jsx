import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const BillingSection = () => {
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  const [paymentMethods] = useState([
    {
      id: 1,
      type: 'card',
      brand: 'visa',
      last4: '4242',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true
    },
    {
      id: 2,
      type: 'bank',
      bankName: 'First Bank of Nigeria',
      accountNumber: '****1234',
      isDefault: false
    }
  ]);

  const [billingHistory] = useState([
    {
      id: 'INV-2024-001',
      date: '2024-01-15',
      description: 'Professional Plan - Monthly',
      amount: '₦15,000',
      status: 'paid',
      downloadUrl: '#'
    },
    {
      id: 'INV-2023-012',
      date: '2023-12-15',
      description: 'Professional Plan - Monthly',
      amount: '₦15,000',
      status: 'paid',
      downloadUrl: '#'
    },
    {
      id: 'INV-2023-011',
      date: '2023-11-15',
      description: 'Credit Purchase - 500 Credits',
      amount: '₦8,000',
      status: 'paid',
      downloadUrl: '#'
    },
    {
      id: 'INV-2023-010',
      date: '2023-11-15',
      description: 'Professional Plan - Monthly',
      amount: '₦15,000',
      status: 'failed',
      downloadUrl: null
    }
  ]);

  const [currentBilling] = useState({
    plan: 'Professional',
    amount: '₦15,000',
    nextBilling: '2024-02-15',
    paymentMethod: 'Visa ending in 4242',
    billingAddress: {
      name: 'John Doe',
      address: '123 Victoria Island',
      city: 'Lagos',
      state: 'Lagos State',
      country: 'Nigeria',
      postalCode: '101241'
    }
  });

  const handleAddPaymentMethod = (method) => {
    console.log('Adding payment method:', method);
    setShowAddPaymentMethod(false);
  };

  const handleSetDefaultPayment = (methodId) => {
    console.log('Setting default payment method:', methodId);
  };

  const handleRemovePaymentMethod = (methodId) => {
    console.log('Removing payment method:', methodId);
  };

  const handleDownloadInvoice = (invoice) => {
    console.log('Downloading invoice:', invoice.id);
  };

  const handleViewInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  const getPaymentMethodIcon = (type, brand) => {
    if (type === 'card') {
      switch (brand) {
        case 'visa': return 'CreditCard';
        case 'mastercard': return 'CreditCard';
        default: return 'CreditCard';
      }
    }
    return 'Building2';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'text-success bg-success/10';
      case 'failed': return 'text-error bg-error/10';
      case 'pending': return 'text-warning bg-warning/10';
      default: return 'text-text-secondary bg-background';
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Billing */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Current Billing</h3>
          <p className="text-sm text-text-secondary">Your current subscription and billing information</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary">Current Plan</span>
                <span className="text-lg font-semibold text-primary">{currentBilling.plan}</span>
              </div>
              <div className="flex items-center justify-between text-sm text-text-secondary">
                <span>Next billing date</span>
                <span>{currentBilling.nextBilling}</span>
              </div>
              <div className="flex items-center justify-between text-sm text-text-secondary">
                <span>Amount</span>
                <span className="font-medium">{currentBilling.amount}</span>
              </div>
            </div>

            <div className="p-4 border border-border rounded-lg">
              <h4 className="text-sm font-medium text-text-primary mb-2">Payment Method</h4>
              <p className="text-sm text-text-secondary">{currentBilling.paymentMethod}</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 border border-border rounded-lg">
              <h4 className="text-sm font-medium text-text-primary mb-3">Billing Address</h4>
              <div className="space-y-1 text-sm text-text-secondary">
                <p>{currentBilling.billingAddress.name}</p>
                <p>{currentBilling.billingAddress.address}</p>
                <p>{currentBilling.billingAddress.city}, {currentBilling.billingAddress.state}</p>
                <p>{currentBilling.billingAddress.country} {currentBilling.billingAddress.postalCode}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">Payment Methods</h3>
            <p className="text-sm text-text-secondary">Manage your saved payment methods</p>
          </div>
          <Button variant="primary" onClick={() => setShowAddPaymentMethod(true)} iconName="Plus" iconPosition="left">
            Add Payment Method
          </Button>
        </div>

        <div className="space-y-3">
          {paymentMethods.map((method) => (
            <div key={method.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                  <Icon name={getPaymentMethodIcon(method.type, method.brand)} size={16} color="var(--color-secondary)" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-text-primary">
                      {method.type === 'card' 
                        ? `${method.brand.toUpperCase()} ending in ${method.last4}`
                        : `${method.bankName} ${method.accountNumber}`
                      }
                    </span>
                    {method.isDefault && (
                      <span className="text-xs bg-primary text-primary-foreground px-2 py-0.5 rounded-full">
                        Default
                      </span>
                    )}
                  </div>
                  {method.type === 'card' && (
                    <p className="text-xs text-text-secondary">
                      Expires {method.expiryMonth}/{method.expiryYear}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {!method.isDefault && (
                  <Button variant="ghost" onClick={() => handleSetDefaultPayment(method.id)}>
                    Set Default
                  </Button>
                )}
                <Button variant="ghost" onClick={() => handleRemovePaymentMethod(method.id)}>
                  <Icon name="Trash2" size={16} />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Add Payment Method Modal */}
        {showAddPaymentMethod && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
            <div className="bg-surface rounded-lg border border-border max-w-md w-full">
              <div className="p-6 border-b border-border">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-text-primary">Add Payment Method</h4>
                  <Button variant="ghost" onClick={() => setShowAddPaymentMethod(false)}>
                    <Icon name="X" size={20} />
                  </Button>
                </div>
              </div>
              <div className="p-6 space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <button className="p-4 border border-border rounded-lg hover:border-primary transition-micro text-center">
                    <Icon name="CreditCard" size={24} className="mx-auto mb-2" />
                    <span className="text-sm text-text-primary">Card</span>
                  </button>
                  <button className="p-4 border border-border rounded-lg hover:border-primary transition-micro text-center">
                    <Icon name="Building2" size={24} className="mx-auto mb-2" />
                    <span className="text-sm text-text-primary">Bank Account</span>
                  </button>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">Card Number</label>
                    <Input type="text" placeholder="1234 5678 9012 3456" />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">Expiry Date</label>
                      <Input type="text" placeholder="MM/YY" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">CVV</label>
                      <Input type="text" placeholder="123" />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">Cardholder Name</label>
                    <Input type="text" placeholder="John Doe" />
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button variant="primary" onClick={() => handleAddPaymentMethod({})} className="flex-1">
                    Add Payment Method
                  </Button>
                  <Button variant="ghost" onClick={() => setShowAddPaymentMethod(false)} className="flex-1">
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Billing History */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Billing History</h3>
          <p className="text-sm text-text-secondary">Download your invoices and view payment history</p>
        </div>

        <div className="space-y-3">
          {billingHistory.map((invoice) => (
            <div key={invoice.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                  <Icon name="Receipt" size={16} color="var(--color-secondary)" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-text-primary">{invoice.id}</span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusColor(invoice.status)}`}>
                      {invoice.status}
                    </span>
                  </div>
                  <p className="text-xs text-text-secondary">
                    {invoice.date} • {invoice.description}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-text-primary">{invoice.amount}</span>
                <div className="flex items-center space-x-1">
                  <Button variant="ghost" onClick={() => handleViewInvoice(invoice)}>
                    <Icon name="Eye" size={16} />
                  </Button>
                  {invoice.downloadUrl && (
                    <Button variant="ghost" onClick={() => handleDownloadInvoice(invoice)}>
                      <Icon name="Download" size={16} />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Invoice Modal */}
      {showInvoiceModal && selectedInvoice && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
          <div className="bg-surface rounded-lg border border-border max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-text-primary">Invoice {selectedInvoice.id}</h4>
                <Button variant="ghost" onClick={() => setShowInvoiceModal(false)}>
                  <Icon name="X" size={20} />
                </Button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="flex justify-between">
                  <div>
                    <h5 className="font-semibold text-text-primary mb-2">DocForge AI</h5>
                    <p className="text-sm text-text-secondary">
                      123 Business District<br />
                      Victoria Island, Lagos<br />
                      Nigeria
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-text-secondary">Invoice Date: {selectedInvoice.date}</p>
                    <p className="text-sm text-text-secondary">Due Date: {selectedInvoice.date}</p>
                  </div>
                </div>

                <div>
                  <h6 className="font-medium text-text-primary mb-2">Bill To:</h6>
                  <p className="text-sm text-text-secondary">
                    {currentBilling.billingAddress.name}<br />
                    {currentBilling.billingAddress.address}<br />
                    {currentBilling.billingAddress.city}, {currentBilling.billingAddress.state}<br />
                    {currentBilling.billingAddress.country} {currentBilling.billingAddress.postalCode}
                  </p>
                </div>

                <div className="border border-border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-background">
                      <tr>
                        <th className="text-left p-3 text-sm font-medium text-text-primary">Description</th>
                        <th className="text-right p-3 text-sm font-medium text-text-primary">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="p-3 text-sm text-text-secondary">{selectedInvoice.description}</td>
                        <td className="p-3 text-sm text-text-primary text-right">{selectedInvoice.amount}</td>
                      </tr>
                    </tbody>
                    <tfoot className="bg-background">
                      <tr>
                        <td className="p-3 text-sm font-medium text-text-primary">Total</td>
                        <td className="p-3 text-sm font-medium text-text-primary text-right">{selectedInvoice.amount}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => handleDownloadInvoice(selectedInvoice)}>
                    Download PDF
                  </Button>
                  <Button variant="primary" onClick={() => setShowInvoiceModal(false)}>
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BillingSection;