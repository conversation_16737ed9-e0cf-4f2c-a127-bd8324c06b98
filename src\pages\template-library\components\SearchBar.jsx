import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';
import Button from '../../../components/ui/Button';

const SearchBar = ({ onSearch, searchQuery, setSearchQuery }) => {
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1);
  const searchRef = useRef(null);
  const suggestionsRef = useRef(null);

  const mockSuggestions = [
    "Business Plan Template",
    "Research Paper Format",
    "eBook Template",
    "Marketing Proposal",
    "Academic Thesis",
    "Financial Report",
    "Project Proposal",
    "Case Study Template",
    "White Paper Format",
    "Presentation Template",
    "Technical Documentation",
    "User Manual Template"
  ];

  useEffect(() => {
    if (searchQuery.length > 1) {
      const filtered = mockSuggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setSuggestions(filtered.slice(0, 6));
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
    setSelectedSuggestion(-1);
  }, [searchQuery]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (query = searchQuery) => {
    if (query.trim()) {
      onSearch(query.trim());
      setShowSuggestions(false);
    }
  };

  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Enter') {
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestion(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestion(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestion >= 0) {
          const selectedQuery = suggestions[selectedSuggestion];
          setSearchQuery(selectedQuery);
          handleSearch(selectedQuery);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestion(-1);
        break;
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setSearchQuery(suggestion);
    handleSearch(suggestion);
  };

  const clearSearch = () => {
    setSearchQuery('');
    onSearch('');
    setShowSuggestions(false);
  };

  return (
    <div ref={searchRef} className="relative w-full max-w-2xl">
      <div className="relative">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          <Icon name="Search" size={20} color="var(--color-text-secondary)" />
        </div>
        
        <Input
          type="search"
          placeholder="Search templates by name, category, or keyword..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          className="pl-12 pr-20 h-12 text-base"
        />

        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {searchQuery && (
            <Button
              variant="ghost"
              onClick={clearSearch}
              className="p-1.5"
            >
              <Icon name="X" size={16} />
            </Button>
          )}
          
          <Button
            variant="primary"
            onClick={() => handleSearch()}
            className="px-4 py-2"
          >
            Search
          </Button>
        </div>
      </div>

      {/* Search Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-surface border border-border rounded-lg shadow-elevation-3 z-50 max-h-64 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <button
              key={suggestion}
              onClick={() => handleSuggestionClick(suggestion)}
              className={`w-full text-left px-4 py-3 hover:bg-background transition-colors flex items-center space-x-3 ${
                index === selectedSuggestion ? 'bg-background' : ''
              }`}
            >
              <Icon name="Search" size={16} color="var(--color-text-secondary)" />
              <span className="text-sm text-text-primary">{suggestion}</span>
            </button>
          ))}
        </div>
      )}

      {/* Popular Searches */}
      <div className="mt-3 flex flex-wrap gap-2">
        <span className="text-xs text-text-secondary">Popular:</span>
        {['Business Plan', 'Research Paper', 'eBook', 'Marketing'].map((term) => (
          <button
            key={term}
            onClick={() => {
              setSearchQuery(term);
              handleSearch(term);
            }}
            className="text-xs text-secondary hover:text-primary transition-colors"
          >
            {term}
          </button>
        ))}
      </div>
    </div>
  );
};

export default SearchBar;