import React from 'react';
import Icon from '../../../components/AppIcon';

const StepProgressIndicator = ({ steps, currentStep, onStepClick, className = '' }) => {
  return (
    <div className={`w-full ${className}`}>
      {/* Progress Bar Background */}
      <div className="relative">
        <div className="flex items-center justify-between mb-8 px-4">
          {steps.map((step, index) => {
            const isActive = currentStep === step.id;
            const isCompleted = currentStep > step.id;
            const isClickable = currentStep >= step.id;

            return (
              <div key={step.id} className="flex flex-col items-center relative flex-1">
                {/* Step Circle */}
                <button
                  onClick={() => isClickable && onStepClick && onStepClick(step.id)}
                  disabled={!isClickable}
                  className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold
                    transition-all duration-300 relative z-10 mb-2
                    ${isActive
                      ? 'bg-primary text-white shadow-md'
                      : isCompleted
                        ? 'bg-primary text-white'
                        : 'bg-gray-200 text-gray-500'
                    }
                    ${isClickable ? 'cursor-pointer hover:shadow-lg' : 'cursor-not-allowed'}
                  `}
                >
                  {isCompleted ? (
                    <Icon name="Check" size={12} />
                  ) : (
                    <span>{step.id}</span>
                  )}
                </button>

                {/* Step Label */}
                <div className="text-center">
                  <div className={`text-xs font-medium ${
                    isActive ? 'text-primary' : isCompleted ? 'text-text-primary' : 'text-text-secondary'
                  }`}>
                    {step.title}
                  </div>
                </div>

                {/* Connection Line */}
                {index < steps.length - 1 && (
                  <div
                    className={`
                      absolute top-4 h-0.5 transition-all duration-500 hidden sm:block
                      ${isCompleted ? 'bg-primary' : 'bg-gray-200'}
                    `}
                    style={{
                      left: 'calc(50% + 1rem)',
                      right: 'calc(-100% + 1rem)',
                      width: 'calc(100% - 2rem)'
                    }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default StepProgressIndicator;
