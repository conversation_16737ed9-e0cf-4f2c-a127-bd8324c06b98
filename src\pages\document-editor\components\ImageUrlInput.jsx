import React, { useState } from 'react';

/**
 * ImageUrlInput - Compact URL input component for floating menu
 * 
 * Provides:
 * - URL input with validation
 * - Basic image preview
 * - Insert/Cancel actions
 * - Error handling for invalid URLs
 */
const ImageUrlInput = ({ 
  onImageInsert, 
  onCancel,
  isVisible = false 
}) => {
  const [imageUrl, setImageUrl] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [previewImage, setPreviewImage] = useState(null);

  // Basic URL validation
  const isValidImageUrl = (url) => {
    try {
      const urlObj = new URL(url);
      const validProtocols = ['http:', 'https:'];
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
      
      if (!validProtocols.includes(urlObj.protocol)) {
        return false;
      }
      
      // Check if URL ends with image extension or contains image service patterns
      const pathname = urlObj.pathname.toLowerCase();
      const hasImageExtension = imageExtensions.some(ext => pathname.includes(ext));
      const isImageService = urlObj.hostname.includes('unsplash.com') || 
                           urlObj.hostname.includes('images.') ||
                           urlObj.hostname.includes('img.') ||
                           urlObj.searchParams.has('w') || // Common image service param
                           urlObj.searchParams.has('width');
      
      return hasImageExtension || isImageService;
    } catch {
      return false;
    }
  };

  // Handle URL input change with validation
  const handleUrlChange = (e) => {
    const url = e.target.value;
    setImageUrl(url);
    setValidationError('');
    setPreviewImage(null);

    if (url.trim() && !isValidImageUrl(url)) {
      setValidationError('Please enter a valid image URL');
    }
  };

  // Test image loading for preview
  const handlePreview = () => {
    if (!imageUrl.trim() || !isValidImageUrl(imageUrl)) {
      setValidationError('Please enter a valid image URL');
      return;
    }

    setIsValidating(true);
    setValidationError('');

    const img = new Image();
    img.onload = () => {
      setPreviewImage(imageUrl);
      setIsValidating(false);
    };
    img.onerror = () => {
      setValidationError('Unable to load image from this URL');
      setIsValidating(false);
      setPreviewImage(null);
    };
    img.src = imageUrl;
  };

  // Handle image insertion
  const handleInsert = () => {
    if (!imageUrl.trim() || !isValidImageUrl(imageUrl)) {
      setValidationError('Please enter a valid image URL');
      return;
    }

    onImageInsert({
      src: imageUrl.trim(),
      alt: 'Image from URL'
    });

    // Reset state
    setImageUrl('');
    setPreviewImage(null);
    setValidationError('');
  };

  // Handle cancel
  const handleCancel = () => {
    setImageUrl('');
    setPreviewImage(null);
    setValidationError('');
    onCancel();
  };

  if (!isVisible) return null;

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-[280px] max-w-[320px]">
      {/* Header */}
      <div className="flex items-center mb-3 pb-2 border-b border-gray-100">
        <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2">
          <span className="text-white text-sm">🔗</span>
        </div>
        <span className="text-sm text-gray-600 font-medium">Insert image from URL</span>
      </div>

      {/* URL Input */}
      <div className="space-y-3">
        <div>
          <input
            type="url"
            value={imageUrl}
            onChange={handleUrlChange}
            placeholder="https://example.com/image.jpg"
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            autoFocus
          />
          {validationError && (
            <p className="text-red-500 text-xs mt-1">{validationError}</p>
          )}
        </div>

        {/* Preview Button */}
        {imageUrl.trim() && !previewImage && !validationError && (
          <button
            onClick={handlePreview}
            disabled={isValidating}
            className="w-full px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
          >
            {isValidating ? 'Loading preview...' : 'Preview image'}
          </button>
        )}

        {/* Image Preview */}
        {previewImage && (
          <div className="border border-gray-200 rounded-md overflow-hidden">
            <img
              src={previewImage}
              alt="Preview"
              className="w-full h-24 object-cover"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          <button
            onClick={handleCancel}
            className="flex-1 px-3 py-2 text-sm text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleInsert}
            disabled={!imageUrl.trim() || !!validationError}
            className="flex-1 px-3 py-2 text-sm text-white bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-md transition-colors"
          >
            Insert
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageUrlInput;
