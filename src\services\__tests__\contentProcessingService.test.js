/**
 * Unit Tests for Content Processing Service
 * 
 * Tests HTML and markdown content processing utilities for DOCX export
 */

import {
    parseHTMLContent,
    parseMarkdownContent,
    convertMarkdownToHTML,
    processContent,
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    convertToDocxParagraphs,
    detectContentType,
    processHTMLElement,
    processHeading,
    processParagraph,
    processImage,
    processList,
    processTextFormatting,
    createHeadingParagraph,
    createTextParagraph,
    createListParagraphs,
    CONTENT_TYPES,
    HEADING_LEVELS
} from '../contentProcessingService';

// Mock DOMPurify
jest.mock('dompurify', () => ({
    sanitize: jest.fn((html) => html)
}));

// Mock unified and related packages - these will be dynamically imported
// so we don't need to mock them at the module level

describe('Content Processing Service', () => {

    describe('convertMarkdownToHTML', () => {
        test('should convert basic markdown to HTML', () => {
            const markdown = '# Title\n\n**Bold text** and *italic text*\n\nRegular paragraph.';
            const html = convertMarkdownToHTML(markdown);

            expect(html).toContain('<h1>Title</h1>');
            expect(html).toContain('<strong>Bold text</strong>');
            expect(html).toContain('<em>italic text</em>');
            expect(html).toContain('<p>Regular paragraph.</p>');
        });

        test('should convert markdown images', () => {
            const markdown = 'Text with ![alt text](image.jpg) image.';
            const html = convertMarkdownToHTML(markdown);

            expect(html).toContain('<img src="image.jpg" alt="alt text" />');
        });

        test('should convert markdown links', () => {
            const markdown = 'Text with [link text](http://example.com) link.';
            const html = convertMarkdownToHTML(markdown);

            expect(html).toContain('<a href="http://example.com">link text</a>');
        });

        test('should convert markdown lists', () => {
            const markdown = '- Item 1\n- Item 2\n- Item 3';
            const html = convertMarkdownToHTML(markdown);

            expect(html).toContain('<ul>');
            expect(html).toContain('<li>Item 1</li>');
            expect(html).toContain('<li>Item 2</li>');
            expect(html).toContain('<li>Item 3</li>');
        });

        test('should handle empty markdown', () => {
            expect(convertMarkdownToHTML('')).toBe('<p></p>');
            expect(convertMarkdownToHTML(null)).toBe('<p></p>');
        });

        test('should handle multiple heading levels', () => {
            const markdown = '# H1\n## H2\n### H3';
            const html = convertMarkdownToHTML(markdown);

            expect(html).toContain('<h1>H1</h1>');
            expect(html).toContain('<h2>H2</h2>');
            expect(html).toContain('<h3>H3</h3>');
        });
    });

    describe('detectContentType', () => {
        test('should detect HTML content', () => {
            const htmlContent = '<p>This is <strong>HTML</strong> content</p>';
            expect(detectContentType(htmlContent)).toBe('html');
        });

        test('should detect markdown content', () => {
            const markdownContent = '# This is markdown\n\n**Bold text** and *italic text*';
            expect(detectContentType(markdownContent)).toBe('markdown');
        });

        test('should default to html for plain text', () => {
            const plainText = 'This is just plain text';
            expect(detectContentType(plainText)).toBe('html');
        });

        test('should handle empty content', () => {
            expect(detectContentType('')).toBe('html');
            expect(detectContentType(null)).toBe('html');
            expect(detectContentType(undefined)).toBe('html');
        });
    });

    describe('extractImagesFromHTML', () => {
        test('should extract images from HTML content', () => {
            const htmlContent = `
        <p>Some text</p>
        <img src="image1.jpg" alt="First image" width="300" height="200">
        <p>More text</p>
        <img src="image2.png" alt="Second image">
      `;

            const images = extractImagesFromHTML(htmlContent);

            expect(images).toHaveLength(2);
            expect(images[0]).toEqual({
                src: 'image1.jpg',
                alt: 'First image',
                width: '300',
                height: '200',
                class: null,
                index: 0,
                type: 'html'
            });
            expect(images[1]).toEqual({
                src: 'image2.png',
                alt: 'Second image',
                width: null,
                height: null,
                class: null,
                index: 1,
                type: 'html'
            });
        });

        test('should handle images without alt text', () => {
            const htmlContent = '<img src="test.jpg">';
            const images = extractImagesFromHTML(htmlContent);

            expect(images).toHaveLength(1);
            expect(images[0].alt).toBe('Image 1');
        });

        test('should handle empty HTML', () => {
            expect(extractImagesFromHTML('')).toEqual([]);
            expect(extractImagesFromHTML(null)).toEqual([]);
        });
    });

    describe('extractImagesFromMarkdown', () => {
        test('should extract images from markdown content', () => {
            const markdownContent = `
        # Title
        
        Some text with ![First image](image1.jpg) in the middle.
        
        And another ![Second image](image2.png) here.
        
        ![](image3.gif)
      `;

            const images = extractImagesFromMarkdown(markdownContent);

            expect(images).toHaveLength(3);
            expect(images[0]).toEqual({
                src: 'image1.jpg',
                alt: 'First image',
                markdownSyntax: '![First image](image1.jpg)',
                index: 0,
                type: 'markdown'
            });
            expect(images[1]).toEqual({
                src: 'image2.png',
                alt: 'Second image',
                markdownSyntax: '![Second image](image2.png)',
                index: 1,
                type: 'markdown'
            });
            expect(images[2]).toEqual({
                src: 'image3.gif',
                alt: 'Image 3',
                markdownSyntax: '![](image3.gif)',
                index: 2,
                type: 'markdown'
            });
        });

        test('should handle markdown without images', () => {
            const markdownContent = '# Title\n\nJust text content';
            expect(extractImagesFromMarkdown(markdownContent)).toEqual([]);
        });

        test('should handle empty markdown', () => {
            expect(extractImagesFromMarkdown('')).toEqual([]);
            expect(extractImagesFromMarkdown(null)).toEqual([]);
        });
    });

    describe('processTextFormatting', () => {
        test('should process plain text', () => {
            // Create a mock element
            const mockElement = {
                children: { length: 0 },
                textContent: 'Plain text content',
                childNodes: []
            };

            const textRuns = processTextFormatting(mockElement);

            expect(textRuns).toHaveLength(1);
            expect(textRuns[0]).toEqual({
                text: 'Plain text content',
                bold: false,
                italic: false,
                underline: false
            });
        });

        test('should handle empty text', () => {
            const mockElement = {
                children: { length: 0 },
                textContent: '',
                childNodes: []
            };

            const textRuns = processTextFormatting(mockElement);
            expect(textRuns).toEqual([]);
        });
    });

    describe('processHeading', () => {
        test('should process heading element', () => {
            const mockElement = {
                tagName: 'H2',
                textContent: 'Test Heading',
                children: { length: 0 },
                childNodes: []
            };

            const result = processHeading(mockElement);

            expect(result.type).toBe(CONTENT_TYPES.HEADING);
            expect(result.level).toBe(2);
            expect(result.text).toBe('Test Heading');
            expect(result.textRuns).toHaveLength(1);
        });
    });

    describe('processParagraph', () => {
        test('should process paragraph element', () => {
            const mockElement = {
                textContent: 'Test paragraph content',
                children: { length: 0 },
                childNodes: []
            };

            const result = processParagraph(mockElement);

            expect(result.type).toBe(CONTENT_TYPES.PARAGRAPH);
            expect(result.text).toBe('Test paragraph content');
            expect(result.textRuns).toHaveLength(1);
        });
    });

    describe('processImage', () => {
        test('should process image element', () => {
            const mockElement = {
                getAttribute: jest.fn((attr) => {
                    const attrs = {
                        'src': 'test.jpg',
                        'alt': 'Test image',
                        'width': '300',
                        'height': '200'
                    };
                    return attrs[attr];
                }),
                outerHTML: '<img src="test.jpg" alt="Test image" width="300" height="200">'
            };

            const result = processImage(mockElement);

            expect(result.type).toBe(CONTENT_TYPES.IMAGE);
            expect(result.src).toBe('test.jpg');
            expect(result.alt).toBe('Test image');
            expect(result.width).toBe(300);
            expect(result.height).toBe(200);
        });

        test('should handle image without dimensions', () => {
            const mockElement = {
                getAttribute: jest.fn((attr) => {
                    return attr === 'src' ? 'test.jpg' : (attr === 'alt' ? 'Test' : null);
                }),
                outerHTML: '<img src="test.jpg" alt="Test">'
            };

            const result = processImage(mockElement);

            expect(result.width).toBeNull();
            expect(result.height).toBeNull();
        });
    });

    describe('parseHTMLContent', () => {
        // Mock DOM parser for testing
        const mockDOMParser = () => {
            global.DOMParser = jest.fn(() => ({
                parseFromString: jest.fn(() => ({
                    body: {
                        children: [
                            {
                                tagName: 'H1',
                                textContent: 'Test Title',
                                children: { length: 0 },
                                childNodes: []
                            },
                            {
                                tagName: 'P',
                                textContent: 'Test paragraph',
                                children: { length: 0 },
                                childNodes: []
                            }
                        ]
                    }
                }))
            }));
        };

        beforeEach(() => {
            mockDOMParser();
        });

        test('should parse HTML content', () => {
            const htmlContent = '<h1>Test Title</h1><p>Test paragraph</p>';
            const result = parseHTMLContent(htmlContent);

            expect(result).toHaveLength(2);
            expect(result[0].type).toBe(CONTENT_TYPES.HEADING);
            expect(result[1].type).toBe(CONTENT_TYPES.PARAGRAPH);
        });

        test('should handle empty HTML', () => {
            expect(parseHTMLContent('')).toEqual([]);
            expect(parseHTMLContent(null)).toEqual([]);
        });

        test('should handle invalid HTML gracefully', () => {
            // Mock DOMParser to throw error
            global.DOMParser = jest.fn(() => ({
                parseFromString: jest.fn(() => {
                    throw new Error('Parse error');
                })
            }));

            const result = parseHTMLContent('<invalid>html');
            expect(result).toHaveLength(1);
            expect(result[0].text).toBe('Error processing HTML content');
        });
    });

    describe('parseMarkdownContent', () => {
        beforeEach(() => {
            // Mock DOMParser for HTML parsing
            global.DOMParser = jest.fn(() => ({
                parseFromString: jest.fn(() => ({
                    body: {
                        children: [
                            {
                                tagName: 'H1',
                                textContent: 'Title',
                                children: { length: 0 },
                                childNodes: []
                            },
                            {
                                tagName: 'P',
                                textContent: 'Paragraph content',
                                children: { length: 0 },
                                childNodes: []
                            }
                        ]
                    }
                }))
            }));
        });

        test('should parse markdown content using fallback converter', async () => {
            const markdownContent = '# Title\n\nParagraph content';
            const result = await parseMarkdownContent(markdownContent);

            expect(result).toBeDefined();
            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBeGreaterThan(0);
        });

        test('should handle empty markdown', async () => {
            const result = await parseMarkdownContent('');
            expect(result).toEqual([]);
        });

        test('should handle null markdown', async () => {
            const result = await parseMarkdownContent(null);
            expect(result).toEqual([]);
        });

        test('should fallback gracefully when processing fails', async () => {
            // Mock DOMParser to throw error
            global.DOMParser = jest.fn(() => ({
                parseFromString: jest.fn(() => {
                    throw new Error('Parse error');
                })
            }));

            const result = await parseMarkdownContent('# Test');
            expect(result).toHaveLength(1);
            expect(result[0].text).toBe('Error processing HTML content');
        });
    });

    describe('processContent', () => {
        beforeEach(() => {
            // Reset mocks
            jest.clearAllMocks();

            // Mock DOMParser
            global.DOMParser = jest.fn(() => ({
                parseFromString: jest.fn(() => ({
                    body: { children: [] },
                    querySelectorAll: jest.fn(() => [])
                }))
            }));
        });

        test('should process HTML content', async () => {
            const htmlContent = '<p>Test content</p>';
            const result = await processContent(htmlContent, 'html');

            expect(result.contentType).toBe('html');
            expect(result.originalContent).toBe(htmlContent);
            expect(Array.isArray(result.processedContent)).toBe(true);
            expect(Array.isArray(result.images)).toBe(true);
        });

        test('should process markdown content', async () => {
            const markdownContent = '# Test\n\nContent';
            const result = await processContent(markdownContent, 'markdown');

            expect(result.contentType).toBe('markdown');
            expect(result.originalContent).toBe(markdownContent);
            expect(Array.isArray(result.processedContent)).toBe(true);
            expect(Array.isArray(result.images)).toBe(true);
        });

        test('should handle unsupported content type', async () => {
            const result = await processContent('content', 'unsupported');

            expect(result.error).toBeDefined();
            expect(result.processedContent).toHaveLength(1);
            expect(result.processedContent[0].text).toBe('Error processing content');
        });

        test('should handle empty content', async () => {
            const result = await processContent('');

            expect(result.processedContent).toEqual([]);
            expect(result.images).toEqual([]);
        });
    });

    describe('convertToDocxParagraphs', () => {
        test('should convert processed content to DOCX paragraphs', () => {
            const processedContent = [
                {
                    type: CONTENT_TYPES.HEADING,
                    level: 1,
                    text: 'Test Heading',
                    textRuns: [{ text: 'Test Heading', bold: false, italic: false, underline: false }]
                },
                {
                    type: CONTENT_TYPES.PARAGRAPH,
                    text: 'Test paragraph',
                    textRuns: [{ text: 'Test paragraph', bold: false, italic: false, underline: false }]
                }
            ];

            const paragraphs = convertToDocxParagraphs(processedContent);

            expect(paragraphs).toHaveLength(2);
            expect(paragraphs[0]).toBeDefined(); // Heading paragraph
            expect(paragraphs[1]).toBeDefined(); // Text paragraph
        });

        test('should handle empty processed content', () => {
            expect(convertToDocxParagraphs([])).toEqual([]);
            expect(convertToDocxParagraphs(null)).toEqual([]);
        });

        test('should handle image content with placeholder', () => {
            const processedContent = [
                {
                    type: CONTENT_TYPES.IMAGE,
                    src: 'test.jpg',
                    alt: 'Test image'
                }
            ];

            const paragraphs = convertToDocxParagraphs(processedContent);

            expect(paragraphs).toHaveLength(1);
            // Should create image placeholder paragraph
        });

        test('should handle list content', () => {
            const processedContent = [
                {
                    type: CONTENT_TYPES.LIST,
                    ordered: false,
                    items: [
                        { text: 'Item 1', textRuns: [{ text: 'Item 1', bold: false, italic: false, underline: false }] },
                        { text: 'Item 2', textRuns: [{ text: 'Item 2', bold: false, italic: false, underline: false }] }
                    ]
                }
            ];

            const paragraphs = convertToDocxParagraphs(processedContent);

            expect(paragraphs).toHaveLength(2); // Two list items
        });
    });

    describe('createHeadingParagraph', () => {
        test('should create heading paragraph with correct level', () => {
            const content = {
                level: 2,
                text: 'Test Heading',
                textRuns: [{ text: 'Test Heading', bold: true, italic: false, underline: false }]
            };

            const paragraph = createHeadingParagraph(content);

            expect(paragraph).toBeDefined();
            // Check that paragraph was created (exact structure depends on docx library implementation)
            expect(paragraph).toBeDefined();
            expect(paragraph.constructor.name).toBe('Paragraph');
        });

        test('should handle heading without textRuns', () => {
            const content = {
                level: 1,
                text: 'Simple Heading'
            };

            const paragraph = createHeadingParagraph(content);
            expect(paragraph).toBeDefined();
        });
    });

    describe('createTextParagraph', () => {
        test('should create text paragraph', () => {
            const content = {
                text: 'Test paragraph',
                textRuns: [{ text: 'Test paragraph', bold: false, italic: true, underline: false }]
            };

            const paragraph = createTextParagraph(content);
            expect(paragraph).toBeDefined();
        });

        test('should handle paragraph without textRuns', () => {
            const content = {
                text: 'Simple paragraph'
            };

            const paragraph = createTextParagraph(content);
            expect(paragraph).toBeDefined();
        });
    });

    describe('createListParagraphs', () => {
        test('should create unordered list paragraphs', () => {
            const content = {
                ordered: false,
                items: [
                    { text: 'Item 1', textRuns: [{ text: 'Item 1', bold: false, italic: false, underline: false }] },
                    { text: 'Item 2', textRuns: [{ text: 'Item 2', bold: false, italic: false, underline: false }] }
                ]
            };

            const paragraphs = createListParagraphs(content);

            expect(paragraphs).toHaveLength(2);
            expect(paragraphs[0]).toBeDefined();
            expect(paragraphs[1]).toBeDefined();
        });

        test('should create ordered list paragraphs', () => {
            const content = {
                ordered: true,
                items: [
                    { text: 'First item', textRuns: [{ text: 'First item', bold: false, italic: false, underline: false }] },
                    { text: 'Second item', textRuns: [{ text: 'Second item', bold: false, italic: false, underline: false }] }
                ]
            };

            const paragraphs = createListParagraphs(content);

            expect(paragraphs).toHaveLength(2);
        });

        test('should handle empty list', () => {
            const content = { ordered: false, items: [] };
            expect(createListParagraphs(content)).toEqual([]);
        });

        test('should handle list without items', () => {
            const content = { ordered: false };
            expect(createListParagraphs(content)).toEqual([]);
        });
    });
});