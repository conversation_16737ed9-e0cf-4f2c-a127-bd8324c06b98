import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./styles/tailwind.css";
import "./styles/index.css";

// Import debug utilities in development
if (import.meta.env.DEV) {
  // DISABLE_DEBUG_UTILS flag - set to true to disable debug utilities
  const DISABLE_DEBUG_UTILS = true;
  
  // Only load minimal environment checker
  window.checkEnv = () => {
    console.log('🔍 Environment:', import.meta.env.DEV ? 'Development' : 'Production');
    console.log('🔧 Debug Mode:', DISABLE_DEBUG_UTILS ? 'Disabled' : 'Enabled');
    console.log('🌐 Base URL:', import.meta.env.BASE_URL);
    console.log('🔗 Supabase URL:', import.meta.env.VITE_SUPABASE_URL?.substring(0, 20) + '...');
    return import.meta.env.DEV ? 'Development Mode' : 'Production Mode';
  };
  
  // Only load other debug utilities if explicitly enabled
  if (!DISABLE_DEBUG_UTILS) {
    // Check environment variables
    import('./utils/envChecker.js');
    
    // Load Supabase debug utilities
    import('./utils/supabaseDebug.js').then((module) => {
      window.supabaseDebug = module.supabaseDebug;
    });

    // Load auth test utilities
    import('./utils/authTest.js').then((module) => {
      window.authTests = module.authTests;
    });
  }
}

const container = document.getElementById("root");
const root = createRoot(container);

root.render(<App />);
