/**
 * Enhanced Questionnaire Data Structure for DocForge AI
 * Comprehensive data models for collecting document requirements
 * Based on Designrr's proven questionnaire approach
 */

// Default questionnaire state structure
export const defaultQuestionnaireData = {
  // Step 1: Document Purpose & Type (Designrr: Generate step)
  documentPurpose: {
    primaryType: 'ebook', // 'ebook', 'academic', 'business', 'guide'
    subType: '', // Specific sub-category based on primary type
    useCase: '', // Specific use case or purpose
    targetOutcome: '', // What the document should achieve
    urgency: 'normal', // 'urgent', 'normal', 'flexible'
    format: 'pdf', // 'pdf', 'epub', 'word', 'html'
    baseline: 'template', // 'template', 'existing-content', 'scratch', 'import-url'
    // URL Import specific data
    importedContent: {
      sourceUrl: '', // Original URL
      extractedContent: '', // Cleaned text content
      originalTitle: '', // Title from the webpage
      author: '', // Author if available
      publishDate: '', // Publication date if available
      wordCount: 0, // Word count of extracted content
      extractedAt: null, // Timestamp of extraction
      extractionStatus: 'pending', // 'pending', 'extracting', 'success', 'error'
      extractionError: '', // Error message if extraction fails
    }
  },

  // Step 2: Topic and Niche Selection (Designrr: Topic/Niche)
  topicAndNiche: {
    mainTopic: '', // Primary topic/niche (e.g., "Life coaching")
    language: 'english', // Document language
    subNiches: [], // Selected sub-niches (e.g., ["Wellness and Health Coaching", "Mindfulness and Stress Management Coaching"])
    availableSubNiches: [], // Dynamically populated based on main topic
    customSubNiche: '', // Custom sub-niche if not in predefined list
    topicDepth: 'comprehensive', // 'overview', 'detailed', 'comprehensive', 'exhaustive'
    includeCurrentTrends: false, // Include latest trends and developments
    focusAreas: [], // Specific areas to focus on within the topic
  },

  // Step 3: Audience Analysis (Designrr: Audience)
  audienceAnalysis: {
    primaryAudience: '', // e.g., "parents", "professionals", "students"
    audienceDescription: '', // Detailed description of target audience
    knowledgeLevel: 'intermediate', // 'beginner', 'intermediate', 'advanced', 'expert'
    ageRange: '', // 'children', 'teens', 'young-adults', 'adults', 'seniors', 'all-ages'
    professionalLevel: '', // 'entry-level', 'mid-level', 'senior-level', 'executive', 'mixed'
    geographicScope: 'global', // 'local', 'regional', 'national', 'global'
    painPoints: [], // Audience pain points to address
    goals: [], // What the audience wants to achieve
    priorKnowledge: [], // Assumed prior knowledge
    demographics: {
      income: '', // 'low', 'middle', 'high', 'mixed'
      education: '', // 'high-school', 'college', 'graduate', 'mixed'
      lifestyle: '', // 'busy', 'leisure', 'professional', 'family-focused'
    },
  },

  // Step 4: Title Selection (Designrr: Title selection)
  titleSelection: {
    selectedTitle: '', // Chosen title from generated options
    generatedTitles: [], // AI-generated title suggestions
    customTitle: '', // Custom title if user prefers
    titleStyle: 'descriptive', // 'descriptive', 'catchy', 'professional', 'academic'
    includeSubtitle: false, // Whether to include a subtitle
    subtitle: '', // Subtitle text if included
  },

  // Step 5: Tone and Voice (Designrr: Tone/Voice selection)
  toneAndVoice: {
    toneOfVoice: 'informative', // Primary tone selection
    writingStyle: 'professional', // Overall writing style
    formalityLevel: 'formal', // 'very-formal', 'formal', 'semi-formal', 'casual', 'very-casual'
    voicePersonality: [], // Multiple personality traits
    targetMood: 'neutral', // 'inspiring', 'serious', 'friendly', 'authoritative', 'neutral'
    perspectiveVoice: 'third-person', // 'first-person', 'second-person', 'third-person', 'mixed'
    industryLanguage: false, // Use industry-specific terminology
    readabilityLevel: 'college', // 'elementary', 'middle-school', 'high-school', 'college', 'graduate'
    brandVoice: '', // Specific brand voice guidelines
  },

  // Step 6: Document Outline (AI-generated structure)
  documentOutline: {
    generatedOutline: null, // AI-generated outline object
    approved: false, // Whether user has approved the outline
    customModifications: [], // Any user modifications to the outline
    estimatedLength: '', // Estimated document length based on outline
    estimatedReadingTime: '', // Estimated reading time
  },

  // Step 7: Content Details and Preferences
  contentDetails: {
    mainTheme: '', // Primary topic/theme (inherited from topicAndNiche)
    subTopics: [], // Array of subtopics to cover
    keyPoints: [], // Essential points to include
    contentDepth: 'comprehensive', // 'overview', 'detailed', 'comprehensive', 'exhaustive'
    researchRequired: false, // Whether research/citations are needed
    includeCurrentTrends: false, // Include current trends and developments
    practicalExamples: true, // Include practical examples
    caseStudies: false, // Include case studies
    exclusions: [], // Topics to explicitly exclude
    addImages: false, // Whether to include AI-generated images
    contentPreferences: {
      includeIntroduction: true,
      includeConclusion: true,
      includeActionItems: false,
      includeResources: false,
      includeGlossary: false,
      includeIndex: false,
    },
  },

  // Step 4: Document Structure
  documentStructure: {
    estimatedLength: '', // 'short', 'medium', 'long', 'custom'
    customLength: '', // Specific page/word count if custom
    format: 'pdf', // 'pdf', 'epub', 'word', 'html', 'presentation'
    organizationStyle: 'logical', // 'chronological', 'logical', 'problem-solution', 'compare-contrast'
    includeTableOfContents: true,
    includeExecutiveSummary: false,
    includeAppendices: false,
    chapterStructure: [], // Array of planned chapters/sections
    visualElements: {
      includeImages: false,
      includeCharts: false,
      includeDiagrams: false,
      includeInfographics: false,
    },
  },

  // Step 5: Style & Tone Requirements
  styleAndTone: {
    writingStyle: 'professional', // 'academic', 'professional', 'conversational', 'technical', 'creative'
    toneOfVoice: 'informative', // 'informative', 'persuasive', 'instructional', 'analytical', 'inspirational'
    formalityLevel: 'formal', // 'very-formal', 'formal', 'semi-formal', 'casual', 'very-casual'
    technicalLevel: 'moderate', // 'basic', 'moderate', 'advanced', 'expert'
    voicePreference: 'third-person', // 'first-person', 'second-person', 'third-person', 'mixed'
    industryLanguage: false, // Use industry-specific terminology
    language: 'english', // Primary language
    readabilityTarget: 'college', // 'elementary', 'middle-school', 'high-school', 'college', 'graduate'
  },

  // Step 6: Additional Requirements & Constraints
  additionalRequirements: {
    deadline: '', // Target completion date
    citationStyle: 'none', // 'apa', 'mla', 'chicago', 'harvard', 'ieee', 'none'
    minimumSources: 0, // Minimum number of sources to cite
    complianceRequirements: [], // Regulatory or compliance needs
    brandGuidelines: '', // Specific brand voice or guidelines
    templatePreference: 'modern', // 'academic', 'business', 'modern', 'creative', 'minimal'
    accessibilityRequirements: false, // ADA compliance needs
    multilingualSupport: false, // Need for multiple languages
    collaborationNeeds: false, // Multiple authors or reviewers
    revisionCycles: 1, // Expected number of revision rounds
    specialFormatting: [], // Special formatting requirements
    customInstructions: '', // Any additional custom instructions
  },

  // Step 7: Review & Summary (computed fields)
  reviewSummary: {
    completionPercentage: 0,
    estimatedGenerationTime: '',
    complexityScore: 'medium', // 'low', 'medium', 'high', 'very-high'
    recommendedApproach: '',
    potentialChallenges: [],
    suggestedImprovements: [],
  },

  // Metadata
  metadata: {
    createdAt: null,
    lastModified: null,
    version: '1.0',
    stepProgress: {
      step1: false,
      step2: false,
      step3: false,
      step4: false,
      step5: false,
      step6: false,
      step7: false,
    },
    validationErrors: {},
    autoSaveEnabled: true,
    sessionId: '',
  }
};

// Document type configurations with sub-types and use cases
export const documentTypeConfigs = {
  academic: {
    name: 'Academic Paper',
    icon: 'GraduationCap',
    description: 'Research papers, essays, theses, dissertations',
    subTypes: [
      { id: 'research-paper', name: 'Research Paper', description: 'Original research with methodology and findings' },
      { id: 'literature-review', name: 'Literature Review', description: 'Comprehensive review of existing research' },
      { id: 'thesis', name: 'Thesis/Dissertation', description: 'Extended academic work for degree completion' },
      { id: 'essay', name: 'Academic Essay', description: 'Analytical or argumentative essay' },
      { id: 'case-study', name: 'Case Study', description: 'In-depth analysis of specific cases' },
      { id: 'lab-report', name: 'Lab Report', description: 'Scientific experiment documentation' },
    ],
    defaultRequirements: {
      citationStyle: 'apa',
      formalityLevel: 'very-formal',
      writingStyle: 'academic',
      includeExecutiveSummary: false,
      researchRequired: true,
    }
  },
  business: {
    name: 'Business Document',
    icon: 'Briefcase',
    description: 'Business documents, proposals, presentations, plans',
    subTypes: [
      { id: 'business-plan', name: 'Business Plan', description: 'Comprehensive business strategy document' },
      { id: 'proposal', name: 'Business Proposal', description: 'Project or service proposals' },
      { id: 'analysis', name: 'Business Analysis', description: 'Analysis and recommendations' },
      { id: 'presentation', name: 'Presentation', description: 'Slide deck content and speaker notes' },
      { id: 'policy', name: 'Policy Document', description: 'Company policies and procedures' },
    ],
    defaultRequirements: {
      formalityLevel: 'formal',
      writingStyle: 'professional',
      includeExecutiveSummary: true,
      visualElements: { includeCharts: true },
    }
  },
  creative: {
    name: 'Creative Writing',
    icon: 'Feather',
    description: 'Stories, scripts, creative non-fiction',
    subTypes: [
      { id: 'short-story', name: 'Short Story', description: 'Fictional narrative work' },
      { id: 'screenplay', name: 'Screenplay', description: 'Script for film or television' },
      { id: 'memoir', name: 'Memoir', description: 'Personal life story or experiences' },
      { id: 'poetry', name: 'Poetry Collection', description: 'Collection of poems' },
      { id: 'creative-nonfiction', name: 'Creative Non-fiction', description: 'Narrative non-fiction writing' },
    ],
    defaultRequirements: {
      writingStyle: 'creative',
      formalityLevel: 'casual',
      voicePreference: 'first-person',
    }
  },
  guide: {
    name: 'Guide',
    icon: 'Map',
    description: 'How-to guides, tutorials, instructional content',
    subTypes: [
      { id: 'how-to-guide', name: 'How-to Guide', description: 'Step-by-step instructions' },
      { id: 'user-guide', name: 'User Guide', description: 'Product or software documentation' },
      { id: 'tutorial', name: 'Tutorial', description: 'Educational step-by-step content' },
      { id: 'best-practices', name: 'Best Practices Guide', description: 'Industry best practices and recommendations' },
      { id: 'troubleshooting', name: 'Troubleshooting Guide', description: 'Problem-solving documentation' },
    ],
    defaultRequirements: {
      writingStyle: 'instructional',
      organizationStyle: 'logical',
      practicalExamples: true,
      voicePreference: 'second-person',
    }
  },
  ebook: {
    name: 'eBook',
    icon: 'Book',
    description: 'Digital books, novels, non-fiction books, educational content',
    subTypes: [
      { id: 'fiction', name: 'Fiction eBook', description: 'Novels, short story collections, and fictional narratives' },
      { id: 'non-fiction', name: 'Non-Fiction eBook', description: 'Educational, informational, and instructional books' },
      { id: 'self-help', name: 'Self-Help eBook', description: 'Personal development and improvement guides' },
      { id: 'educational', name: 'Educational eBook', description: 'Textbooks, course materials, and learning resources' },
      { id: 'biography', name: 'Biography/Memoir', description: 'Life stories and personal experiences' },
      { id: 'reference', name: 'Reference eBook', description: 'Handbooks, encyclopedias, and reference materials' },
      { id: 'children', name: 'Children\'s eBook', description: 'Books designed for young readers' },
    ],
    defaultRequirements: {
      writingStyle: 'engaging',
      formalityLevel: 'casual-to-formal',
      narrativeStructure: true,
      chapterOrganization: 'sequential',
      readerEngagement: 'high',
      publishingFormat: 'digital',
      includeTableOfContents: true,
      targetWordCount: 'medium-to-long',
      voicePreference: 'consistent-throughout',
    }
  }
};

// Designrr-style Tone and Voice Options
export const toneAndVoiceOptions = {
  toneOfVoice: [
    { id: 'informative', name: 'Informative', description: 'Educational and fact-based approach' },
    { id: 'persuasive', name: 'Persuasive', description: 'Convincing and compelling tone' },
    { id: 'instructional', name: 'Instructional', description: 'Step-by-step guidance focused' },
    { id: 'inspirational', name: 'Inspirational', description: 'Motivating and uplifting' },
    { id: 'analytical', name: 'Analytical', description: 'Data-driven and logical' },
    { id: 'conversational', name: 'Conversational', description: 'Friendly and approachable' },
    { id: 'authoritative', name: 'Authoritative', description: 'Expert and confident' },
    { id: 'empathetic', name: 'Empathetic', description: 'Understanding and supportive' },
  ],

  writingStyle: [
    { id: 'professional', name: 'Professional', description: 'Business-appropriate and polished' },
    { id: 'academic', name: 'Academic', description: 'Scholarly and research-focused' },
    { id: 'conversational', name: 'Conversational', description: 'Natural and easy-going' },
    { id: 'technical', name: 'Technical', description: 'Precise and detail-oriented' },
    { id: 'creative', name: 'Creative', description: 'Artistic and imaginative' },
    { id: 'informative', name: 'Informative', description: 'Clear and educational' },
  ],

  voicePersonality: [
    { id: 'friendly', name: 'Friendly', description: 'Warm and welcoming' },
    { id: 'serious', name: 'Serious', description: 'Formal and focused' },
    { id: 'enthusiastic', name: 'Enthusiastic', description: 'Energetic and passionate' },
    { id: 'calm', name: 'Calm', description: 'Peaceful and reassuring' },
    { id: 'confident', name: 'Confident', description: 'Self-assured and decisive' },
    { id: 'humble', name: 'Humble', description: 'Modest and approachable' },
    { id: 'witty', name: 'Witty', description: 'Clever and engaging' },
    { id: 'straightforward', name: 'Straightforward', description: 'Direct and clear' },
  ],
};

// Sub-niche configurations based on main topics (Designrr-style)
export const subNicheConfigurations = {
  'life-coaching': [
    { id: 'wellness-health', name: 'Wellness and Health Coaching', description: 'Physical and mental wellness focus' },
    { id: 'career-transition', name: 'Career Transition Coaching', description: 'Professional development and career changes' },
    { id: 'relationship-dating', name: 'Relationship and Dating Coaching', description: 'Personal relationships and dating advice' },
    { id: 'executive-leadership', name: 'Executive Leadership Coaching', description: 'Leadership development for executives' },
    { id: 'confidence-self-esteem', name: 'Confidence and Self-Esteem Coaching', description: 'Building self-confidence and self-worth' },
    { id: 'mindfulness-stress', name: 'Mindfulness and Stress Management Coaching', description: 'Stress reduction and mindfulness practices' },
    { id: 'finance-wealth', name: 'Personal Finance and Wealth Coaching', description: 'Financial planning and wealth building' },
    { id: 'parenting-family', name: 'Parenting and Family Coaching', description: 'Family dynamics and parenting strategies' },
    { id: 'spiritual-growth', name: 'Spiritual Growth and Development Coaching', description: 'Spiritual development and growth' },
    { id: 'time-productivity', name: 'Time Management and Productivity Coaching', description: 'Efficiency and time management' },
  ],

  'business': [
    { id: 'startup-entrepreneurship', name: 'Startup and Entrepreneurship', description: 'Starting and growing new businesses' },
    { id: 'digital-marketing', name: 'Digital Marketing', description: 'Online marketing strategies and tactics' },
    { id: 'sales-strategy', name: 'Sales Strategy', description: 'Sales processes and techniques' },
    { id: 'leadership-management', name: 'Leadership and Management', description: 'Team leadership and management skills' },
    { id: 'finance-accounting', name: 'Finance and Accounting', description: 'Financial management and accounting' },
    { id: 'operations-efficiency', name: 'Operations and Efficiency', description: 'Business operations optimization' },
    { id: 'hr-talent', name: 'HR and Talent Management', description: 'Human resources and talent development' },
    { id: 'strategy-planning', name: 'Strategy and Planning', description: 'Business strategy and planning' },
  ],

  'health-wellness': [
    { id: 'nutrition-diet', name: 'Nutrition and Diet', description: 'Healthy eating and nutrition guidance' },
    { id: 'fitness-exercise', name: 'Fitness and Exercise', description: 'Physical fitness and workout routines' },
    { id: 'mental-health', name: 'Mental Health', description: 'Mental wellness and emotional health' },
    { id: 'alternative-medicine', name: 'Alternative Medicine', description: 'Holistic and alternative health approaches' },
    { id: 'weight-management', name: 'Weight Management', description: 'Healthy weight loss and maintenance' },
    { id: 'stress-management', name: 'Stress Management', description: 'Stress reduction techniques and coping' },
    { id: 'sleep-recovery', name: 'Sleep and Recovery', description: 'Sleep optimization and recovery' },
    { id: 'chronic-conditions', name: 'Chronic Conditions', description: 'Managing chronic health conditions' },
  ],
};

// Theme and project configurations (Designrr-style)
export const themeConfigurations = {
  'self-development': {
    name: 'Self Development',
    icon: '🌱',
    color: '#10B981',
    popularTopics: ['Personal Growth', 'Mindfulness', 'Goal Setting', 'Habit Formation']
  },
  'education': {
    name: 'Education',
    icon: '📚',
    color: '#3B82F6',
    popularTopics: ['Learning Strategies', 'Study Skills', 'Academic Success', 'Online Learning']
  },
  'health-wellness': {
    name: 'Health & Wellness',
    icon: '🏃‍♂️',
    color: '#EF4444',
    popularTopics: ['Fitness', 'Nutrition', 'Mental Health', 'Preventive Care']
  },
  'business': {
    name: 'Business',
    icon: '💼',
    color: '#8B5CF6',
    popularTopics: ['Entrepreneurship', 'Marketing', 'Leadership', 'Strategy']
  },
  'digital-marketing': {
    name: 'Digital Marketing',
    icon: '📱',
    color: '#F59E0B',
    popularTopics: ['Social Media', 'SEO', 'Content Marketing', 'Email Marketing']
  },
  'life-coaching': {
    name: 'Life Coaching',
    icon: '🎯',
    color: '#06B6D4',
    popularTopics: ['Career Coaching', 'Relationship Coaching', 'Wellness Coaching', 'Executive Coaching']
  },
  'spiritual-self-development': {
    name: 'Spiritual Self Development',
    icon: '🧘‍♀️',
    color: '#84CC16',
    popularTopics: ['Meditation', 'Spirituality', 'Mindfulness', 'Inner Peace']
  },
  'training-development': {
    name: 'Training and Development',
    icon: '🎓',
    color: '#F97316',
    popularTopics: ['Skill Development', 'Professional Training', 'Certification', 'Workshops']
  },
  'business-development-sales': {
    name: 'Business Development / Sales',
    icon: '📈',
    color: '#EC4899',
    popularTopics: ['Sales Strategy', 'Lead Generation', 'Customer Relations', 'Revenue Growth']
  },
  'writing-non-fiction': {
    name: 'Writing Non-Fiction',
    icon: '✍️',
    color: '#6366F1',
    popularTopics: ['Content Writing', 'Technical Writing', 'Copywriting', 'Blogging']
  },
  'author': {
    name: 'Author',
    icon: '📖',
    color: '#14B8A6',
    popularTopics: ['Book Writing', 'Publishing', 'Author Platform', 'Creative Writing']
  },
  'other': {
    name: 'Other',
    icon: '🔧',
    color: '#6B7280',
    popularTopics: ['Custom Topics', 'Specialized Fields', 'Niche Subjects', 'Unique Content']
  },
  'e-commerce': {
    name: 'E-Commerce',
    icon: '🛒',
    color: '#DC2626',
    popularTopics: ['Online Selling', 'Product Marketing', 'Customer Experience', 'E-commerce Strategy']
  },
  'blogging': {
    name: 'Blogging',
    icon: '📝',
    color: '#7C3AED',
    popularTopics: ['Content Creation', 'Blog Strategy', 'SEO Writing', 'Audience Building']
  },
  'advertising': {
    name: 'Advertising',
    icon: '📢',
    color: '#F59E0B',
    popularTopics: ['Ad Strategy', 'Campaign Management', 'Brand Advertising', 'Performance Marketing']
  },
};

// Validation rules for each step (Updated for Designrr-style flow with outline step)
export const validationRules = {
  step1: {
    required: ['documentPurpose.primaryType', 'documentPurpose.format', 'documentPurpose.baseline'],
    conditional: {}
  },
  step2: {
    required: ['topicAndNiche.mainTopic', 'topicAndNiche.language'],
    conditional: {}
  },
  step3: {
    required: ['topicAndNiche.subNiches'],
    conditional: {
      'topicAndNiche.subNiches.length === 0': ['topicAndNiche.customSubNiche']
    }
  },
  step4: {
    required: ['audienceAnalysis.primaryAudience'],
    conditional: {}
  },
  step5: {
    required: ['titleSelection.selectedTitle'],
    conditional: {
      'titleSelection.selectedTitle === "custom"': ['titleSelection.customTitle']
    }
  },
  step6: {
    required: ['toneAndVoice.toneOfVoice', 'toneAndVoice.writingStyle'],
    conditional: {}
  },
  step7: {
    required: ['documentOutline.approved'],
    conditional: {}
  },
  step8: {
    required: [],
    conditional: {}
  },
  step9: {
    required: [],
    conditional: {}
  }
};

// Helper function to get default data for a document type
export const getDefaultDataForDocumentType = (documentType) => {
  const config = documentTypeConfigs[documentType];
  if (!config) return {};

  return {
    ...config.defaultRequirements,
    documentPurpose: {
      ...defaultQuestionnaireData.documentPurpose,
      primaryType: documentType
    }
  };
};

// Helper function to calculate completion percentage
export const calculateCompletionPercentage = (data) => {
  const totalSteps = 6;
  let completedSteps = 0;

  // Check each step's completion based on required fields
  Object.keys(validationRules).forEach(step => {
    const rules = validationRules[step];
    const isStepComplete = rules.required.every(field => {
      const value = getNestedValue(data, field);
      return value !== '' && value !== null && value !== undefined;
    });
    
    if (isStepComplete) completedSteps++;
  });

  return Math.round((completedSteps / totalSteps) * 100);
};

// Helper function to get nested object values
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};
