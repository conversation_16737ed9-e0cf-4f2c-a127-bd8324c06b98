import React, { useState, useEffect } from 'react';
import Button from '../../../../components/ui/Button';
import { generateDocumentOutline } from '../../../../services/aiService';
import { generateImageSuggestions } from '../../../../services/unsplashService';

/**
 * DocumentOutlineStep - Dedicated step for AI-generated document outline
 * Shows the structure and chapters that will be created for the document
 */
const DocumentOutlineStep = ({ 
  formData, 
  onInputChange, 
  onValidationChange,
  className = '' 
}) => {
  const [generatedOutline, setGeneratedOutline] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);
  const [imageGenerationComplete, setImageGenerationComplete] = useState(false);

  // Generate document outline using real AI service
  const generateOutline = async () => {
    setIsGenerating(true);
    
    try {
      // Use real AI service to generate outline
      const aiOutline = await generateDocumentOutline(formData);
      
      setGeneratedOutline(aiOutline);
      onInputChange('documentOutline.generatedOutline', aiOutline);
      setHasGenerated(true);
      
    } catch (error) {
      console.error('Error generating outline:', error);
      
      // Fallback to mock outline if AI fails
      const fallbackOutline = generateFallbackOutline(formData);
      setGeneratedOutline(fallbackOutline);
      onInputChange('documentOutline.generatedOutline', fallbackOutline);
      setHasGenerated(true);
    } finally {
      setIsGenerating(false);
    }
  };

  // Fallback outline generation if AI fails
  const generateFallbackOutline = (documentData) => {
    const topic = documentData.topicAndNiche?.mainTopic || 'Your Topic';
    const title = documentData.titleSelection?.selectedTitle || `Guide to ${topic}`;
    
    return {
      title: title,
      chapters: [
        {
          number: 1,
          title: `Understanding ${topic}`,
          sections: [
            `What is ${topic}?`,
            'Why it matters',
            'Getting started'
          ]
        },
        {
          number: 2,
          title: 'Fundamentals and Core Concepts',
          sections: [
            'Key principles',
            'Essential terminology',
            'Foundation strategies'
          ]
        },
        {
          number: 3,
          title: 'Practical Implementation',
          sections: [
            'Step-by-step approach',
            'Real-world examples',
            'Common challenges'
          ]
        },
        {
          number: 4,
          title: 'Advanced Strategies and Success',
          sections: [
            'Expert techniques',
            'Long-term success',
            'Next steps'
          ]
        }
      ]
    };
  };

  // Generate outline when component mounts
  useEffect(() => {
    if (formData.titleSelection?.selectedTitle && !hasGenerated) {
      generateOutline();
    } else if (formData.documentOutline?.generatedOutline) {
      setGeneratedOutline(formData.documentOutline.generatedOutline);
      setHasGenerated(true);
    }
  }, [formData.titleSelection?.selectedTitle, hasGenerated]);

  // Reset hasGenerated flag when generatedOutline is cleared (e.g., returning from editor)
  useEffect(() => {
    const selectedTitle = formData.titleSelection?.selectedTitle;
    const generatedOutline = formData.documentOutline?.generatedOutline;

    // Reset if title exists but no generated outline (indicates reset)
    if (selectedTitle && !generatedOutline) {
      console.log('Detected AI content reset - resetting outline generation flag');
      setHasGenerated(false);
      setGeneratedOutline(null);
    }
  }, [formData.documentOutline?.generatedOutline]);

  const handleRegenerateOutline = () => {
    setHasGenerated(false);
    generateOutline();
  };

  const handleApproveOutline = () => {
    onInputChange('documentOutline.approved', true);
    // Note: Image generation is now controlled by the toggle switch
  };

  const handleToggleImages = async (enabled) => {
    onInputChange('imageAddition.enabled', enabled);

    if (enabled) {
      // Generate image suggestions when toggle is turned on
      await generateImageSuggestionsForOutline();
    } else {
      // Clear suggestions when toggle is turned off
      setImageGenerationComplete(false);
      onInputChange('imageAddition.suggestions', {});
    }
  };

  const generateImageSuggestionsForOutline = async () => {
    if (!generatedOutline) return;

    setIsGeneratingImages(true);
    try {
      console.log('🖼️ Generating image suggestions for approved outline...');
      const suggestions = await generateImageSuggestions(formData, generatedOutline);

      // Store suggestions in form data for document editor
      onInputChange('imageAddition.suggestions', suggestions);

      setImageGenerationComplete(true);
      console.log('✅ Image suggestions generated:', Object.keys(suggestions).length, 'chapters');
    } catch (error) {
      console.error('❌ Error generating image suggestions:', error);
    } finally {
      setIsGeneratingImages(false);
    }
  };

  // Validation - outline should be generated and approved
  useEffect(() => {
    const isValid = generatedOutline && formData.documentOutline?.approved;
    onValidationChange?.(isValid);
  }, [generatedOutline, formData.documentOutline?.approved]);

  return (
    <div className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Document Outline
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          Review the AI-generated structure for your document
        </p>
      </div>

      {/* Document Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg border border-blue-200">
        <h3 className="font-semibold text-text-primary mb-3">Document Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-text-secondary">Topic:</span>
            <span className="font-medium text-text-primary ml-2">
              {formData.topicAndNiche?.mainTopic || 'Not specified'}
            </span>
          </div>
          <div>
            <span className="text-text-secondary">Audience:</span>
            <span className="font-medium text-text-primary ml-2">
              {formData.audienceAnalysis?.primaryAudience || 'Not specified'}
            </span>
          </div>
          <div>
            <span className="text-text-secondary">Tone:</span>
            <span className="font-medium text-text-primary ml-2">
              {formData.toneAndVoice?.toneOfVoice || 'Informative'}
            </span>
          </div>
          <div>
            <span className="text-text-secondary">Type:</span>
            <span className="font-medium text-text-primary ml-2">
              {formData.documentPurpose?.primaryType || 'eBook'}
            </span>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isGenerating && (
        <div className="text-center py-12">
          <div className="inline-flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="text-text-secondary">Generating document outline using AI...</span>
          </div>
        </div>
      )}

      {/* Generated Outline */}
      {!isGenerating && generatedOutline && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-text-primary">
                AI-Generated Outline
              </h3>
              <p className="text-text-secondary text-sm">
                This structure will guide the content generation
              </p>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleRegenerateOutline}
              iconName="RefreshCw"
              iconPosition="left"
            >
              Regenerate
            </Button>
          </div>

          {/* Document Title */}
          <div className="bg-white p-6 rounded-lg border border-border shadow-sm">
            <h4 className="text-xl font-bold text-text-primary mb-4">
              {generatedOutline.title}
            </h4>
            
            {/* Chapters */}
            <div className="space-y-6">
              {generatedOutline.chapters?.map((chapter) => (
                <div key={chapter.number} className="border-l-4 border-primary/30 pl-6">
                  <h5 className="font-semibold text-text-primary mb-3">
                    Chapter {chapter.number}: {chapter.title}
                  </h5>
                  <ul className="space-y-2">
                    {chapter.sections?.map((section, index) => (
                      <li key={index} className="text-sm text-text-secondary flex items-start">
                        <span className="w-2 h-2 bg-primary/60 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        {section}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Approval Section */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  Approve This Outline
                </h3>
                <p className="text-green-700 mb-4">
                  This outline looks great! The AI has created a logical structure that will guide the content generation process.
                </p>
                <Button
                  variant={formData.documentOutline?.approved ? "success" : "primary"}
                  onClick={handleApproveOutline}
                  iconName={formData.documentOutline?.approved ? "Check" : "ThumbsUp"}
                  iconPosition="left"
                  disabled={formData.documentOutline?.approved}
                >
                  {formData.documentOutline?.approved ? "Outline Approved" : "Approve Outline"}
                </Button>
              </div>
            </div>
          </div>

          {/* Image Addition Toggle - Show after approval */}
          {formData.documentOutline?.approved && (
            <div className="bg-white p-6 rounded-lg border border-border shadow-sm mt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="bg-primary/10 p-3 rounded-lg">
                    <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary">Add images to content</h3>
                    <p className="text-sm text-text-secondary">
                      AI will suggest relevant images for each chapter that you can select and place in the document editor
                    </p>
                  </div>
                </div>

                {/* Toggle Switch */}
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.imageAddition?.enabled || false}
                    onChange={(e) => handleToggleImages(e.target.checked)}
                    className="sr-only peer"
                    disabled={isGeneratingImages}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary peer-disabled:opacity-50"></div>
                </label>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DocumentOutlineStep;
