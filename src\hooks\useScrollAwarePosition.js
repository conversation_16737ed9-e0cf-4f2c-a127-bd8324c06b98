import { useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook for managing scroll-aware positioning of floating elements
 * 
 * This hook provides:
 * - Throttled scroll event listeners for performance
 * - Position update callbacks when scroll occurs
 * - Automatic cleanup of event listeners
 * - RequestAnimationFrame optimization for smooth updates
 * - Intersection Observer for visibility management
 * 
 * @param {Object} options - Configuration options
 * @param {Function} options.onPositionUpdate - Callback when position needs updating
 * @param {HTMLElement} options.targetElement - Element to track positioning for
 * @param {boolean} options.enabled - Whether positioning is enabled
 * @param {number} options.throttleMs - Throttle delay in milliseconds (default: 16ms for 60fps)
 * @returns {Object} Hook utilities and state
 */
const useScrollAwarePosition = ({
  onPositionUpdate,
  targetElement,
  enabled = true,
  throttleMs = 16 // 60fps
}) => {
  const scrollTimeoutRef = useRef(null);
  const lastScrollY = useRef(window.scrollY);
  const animationFrameRef = useRef(null);
  const intersectionObserverRef = useRef(null);
  const isVisibleRef = useRef(true);

  // Throttled scroll handler using requestAnimationFrame for smooth performance
  const handleScroll = useCallback(() => {
    if (!enabled || !onPositionUpdate || !targetElement) return;

    // Cancel any pending animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Use requestAnimationFrame for smooth updates
    animationFrameRef.current = requestAnimationFrame(() => {
      const currentScrollY = window.scrollY;

      // Only update if scroll position actually changed and element is visible
      if (Math.abs(currentScrollY - lastScrollY.current) > 1 && isVisibleRef.current) {
        lastScrollY.current = currentScrollY;

        // Call the position update callback
        onPositionUpdate({
          scrollY: currentScrollY,
          scrollDelta: currentScrollY - lastScrollY.current,
          targetElement
        });
      }
    });
  }, [enabled, onPositionUpdate, targetElement]);

  // Throttled scroll handler with fallback timeout
  const throttledScrollHandler = useCallback(() => {
    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Set new timeout for throttling
    scrollTimeoutRef.current = setTimeout(handleScroll, throttleMs);
  }, [handleScroll, throttleMs]);

  // Set up intersection observer for visibility tracking
  useEffect(() => {
    if (!enabled || !targetElement) return;

    // Create intersection observer to track element visibility
    intersectionObserverRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const wasVisible = isVisibleRef.current;
          isVisibleRef.current = entry.isIntersecting;

          // Only trigger position updates for visibility changes, not during normal scroll
          if (onPositionUpdate && wasVisible !== entry.isIntersecting) {
            onPositionUpdate({
              scrollY: window.scrollY,
              scrollDelta: 0,
              targetElement,
              isVisible: entry.isIntersecting,
              visibilityChanged: true
            });
          }
        });
      },
      {
        // More forgiving threshold - trigger when any part is visible
        threshold: 0,
        // Larger margin to prevent premature hiding during scroll transitions
        rootMargin: '100px 0px'
      }
    );

    // Start observing the target element
    intersectionObserverRef.current.observe(targetElement);

    return () => {
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }
    };
  }, [enabled, targetElement, onPositionUpdate]);

  // Set up scroll event listener
  useEffect(() => {
    if (!enabled) return;

    // Add scroll event listener with passive option for better performance
    window.addEventListener('scroll', throttledScrollHandler, { passive: true });

    // Also listen for resize events that might affect positioning
    window.addEventListener('resize', throttledScrollHandler, { passive: true });

    return () => {
      // Cleanup event listeners
      window.removeEventListener('scroll', throttledScrollHandler);
      window.removeEventListener('resize', throttledScrollHandler);

      // Clear any pending timeouts
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Cancel any pending animation frames
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enabled, throttledScrollHandler]);

  // Utility function to manually trigger position update
  const updatePosition = useCallback(() => {
    if (enabled && onPositionUpdate && targetElement) {
      onPositionUpdate({
        scrollY: window.scrollY,
        scrollDelta: 0,
        targetElement,
        isVisible: isVisibleRef.current,
        manual: true
      });
    }
  }, [enabled, onPositionUpdate, targetElement]);

  // Utility function to get current scroll information
  const getScrollInfo = useCallback(() => {
    return {
      scrollY: window.scrollY,
      scrollX: window.scrollX,
      viewportHeight: window.innerHeight,
      viewportWidth: window.innerWidth,
      isVisible: isVisibleRef.current
    };
  }, []);

  return {
    updatePosition,
    getScrollInfo,
    isVisible: isVisibleRef.current
  };
};

export default useScrollAwarePosition;
