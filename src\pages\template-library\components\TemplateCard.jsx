import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const TemplateCard = ({ template, onSelect, onPreview, onFavorite }) => {
  const [isHovered, setIsHovered] = useState(false);

  const formatBadges = (formats) => {
    const badgeColors = {
      PDF: 'bg-error/10 text-error',
      EPUB: 'bg-success/10 text-success',
      MOBI: 'bg-warning/10 text-warning',
      DOCX: 'bg-secondary/10 text-secondary'
    };

    return formats.map(format => ({
      format,
      className: badgeColors[format] || 'bg-background text-text-secondary'
    }));
  };

  const getDifficultyColor = (level) => {
    switch (level) {
      case 'beginner':
        return 'text-success';
      case 'intermediate':
        return 'text-warning';
      case 'advanced':
        return 'text-error';
      default:
        return 'text-text-secondary';
    }
  };

  const getCreditColor = (credits) => {
    if (credits === 0) return 'text-success';
    if (credits <= 5) return 'text-warning';
    return 'text-error';
  };

  return (
    <div
      className="bg-surface border border-border rounded-lg overflow-hidden shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Template Preview Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={template.thumbnail}
          alt={template.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
        
        {/* Overlay Actions */}
        <div className={`absolute inset-0 bg-black/50 flex items-center justify-center space-x-2 transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <Button
            variant="secondary"
            onClick={() => onPreview(template)}
            className="text-sm"
          >
            <Icon name="Eye" size={16} />
            Preview
          </Button>
          <Button
            variant="primary"
            onClick={() => onSelect(template)}
            className="text-sm"
          >
            <Icon name="Plus" size={16} />
            Use Template
          </Button>
        </div>

        {/* Favorite Button */}
        <button
          onClick={() => onFavorite(template)}
          className="absolute top-3 right-3 p-2 bg-surface/90 rounded-full hover:bg-surface transition-colors"
        >
          <Icon 
            name={template.isFavorite ? "Heart" : "Heart"} 
            size={16} 
            color={template.isFavorite ? "var(--color-error)" : "var(--color-text-secondary)"}
          />
        </button>

        {/* New Badge */}
        {template.isNew && (
          <div className="absolute top-3 left-3 bg-accent text-accent-foreground text-xs px-2 py-1 rounded-full font-medium">
            New
          </div>
        )}

        {/* Premium Badge */}
        {template.isPremium && (
          <div className="absolute top-3 left-3 bg-warning text-warning-foreground text-xs px-2 py-1 rounded-full font-medium flex items-center space-x-1">
            <Icon name="Crown" size={12} />
            <span>Premium</span>
          </div>
        )}
      </div>

      {/* Template Info */}
      <div className="p-4">
        {/* Title and Category */}
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-text-primary mb-1 line-clamp-1">
            {template.title}
          </h3>
          <p className="text-sm text-text-secondary">{template.category}</p>
        </div>

        {/* Description */}
        <p className="text-sm text-text-secondary mb-4 line-clamp-2">
          {template.description}
        </p>

        {/* Format Badges */}
        <div className="flex flex-wrap gap-1 mb-4">
          {formatBadges(template.formats).map(({ format, className }) => (
            <span
              key={format}
              className={`text-xs px-2 py-1 rounded-full font-medium ${className}`}
            >
              {format}
            </span>
          ))}
        </div>

        {/* Stats Row */}
        <div className="flex items-center justify-between text-xs text-text-secondary mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Icon name="Users" size={12} />
              <span>{template.usageCount.toLocaleString()}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Icon name="Star" size={12} />
              <span>{template.rating}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Icon name="Clock" size={12} />
              <span>{template.estimatedTime}</span>
            </div>
          </div>
        </div>

        {/* Bottom Row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`text-xs font-medium ${getDifficultyColor(template.complexity)}`}>
              {template.complexity.charAt(0).toUpperCase() + template.complexity.slice(1)}
            </div>
            <div className="text-xs text-text-secondary">
              {template.language}
            </div>
          </div>
          
          <div className={`text-sm font-semibold ${getCreditColor(template.credits)}`}>
            {template.credits === 0 ? 'Free' : `${template.credits} Credits`}
          </div>
        </div>

        {/* Action Buttons - Mobile */}
        <div className="flex space-x-2 mt-4 lg:hidden">
          <Button
            variant="outline"
            onClick={() => onPreview(template)}
            className="flex-1 text-sm"
          >
            <Icon name="Eye" size={16} />
            Preview
          </Button>
          <Button
            variant="primary"
            onClick={() => onSelect(template)}
            className="flex-1 text-sm"
          >
            <Icon name="Plus" size={16} />
            Use Template
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TemplateCard;