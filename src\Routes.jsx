import React from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
import ProtectedRoute from "components/auth/ProtectedRoute";
// Add your imports here
import Dashboard from "pages/dashboard";
import Projects from "pages/projects";
import TemplateLibrary from "pages/template-library";
import PlagiarismChecker from "pages/plagiarism-checker";
import AccountSettings from "pages/account-settings";
import DocumentCreator from "pages/document-creator";
import DocumentEditor from "pages/document-editor";
import DocumentPublish from "pages/document-editor/components/DocumentPublish";
import DocumentReview from "pages/document-editor/components/DocumentReview";
import AuthPage from "pages/auth";
import NotFound from "pages/NotFound";
import TiptapEditorTest from "components/TiptapEditorTest";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Public routes */}
        <Route path="/auth" element={<AuthPage />} />

        {/* Protected routes */}
        <Route path="/" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        <Route path="/projects" element={
          <ProtectedRoute>
            <Projects />
          </ProtectedRoute>
        } />
        <Route path="/template-library" element={
          <ProtectedRoute>
            <TemplateLibrary />
          </ProtectedRoute>
        } />
        <Route path="/plagiarism-checker" element={
          <ProtectedRoute>
            <PlagiarismChecker />
          </ProtectedRoute>
        } />
        <Route path="/account-settings" element={
          <ProtectedRoute>
            <AccountSettings />
          </ProtectedRoute>
        } />
        <Route path="/document-creator" element={
          <ProtectedRoute>
            <DocumentCreator />
          </ProtectedRoute>
        } />
        <Route path="/document-editor/:documentId" element={
          <ProtectedRoute>
            <DocumentEditor />
          </ProtectedRoute>
        } />
        <Route path="/document-editor/:documentId/review" element={
          <ProtectedRoute>
            <DocumentReview />
          </ProtectedRoute>
        } />
        <Route path="/document-editor/:documentId/publish" element={
          <ProtectedRoute>
            <DocumentPublish />
          </ProtectedRoute>
        } />

        {/* Test route for Tiptap editor development */}
        <Route path="/test-editor" element={
          <ProtectedRoute>
            <TiptapEditorTest />
          </ProtectedRoute>
        } />

        {/* Read-only document view */}

        {/* Fallback route */}
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;