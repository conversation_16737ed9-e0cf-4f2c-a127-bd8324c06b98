import { useState, useEffect } from 'react';
import { projectsService } from '../services/projectsService';

/**
 * Custom hook for fetching and managing projects data
 * @param {Object} options - Configuration options
 * @param {string} options.sortBy - Field to sort by (updated_at, created_at, title, progress, last_accessed_at)
 * @param {string} options.sortOrder - Sort order (asc, desc)
 * @param {number} options.limit - Maximum number of projects to fetch
 * @param {string} options.category - Filter by category
 * @param {string} options.status - Filter by status
 * @param {boolean} options.autoLoad - Whether to automatically load data on mount
 * @returns {Object} Hook state and methods
 */
export const useProjects = (options = {}) => {
  const {
    sortBy = 'updated_at',
    sortOrder = 'desc',
    limit = null,
    category = null,
    status = null,
    autoLoad = true
  } = options;

  const [projects, setProjects] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadProjects = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const queryOptions = {
        sortBy,
        sortOrder,
        category,
        status
      };

      // Add limit if specified
      if (limit !== null) {
        queryOptions.limit = limit;
      }

      const response = await projectsService.getUserProjects(queryOptions);

      if (response.success) {
        setProjects(response.data.projects || []);
      } else {
        setError(response.error?.message || 'Failed to load projects');
        console.error('Failed to load projects:', response.error);
      }
    } catch (err) {
      setError('An unexpected error occurred');
      console.error('Error loading projects:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-load projects when dependencies change
  useEffect(() => {
    if (autoLoad) {
      loadProjects();
    }
  }, [sortBy, sortOrder, limit, category, status, autoLoad]);

  return {
    projects,
    isLoading,
    error,
    loadProjects,
    refetch: loadProjects,
    // Utility methods
    isEmpty: projects.length === 0 && !isLoading && !error,
    hasData: projects.length > 0
  };
};

export default useProjects;
