/**
 * Unit tests for image processing functionality in contentProcessingService
 * Tests HTML and markdown image extraction, download functionality, error handling, and retry logic
 */

import {
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    validateImageUrl,
    validateImageFormat,
    downloadImage,
    processImages,
    convertImageForDocx,
    extractAndProcessImages,
    IMAGE_CONFIG
} from '../contentProcessingService.js';

// Mock fetch for testing
global.fetch = jest.fn();

describe('Image Extraction Tests', () => {
    describe('extractImagesFromHTML', () => {
        test('should extract images from HTML content', () => {
            const htmlContent = `
                <div>
                    <img src="https://example.com/image1.jpg" alt="Test Image 1" width="300" height="200" />
                    <p>Some text</p>
                    <img src="https://example.com/image2.png" alt="Test Image 2" class="large" />
                    <img src="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD" alt="Base64 Image" />
                </div>
            `;

            const images = extractImagesFromHTML(htmlContent);

            expect(images).toHaveLength(3);
            expect(images[0]).toEqual({
                src: 'https://example.com/image1.jpg',
                alt: 'Test Image 1',
                width: '300',
                height: '200',
                class: null,
                index: 0,
                type: 'html'
            });
            expect(images[1]).toEqual({
                src: 'https://example.com/image2.png',
                alt: 'Test Image 2',
                width: null,
                height: null,
                class: 'large',
                index: 1,
                type: 'html'
            });
            expect(images[2].src).toContain('data:image/jpeg');
        });

        test('should handle empty or invalid HTML content', () => {
            expect(extractImagesFromHTML('')).toEqual([]);
            expect(extractImagesFromHTML(null)).toEqual([]);
            expect(extractImagesFromHTML('<div>No images here</div>')).toEqual([]);
        });

        test('should skip images without src attribute', () => {
            const htmlContent = '<img alt="No src" /><img src="valid.jpg" alt="Valid" />';
            const images = extractImagesFromHTML(htmlContent);

            expect(images).toHaveLength(1);
            expect(images[0].src).toBe('valid.jpg');
        });
    });

    describe('extractImagesFromMarkdown', () => {
        test('should extract images from markdown content', () => {
            const markdownContent = `
                # Title
                
                Here's an image: ![Alt text](https://example.com/image1.jpg)
                
                And another: ![Another image](https://example.com/image2.png)
                
                ![](https://example.com/image3.gif)
                
                Some text without images.
            `;

            const images = extractImagesFromMarkdown(markdownContent);

            expect(images).toHaveLength(3);
            expect(images[0]).toEqual({
                src: 'https://example.com/image1.jpg',
                alt: 'Alt text',
                markdownSyntax: '![Alt text](https://example.com/image1.jpg)',
                index: 0,
                type: 'markdown'
            });
            expect(images[1]).toEqual({
                src: 'https://example.com/image2.png',
                alt: 'Another image',
                markdownSyntax: '![Another image](https://example.com/image2.png)',
                index: 1,
                type: 'markdown'
            });
            expect(images[2]).toEqual({
                src: 'https://example.com/image3.gif',
                alt: 'Image 3',
                markdownSyntax: '![](https://example.com/image3.gif)',
                index: 2,
                type: 'markdown'
            });
        });

        test('should handle empty or invalid markdown content', () => {
            expect(extractImagesFromMarkdown('')).toEqual([]);
            expect(extractImagesFromMarkdown(null)).toEqual([]);
            expect(extractImagesFromMarkdown('# Title\n\nNo images here')).toEqual([]);
        });

        test('should handle malformed markdown image syntax', () => {
            const markdownContent = `
                ![Incomplete
                ![Valid image](https://example.com/valid.jpg)
                ![Missing closing](https://example.com/missing
            `;

            const images = extractImagesFromMarkdown(markdownContent);
            expect(images).toHaveLength(1);
            expect(images[0].src).toBe('https://example.com/valid.jpg');
        });
    });
});

describe('Image Validation Tests', () => {
    describe('validateImageUrl', () => {
        test('should validate correct URLs', () => {
            const validUrls = [
                'https://example.com/image.jpg',
                'http://example.com/image.png',
                'https://cdn.example.com/path/to/image.gif',
                'https://example.com/image' // No extension is OK
            ];

            validUrls.forEach(url => {
                const result = validateImageUrl(url);
                expect(result.isValid).toBe(true);
                expect(result.url).toBe(url);
            });
        });

        test('should reject invalid URLs', () => {
            const invalidUrls = [
                '',
                null,
                undefined,
                'not-a-url',
                'ftp://example.com/image.jpg',
                'file:///local/image.jpg',
                '   ', // whitespace only
            ];

            invalidUrls.forEach(url => {
                const result = validateImageUrl(url);
                expect(result.isValid).toBe(false);
                expect(result.error).toBeDefined();
            });
        });

        test('should handle URLs with whitespace', () => {
            const result = validateImageUrl('  https://example.com/image.jpg  ');
            expect(result.isValid).toBe(true);
            expect(result.url).toBe('https://example.com/image.jpg');
        });
    });

    describe('validateImageFormat', () => {
        test('should validate supported image formats', () => {
            const supportedFormats = [
                'image/jpeg',
                'image/jpg',
                'image/png',
                'image/gif',
                'image/webp'
            ];

            supportedFormats.forEach(format => {
                const result = validateImageFormat(format);
                expect(result.isValid).toBe(true);
                expect(result.format).toBe(format);
                expect(result.extension).toBeDefined();
            });
        });

        test('should reject unsupported formats', () => {
            const unsupportedFormats = [
                'image/svg+xml',
                'image/bmp',
                'text/html',
                'application/pdf',
                ''
            ];

            unsupportedFormats.forEach(format => {
                const result = validateImageFormat(format);
                expect(result.isValid).toBe(false);
                expect(result.error).toBeDefined();
            });
        });

        test('should handle content type with charset', () => {
            const result = validateImageFormat('image/jpeg; charset=utf-8');
            expect(result.isValid).toBe(true);
            expect(result.format).toBe('image/jpeg');
        });
    });
});

describe('Image Download Tests', () => {
    beforeEach(() => {
        fetch.mockClear();
    });

    describe('downloadImage', () => {
        test('should successfully download valid image', async () => {
            const mockArrayBuffer = new ArrayBuffer(1024);
            const mockResponse = {
                ok: true,
                status: 200,
                headers: {
                    get: jest.fn((header) => {
                        if (header === 'content-type') return 'image/jpeg';
                        if (header === 'content-length') return '1024';
                        return null;
                    })
                },
                arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer)
            };

            fetch.mockResolvedValue(mockResponse);

            const result = await downloadImage('https://example.com/image.jpg');

            expect(result.success).toBe(true);
            expect(result.data).toBe(mockArrayBuffer);
            expect(result.contentType).toBe('image/jpeg');
            expect(result.extension).toBe('.jpg');
            expect(result.size).toBe(1024);
        });

        test('should handle HTTP errors', async () => {
            const mockResponse = {
                ok: false,
                status: 404,
                statusText: 'Not Found'
            };

            fetch.mockResolvedValue(mockResponse);

            const result = await downloadImage('https://example.com/nonexistent.jpg');

            expect(result.success).toBe(false);
            expect(result.error).toContain('HTTP 404');
        });

        test('should handle network errors with retry', async () => {
            fetch.mockRejectedValue(new Error('Network error'));

            const result = await downloadImage('https://example.com/image.jpg');

            expect(result.success).toBe(false);
            expect(result.error).toContain('Failed to download image after 3 attempts');
            expect(fetch).toHaveBeenCalledTimes(3); // Should retry 3 times
        });

        test('should reject oversized images', async () => {
            const mockResponse = {
                ok: true,
                status: 200,
                headers: {
                    get: jest.fn((header) => {
                        if (header === 'content-type') return 'image/jpeg';
                        if (header === 'content-length') return (IMAGE_CONFIG.MAX_SIZE + 1).toString();
                        return null;
                    })
                }
            };

            fetch.mockResolvedValue(mockResponse);

            const result = await downloadImage('https://example.com/huge-image.jpg');

            expect(result.success).toBe(false);
            expect(result.error).toContain('Image too large');
        });

        test('should reject unsupported image formats', async () => {
            const mockResponse = {
                ok: true,
                status: 200,
                headers: {
                    get: jest.fn((header) => {
                        if (header === 'content-type') return 'image/svg+xml';
                        return null;
                    })
                }
            };

            fetch.mockResolvedValue(mockResponse);

            const result = await downloadImage('https://example.com/image.svg');

            expect(result.success).toBe(false);
            expect(result.error).toContain('Unsupported image format');
        });

        test('should handle invalid URLs', async () => {
            const result = await downloadImage('invalid-url');

            expect(result.success).toBe(false);
            expect(result.error).toContain('Invalid URL format');
            expect(fetch).not.toHaveBeenCalled();
        });
    });

    describe('processImages', () => {
        test('should process multiple images successfully', async () => {
            const mockArrayBuffer = new ArrayBuffer(1024);
            const mockResponse = {
                ok: true,
                status: 200,
                headers: {
                    get: jest.fn((header) => {
                        if (header === 'content-type') return 'image/jpeg';
                        if (header === 'content-length') return '1024';
                        return null;
                    })
                },
                arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer)
            };

            fetch.mockResolvedValue(mockResponse);

            const images = [
                { src: 'https://example.com/image1.jpg', alt: 'Image 1', index: 0 },
                { src: 'https://example.com/image2.png', alt: 'Image 2', index: 1 }
            ];

            const result = await processImages(images);

            expect(result.success).toBe(true);
            expect(result.processedImages).toHaveLength(2);
            expect(result.failedImages).toHaveLength(0);
            expect(result.successCount).toBe(2);
            expect(result.failureCount).toBe(0);
        });

        test('should handle mixed success and failure', async () => {
            // Mock successful response for first image
            const successResponse = {
                ok: true,
                status: 200,
                headers: {
                    get: jest.fn((header) => {
                        if (header === 'content-type') return 'image/jpeg';
                        if (header === 'content-length') return '1024';
                        return null;
                    })
                },
                arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(1024))
            };

            // Mock failed response for second image (will be retried 3 times)
            const failResponse = {
                ok: false,
                status: 404,
                statusText: 'Not Found'
            };

            // Set up fetch mock to return success for first call, then failures for retries
            fetch
                .mockResolvedValueOnce(successResponse)  // First image succeeds
                .mockResolvedValue(failResponse);        // Second image fails (all retries)

            const images = [
                { src: 'https://example.com/valid.jpg', alt: 'Valid', index: 0 },
                { src: 'https://example.com/invalid.jpg', alt: 'Invalid', index: 1 }
            ];

            const result = await processImages(images);

            expect(result.success).toBe(false);
            expect(result.processedImages).toHaveLength(1);
            expect(result.failedImages).toHaveLength(1);
            expect(result.successCount).toBe(1);
            expect(result.failureCount).toBe(1);
        });

        test('should handle empty image array', async () => {
            const result = await processImages([]);

            expect(result.success).toBe(true);
            expect(result.processedImages).toHaveLength(0);
            expect(result.failedImages).toHaveLength(0);
            expect(result.totalImages).toBe(0);
        });
    });
});

describe('Image Conversion Tests', () => {
    describe('convertImageForDocx', () => {
        test('should pass through supported formats', async () => {
            const mockData = new ArrayBuffer(1024);

            const result = await convertImageForDocx(mockData, 'image/jpeg');

            expect(result.success).toBe(true);
            expect(result.data).toBe(mockData);
            expect(result.contentType).toBe('image/jpeg');
        });

        test('should handle unsupported formats with warning', async () => {
            const mockData = new ArrayBuffer(1024);

            const result = await convertImageForDocx(mockData, 'image/gif');

            expect(result.success).toBe(true);
            expect(result.data).toBe(mockData);
            expect(result.warning).toBeDefined();
        });
    });
});

describe('Integration Tests', () => {
    describe('extractAndProcessImages', () => {
        beforeEach(() => {
            fetch.mockClear();
        });

        test('should extract and process HTML images', async () => {
            const mockArrayBuffer = new ArrayBuffer(1024);
            const mockResponse = {
                ok: true,
                status: 200,
                headers: {
                    get: jest.fn((header) => {
                        if (header === 'content-type') return 'image/jpeg';
                        if (header === 'content-length') return '1024';
                        return null;
                    })
                },
                arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer)
            };

            fetch.mockResolvedValue(mockResponse);

            const htmlContent = '<img src="https://example.com/image.jpg" alt="Test Image" />';

            const result = await extractAndProcessImages(htmlContent, 'html');

            expect(result.success).toBe(true);
            expect(result.images).toHaveLength(1);
            expect(result.processedImages).toHaveLength(1);
            expect(result.failedImages).toHaveLength(0);
            expect(result.contentType).toBe('html');
        });

        test('should extract and process markdown images', async () => {
            const mockArrayBuffer = new ArrayBuffer(1024);
            const mockResponse = {
                ok: true,
                status: 200,
                headers: {
                    get: jest.fn((header) => {
                        if (header === 'content-type') return 'image/png';
                        if (header === 'content-length') return '1024';
                        return null;
                    })
                },
                arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer)
            };

            fetch.mockResolvedValue(mockResponse);

            const markdownContent = '![Test Image](https://example.com/image.png)';

            const result = await extractAndProcessImages(markdownContent, 'markdown');

            expect(result.success).toBe(true);
            expect(result.images).toHaveLength(1);
            expect(result.processedImages).toHaveLength(1);
            expect(result.failedImages).toHaveLength(0);
            expect(result.contentType).toBe('markdown');
        });

        test('should handle content with no images', async () => {
            const result = await extractAndProcessImages('<p>No images here</p>', 'html');

            expect(result.success).toBe(true);
            expect(result.images).toHaveLength(0);
            expect(result.processedImages).toHaveLength(0);
            expect(result.totalImages).toBe(0);
            expect(result.message).toContain('No images found');
        });

        test('should handle processing errors gracefully', async () => {
            fetch.mockRejectedValue(new Error('Network error'));

            const htmlContent = '<img src="https://example.com/image.jpg" alt="Test Image" />';

            const result = await extractAndProcessImages(htmlContent, 'html');

            expect(result.success).toBe(false);
            expect(result.images).toHaveLength(1);
            expect(result.processedImages).toHaveLength(0);
            expect(result.failedImages).toHaveLength(1);
        });
    });
});

describe('Error Handling and Edge Cases', () => {
    test('should handle malformed HTML gracefully', () => {
        const malformedHtml = '<img src="test.jpg" alt="test" <p>broken html</p>';
        const images = extractImagesFromHTML(malformedHtml);

        // Should still extract the image despite malformed HTML
        expect(images).toHaveLength(1);
        expect(images[0].src).toBe('test.jpg');
    });

    test('should handle special characters in URLs', () => {
        const htmlContent = '<img src="https://example.com/image%20with%20spaces.jpg" alt="Special chars" />';
        const images = extractImagesFromHTML(htmlContent);

        expect(images).toHaveLength(1);
        expect(images[0].src).toBe('https://example.com/image%20with%20spaces.jpg');
    });

    test('should handle data URLs', () => {
        const dataUrl = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD';
        const htmlContent = `<img src="${dataUrl}" alt="Data URL" />`;
        const images = extractImagesFromHTML(htmlContent);

        expect(images).toHaveLength(1);
        expect(images[0].src).toBe(dataUrl);
    });

    test('should handle very long URLs', () => {
        const longUrl = 'https://example.com/' + 'a'.repeat(2000) + '.jpg';
        const result = validateImageUrl(longUrl);

        expect(result.isValid).toBe(true);
        expect(result.url).toBe(longUrl);
    });
});