// Authentication System Test Utility
// This file contains functions to test the authentication system

import { supabase } from '../lib/supabase'

export const authTests = {
  // Test basic connection to Supabase
  async testConnection() {
    try {
      console.log('🔍 Testing Supabase connection...')
      const { data, error } = await supabase.from('auth.users').select('count').limit(1)
      
      if (error) {
        console.log('⚠️  Connection test result:', error.message)
        return { success: false, error: error.message }
      }
      
      console.log('✅ Supabase connection successful!')
      return { success: true }
    } catch (error) {
      console.error('❌ Connection test failed:', error)
      return { success: false, error: error.message }
    }
  },

  // Test session retrieval
  async testSession() {
    try {
      console.log('🔍 Testing session retrieval...')
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.log('⚠️  Session test error:', error.message)
        return { success: false, error: error.message }
      }
      
      if (session) {
        console.log('✅ Active session found:', session.user.email)
        return { success: true, session }
      } else {
        console.log('ℹ️  No active session')
        return { success: true, session: null }
      }
    } catch (error) {
      console.error('❌ Session test failed:', error)
      return { success: false, error: error.message }
    }
  },

  // Test user profile creation (mock)
  async testProfileCreation(userId, profileData) {
    try {
      console.log('🔍 Testing profile creation...')
      
      // This would normally create a profile in the database
      // For now, we'll just validate the data structure
      const requiredFields = ['id', 'email']
      const missingFields = requiredFields.filter(field => !profileData[field])
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
      }
      
      console.log('✅ Profile data structure valid')
      return { success: true, data: profileData }
    } catch (error) {
      console.error('❌ Profile creation test failed:', error)
      return { success: false, error: error.message }
    }
  },

  // Test authentication flow (mock)
  async testAuthFlow() {
    console.log('🚀 Running authentication flow test...')
    
    const results = {
      connection: await this.testConnection(),
      session: await this.testSession(),
      profileStructure: await this.testProfileCreation('test-user-id', {
        id: 'test-user-id',
        email: '<EMAIL>',
        full_name: 'Test User',
        user_type: 'student'
      })
    }
    
    const allPassed = Object.values(results).every(result => result.success)
    
    console.log('📊 Test Results:')
    console.table(Object.entries(results).map(([test, result]) => ({
      Test: test,
      Status: result.success ? '✅ PASS' : '❌ FAIL',
      Message: result.error || 'Success'
    })))
    
    if (allPassed) {
      console.log('🎉 All authentication tests passed!')
    } else {
      console.log('⚠️  Some authentication tests failed')
    }
    
    return { success: allPassed, results }
  },

  // Test environment variables
  testEnvironment() {
    console.log('🔍 Testing environment configuration...')
    
    const requiredEnvVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ]
    
    const envStatus = requiredEnvVars.map(varName => {
      const value = import.meta.env[varName]
      return {
        variable: varName,
        present: !!value,
        value: value ? `${value.substring(0, 10)}...` : 'Not set'
      }
    })
    
    console.table(envStatus)
    
    const allPresent = envStatus.every(env => env.present)
    
    if (allPresent) {
      console.log('✅ All required environment variables are set')
    } else {
      console.log('❌ Some required environment variables are missing')
    }
    
    return { success: allPresent, envStatus }
  },

  // Test local storage functionality
  testLocalStorage() {
    console.log('🔍 Testing local storage functionality...')
    
    try {
      const testKey = 'docforge_test'
      const testValue = { test: true, timestamp: Date.now() }
      
      // Test write
      localStorage.setItem(testKey, JSON.stringify(testValue))
      
      // Test read
      const retrieved = JSON.parse(localStorage.getItem(testKey))
      
      // Test delete
      localStorage.removeItem(testKey)
      
      if (retrieved.test === testValue.test) {
        console.log('✅ Local storage working correctly')
        return { success: true }
      } else {
        throw new Error('Retrieved value does not match stored value')
      }
    } catch (error) {
      console.error('❌ Local storage test failed:', error)
      return { success: false, error: error.message }
    }
  },

  // Run all tests
  async runAllTests() {
    console.log('🧪 Running comprehensive authentication system tests...')
    console.log('=' .repeat(60))
    
    const testResults = {
      environment: this.testEnvironment(),
      localStorage: this.testLocalStorage(),
      connection: await this.testConnection(),
      session: await this.testSession()
    }
    
    const overallSuccess = Object.values(testResults).every(result => result.success)
    
    console.log('=' .repeat(60))
    console.log('📋 FINAL TEST SUMMARY:')
    console.log('=' .repeat(60))
    
    Object.entries(testResults).forEach(([testName, result]) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL'
      const message = result.error ? ` - ${result.error}` : ''
      console.log(`${status} ${testName.toUpperCase()}${message}`)
    })
    
    console.log('=' .repeat(60))
    
    if (overallSuccess) {
      console.log('🎉 ALL TESTS PASSED! Authentication system is ready.')
    } else {
      console.log('⚠️  SOME TESTS FAILED. Please check the configuration.')
    }
    
    return {
      success: overallSuccess,
      results: testResults,
      summary: {
        total: Object.keys(testResults).length,
        passed: Object.values(testResults).filter(r => r.success).length,
        failed: Object.values(testResults).filter(r => !r.success).length
      }
    }
  }
}

// Auto-run tests in development mode
if (import.meta.env.DEV) {
  console.log('🔧 Development mode detected - authentication tests available')
  console.log('Run authTests.runAllTests() in the console to test the auth system')
  
  // Make tests available globally in development
  window.authTests = authTests
}

export default authTests
