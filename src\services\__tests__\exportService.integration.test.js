/**
 * Integration tests for Export Service with TipTap editor
 * Tests the integration between exportService and docxGenerationService
 */

import { exportAsDocx, exportDocument } from '../exportService';

// Mock the docxGenerationService
jest.mock('../docxGenerationService', () => ({
    generateDocxWithImages: jest.fn().mockImplementation(async (documentData, content, contentType) => {
        // Return success with mock blob and image stats
        return {
            success: true,
            blob: new Blob(['mock-docx-content'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }),
            imageStats: {
                totalImages: 2,
                successfulImages: 2,
                failedImages: 0
            },
            userMessage: 'Document generated successfully with 2 images embedded'
        };
    }),
    downloadDocxFile: jest.fn()
}));

// Mock document data
const mockDocumentData = {
    title: 'Test Document',
    author: 'Test Author',
    description: 'Test Description'
};

// Mock generated content
const mockGeneratedContent = {
    chapters: [
        {
            id: 'chapter-1',
            number: 1,
            title: 'Chapter 1',
            content: '# Chapter 1\n\nThis is chapter content with an image.\n\n![Test Image](https://example.com/test-image.jpg)'
        },
        {
            id: 'chapter-2',
            number: 2,
            title: 'Chapter 2',
            content: '# Chapter 2\n\nThis is another chapter with an image.\n\n![Another Image](https://example.com/another-image.jpg)'
        }
    ]
};

// Mock TipTap editor instance
const createMockEditor = (htmlContent) => ({
    getHTML: jest.fn().mockReturnValue(htmlContent)
});

describe('Export Service Integration with TipTap Editor', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    describe('exportAsDocx function', () => {
        test('should extract HTML content from TipTap editor when provided', async () => {
            // Create mock editor with HTML content
            const mockHtmlContent = '<h1>Chapter 1</h1><p>This is chapter content with an image.</p><img src="https://example.com/test-image.jpg" alt="Test Image" />';
            const mockEditor = createMockEditor(mockHtmlContent);

            // Import the mocked functions
            const { generateDocxWithImages, downloadDocxFile } = require('../docxGenerationService');

            // Call exportAsDocx with the mock editor
            const result = await exportAsDocx(mockDocumentData, mockGeneratedContent, mockEditor);

            // Verify the editor's getHTML method was called
            expect(mockEditor.getHTML).toHaveBeenCalled();

            // Verify generateDocxWithImages was called with the correct parameters
            expect(generateDocxWithImages).toHaveBeenCalledWith(
                mockDocumentData,
                mockHtmlContent,
                'html'
            );

            // Verify downloadDocxFile was called with the correct parameters
            expect(downloadDocxFile).toHaveBeenCalledWith(
                expect.any(Blob),
                'Test Document.docx'
            );

            // Verify the result contains the expected properties
            expect(result).toEqual({
                success: true,
                message: expect.stringContaining('DOCX export completed successfully'),
                imageStats: {
                    totalImages: 2,
                    successfulImages: 2,
                    failedImages: 0
                },
                userMessage: 'Document generated successfully with 2 images embedded'
            });
        });

        test('should fall back to markdown content when no editor is provided', async () => {
            // Import the mocked functions
            const { generateDocxWithImages, downloadDocxFile } = require('../docxGenerationService');

            // Call exportAsDocx without an editor
            const result = await exportAsDocx(mockDocumentData, mockGeneratedContent);

            // Verify generateDocxWithImages was called with markdown content
            expect(generateDocxWithImages).toHaveBeenCalledWith(
                mockDocumentData,
                expect.stringContaining('# Chapter 1'),
                'markdown'
            );

            // Verify downloadDocxFile was called
            expect(downloadDocxFile).toHaveBeenCalled();

            // Verify the result contains the expected properties
            expect(result.success).toBe(true);
        });

        test('should handle errors gracefully', async () => {
            // Mock generateDocxWithImages to throw an error
            const { generateDocxWithImages } = require('../docxGenerationService');
            generateDocxWithImages.mockImplementationOnce(() => {
                throw new Error('Test error');
            });

            // Call exportAsDocx
            const result = await exportAsDocx(mockDocumentData, mockGeneratedContent);

            // Verify the result contains the error
            expect(result).toEqual({
                success: false,
                error: 'Test error',
                userMessage: 'An unexpected error occurred during DOCX export. Please try again.'
            });
        });

        test('should handle failed DOCX generation', async () => {
            // Mock generateDocxWithImages to return failure
            const { generateDocxWithImages } = require('../docxGenerationService');
            generateDocxWithImages.mockImplementationOnce(async () => ({
                success: false,
                error: 'Failed to generate DOCX',
                userMessage: 'Could not generate the document due to an error'
            }));

            // Call exportAsDocx
            const result = await exportAsDocx(mockDocumentData, mockGeneratedContent);

            // Verify the result contains the error
            expect(result).toEqual({
                success: false,
                error: 'Failed to generate DOCX',
                userMessage: 'Could not generate the document due to an error'
            });
        });
    });

    describe('exportDocument function', () => {
        test('should pass editor instance to exportAsDocx', async () => {
            // Create mock editor
            const mockHtmlContent = '<h1>Document Title</h1><p>Content</p>';
            const mockEditor = createMockEditor(mockHtmlContent);

            // Import the mocked functions
            const { generateDocxWithImages } = require('../docxGenerationService');

            // Call exportDocument with the mock editor
            const result = await exportDocument('docx', mockDocumentData, mockGeneratedContent, { editorInstance: mockEditor });

            // Verify generateDocxWithImages was called with HTML content
            expect(generateDocxWithImages).toHaveBeenCalledWith(
                mockDocumentData,
                mockHtmlContent,
                'html'
            );

            // Verify the result is successful
            expect(result.success).toBe(true);
        });

        test('should handle missing document data', async () => {
            // Call exportDocument with missing data
            const result = await exportDocument('docx', null, mockGeneratedContent);

            // Verify the result contains the error
            expect(result).toEqual({
                success: false,
                error: 'Missing document data or content',
                userMessage: 'Unable to export: missing document data or content'
            });
        });

        test('should handle unsupported export format', async () => {
            // Call exportDocument with unsupported format
            const result = await exportDocument('unsupported', mockDocumentData, mockGeneratedContent);

            // Verify the result contains the error
            expect(result).toEqual({
                success: false,
                error: 'Unsupported export format: unsupported',
                userMessage: 'Export format "unsupported" is not supported'
            });
        });
    });
});