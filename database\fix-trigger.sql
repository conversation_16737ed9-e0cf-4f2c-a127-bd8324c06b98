-- Improved trigger function with better error handling and logging
-- Run this in your Supabase SQL Editor to fix the trigger

-- 1. Drop existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- 2. <PERSON><PERSON> improved trigger function with error handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_name TEXT;
BEGIN
    -- Log the trigger execution (for debugging)
    RAISE LOG 'handle_new_user trigger fired for user ID: %', NEW.id;
    
    -- Extract email (required)
    user_email := NEW.email;
    
    -- Extract full name from metadata or use email as fallback
    user_name := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );
    
    -- Log what we're about to insert
    RAISE LOG 'Creating profile for user: email=%, name=%', user_email, user_name;
    
    -- Insert the user profile
    INSERT INTO public.user_profiles (
        id, 
        email, 
        full_name,
        user_type,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        user_email,
        user_name,
        COALESCE(NEW.raw_user_meta_data->>'user_type', 'student'),
        NOW(),
        NOW()
    );
    
    RAISE LOG 'Successfully created profile for user: %', NEW.id;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE LOG 'Error creating user profile for %: % %', NEW.id, SQLERRM, SQLSTATE;
        -- Still return NEW so the user creation doesn't fail
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW 
    EXECUTE FUNCTION public.handle_new_user();

-- 4. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.user_profiles TO authenticated;

-- 5. Update RLS policies to be more permissive for profile creation
DROP POLICY IF EXISTS "Users can insert own profile" ON public.user_profiles;

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT 
    WITH CHECK (
        auth.uid() = id OR 
        auth.role() = 'service_role'
    );

-- 6. Test query to verify setup
SELECT 
    'Trigger function updated successfully' as status,
    COUNT(*) as existing_profiles
FROM public.user_profiles;

-- 7. Enable logging for debugging (optional)
-- This will help us see what's happening in the logs
ALTER SYSTEM SET log_statement = 'all';
SELECT pg_reload_conf();
