/**
 * AI Service for DocForge AI
 * Handles all AI-powered content generation using Google Gemini API
 *
 * Features:
 * - Sub-niche generation based on topics
 * - Document title generation
 * - Document outline creation
 * - Full content generation
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { createDocumentTypePrompt } from './documentTypePrompts.js';
import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor';

/**
 * Convert JSON content to markdown format as fallback
 * Handles cases where AI returns unexpected JSON instead of markdown
 * @param {string} content - Content that might be JSON
 * @returns {string} Markdown formatted content
 */
const convertJsonToMarkdown = (content) => {
  try {
    // Try to parse as JSON
    const jsonData = JSON.parse(content);

    // Handle different JSON structures that might be returned
    if (jsonData.chapterTitle && jsonData.sections) {
      // Handle chapter-like JSON structure
      let markdown = `# ${jsonData.chapterTitle}\n\n`;

      if (Array.isArray(jsonData.sections)) {
        jsonData.sections.forEach(section => {
          if (typeof section === 'string') {
            markdown += `${section}\n\n`;
          } else if (section.title && section.content) {
            markdown += `## ${section.title}\n\n${section.content}\n\n`;
          }
        });
      }

      return markdown;
    }

    // Handle simple content object
    if (jsonData.content) {
      return jsonData.content;
    }

    // Handle document structure with title and content arrays (common academic paper format)
    if (jsonData.title && Array.isArray(jsonData.introduction)) {
      let markdown = `# ${jsonData.title}\n\n`;

      // Process introduction array
      if (jsonData.introduction) {
        jsonData.introduction.forEach(item => {
          if (item.type === 'paragraph' && item.content) {
            markdown += `${item.content}\n\n`;
          } else if (item.type === 'heading' && item.content) {
            const level = item.level || 2;
            const hashes = '#'.repeat(level);
            markdown += `${hashes} ${item.content}\n\n`;
          }
        });
      }

      return markdown;
    }

    // Handle array of content items (common AI response format)
    if (Array.isArray(jsonData)) {
      return jsonData.map(item => {
        if (typeof item === 'string') return item;
        if (item.type === 'paragraph' && item.content) return item.content;
        if (item.type === 'heading' && item.content) {
          const level = item.level || 2;
          const hashes = '#'.repeat(level);
          return `${hashes} ${item.content}`;
        }
        if (item.content) return item.content;
        if (item.text) return item.text;
        return JSON.stringify(item);
      }).join('\n\n');
    }

    // Handle document structure with title and content arrays
    if (jsonData.title && Array.isArray(jsonData.introduction)) {
      let markdown = `# ${jsonData.title}\n\n`;

      // Process introduction array
      if (jsonData.introduction) {
        jsonData.introduction.forEach(item => {
          if (item.type === 'paragraph' && item.content) {
            markdown += `${item.content}\n\n`;
          } else if (item.type === 'heading' && item.content) {
            const level = item.level || 2;
            const hashes = '#'.repeat(level);
            markdown += `${hashes} ${item.content}\n\n`;
          }
        });
      }

      return markdown;
    }

    // Handle nested content structures
    if (typeof jsonData === 'object') {
      let markdown = '';

      // Check for title
      if (jsonData.title) {
        markdown += `# ${jsonData.title}\n\n`;
      }

      // Process each property that might contain content
      Object.entries(jsonData).forEach(([key, value]) => {
        if (key === 'title') return; // Already handled

        if (Array.isArray(value)) {
          // Handle arrays of content items
          value.forEach(item => {
            if (typeof item === 'string') {
              markdown += `${item}\n\n`;
            } else if (item.type === 'paragraph' && item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.type === 'heading' && item.content) {
              const level = item.level || 2;
              const hashes = '#'.repeat(level);
              markdown += `${hashes} ${item.content}\n\n`;
            } else if (item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.text) {
              markdown += `${item.text}\n\n`;
            }
          });
        } else if (typeof value === 'string') {
          // Handle string values
          markdown += `${value}\n\n`;
        }
      });

      return markdown || 'No content available';
    }

    // Handle nested content structures (fallback for complex JSON)
    if (typeof jsonData === 'object') {
      let markdown = '';

      // Check for title
      if (jsonData.title) {
        markdown += `# ${jsonData.title}\n\n`;
      }

      // Process each property that might contain content
      Object.entries(jsonData).forEach(([key, value]) => {
        if (key === 'title') return; // Already handled

        if (Array.isArray(value)) {
          // Handle arrays of content items
          value.forEach(item => {
            if (typeof item === 'string') {
              markdown += `${item}\n\n`;
            } else if (item.type === 'paragraph' && item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.type === 'heading' && item.content) {
              const level = item.level || 2;
              const hashes = '#'.repeat(level);
              markdown += `${hashes} ${item.content}\n\n`;
            } else if (item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.text) {
              markdown += `${item.text}\n\n`;
            }
          });
        } else if (typeof value === 'string') {
          // Handle string values
          markdown += `${value}\n\n`;
        }
      });

      return markdown || 'No content available';
    }

    // If it's a valid JSON but unknown structure, return as formatted text
    return Object.entries(jsonData)
      .map(([key, value]) => `**${key}:** ${value}`)
      .join('\n\n');

  } catch (error) {
    // Not valid JSON, return original content
    return content;
  }
};

/**
 * Process AI response content to ensure it's in markdown format
 * @param {string} content - Raw AI response content
 * @returns {string} Processed markdown content
 */
const processAIContent = (content) => {
  if (!content) return '';

  // Remove any JSON code block markers if present
  const cleanContent = content.replace(/^```json\s*/, '').replace(/\s*```$/, '');

  // Check if content looks like JSON (starts with { or [)
  const trimmedContent = cleanContent.trim();
  if (trimmedContent.startsWith('{') || trimmedContent.startsWith('[')) {
    return convertJsonToMarkdown(trimmedContent);
  }

  // Content is already in markdown format
  return cleanContent;
};

// Environment variables for AI API keys
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
const ANTHROPIC_API_KEY = import.meta.env.VITE_ANTHROPIC_API_KEY;

// Create a logger for the AI service
const aiLogger = errorMonitor.createContextLogger('AIService');

// Initialize Gemini AI
let genAI = null;
let model = null;

if (GEMINI_API_KEY) {
  try {
    aiLogger.info('Initializing Gemini AI');
    genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    aiLogger.info('Gemini AI initialized successfully');
  } catch (error) {
    aiLogger.error(error, {
      action: 'initialize_gemini',
      message: 'Failed to initialize Gemini AI'
    });
  }
} else {
  aiLogger.warn('Gemini API key not found, AI features will use mock data', {
    source: 'initialization'
  });
}

/**
 * Generate relevant sub-niches based on a main topic using Gemini AI
 * @param {string} topic - The main topic entered by the user
 * @param {string} language - The target language for content
 * @returns {Promise<Array>} Array of sub-niche objects
 */
export const generateSubNiches = async (topic, language = 'english') => {
  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      const prompt = `You are an expert content strategist and niche specialist. Generate 8 highly relevant and specific sub-niches for the topic "${topic}".

Requirements:
- Each sub-niche should be specific and actionable
- Focus on areas that would make excellent content topics
- Consider different skill levels and approaches
- Make them appealing to content creators and audiences
- Ensure they are distinct from each other

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "wellness-coaching")
- name: Clear, descriptive name (e.g., "Wellness and Health Coaching")
- description: Brief explanation of what this sub-niche covers (max 60 characters)

Topic: ${topic}
Language: ${language}

JSON Response:`;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract JSON from the response
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const subNiches = JSON.parse(jsonMatch[0]);

        // Validate the structure
        if (Array.isArray(subNiches) && subNiches.length > 0) {
          return subNiches.map(niche => ({
            id: niche.id || niche.name?.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
            name: niche.name || 'Untitled Sub-niche',
            description: niche.description || 'No description available'
          }));
        }
      }

      // If parsing fails, fall back to mock data
      console.warn('Failed to parse Gemini response, falling back to mock data');
      return await mockGenerateSubNiches(topic, language);
    }

    // Fallback to mock implementation if Gemini is not available
    console.warn('Gemini AI not configured, using mock data');
    return await mockGenerateSubNiches(topic, language);

  } catch (error) {
    console.error('Error generating sub-niches with Gemini:', error);

    // Fallback to mock data on error
    try {
      return await mockGenerateSubNiches(topic, language);
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
      throw new Error('Failed to generate sub-niches');
    }
  }
};

/**
 * Generate document titles based on topic, audience, and sub-niches using Gemini AI
 * Enhanced to handle imported content from URLs
 * @param {Object} params - Parameters for title generation
 * @returns {Promise<Array>} Array of title objects
 */
export const generateTitles = async ({ topic, audience, subNiches = [], language = 'english', documentData = null }) => {
  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      let prompt;

      // Check if we have imported content to enhance title generation
      const hasImportedContent = documentData?.documentPurpose?.importedContent?.extractedContent;

      // Use document type-specific prompt if documentData is provided
      if (documentData) {
        prompt = createDocumentTypePrompt(documentData, 'title');

        // Enhance prompt with imported content context if available
        if (hasImportedContent) {
          const importedContent = documentData.documentPurpose.importedContent;
          prompt += `\n\nIMPORTED CONTENT CONTEXT:
Original Title: "${importedContent.originalTitle}"
Content Preview: "${importedContent.extractedContent.substring(0, 500)}..."
Word Count: ${importedContent.wordCount}

Please generate titles that improve upon or provide alternatives to the original title,
taking into account the actual content and making them more engaging and specific to the target audience.`;
        }
      } else {
        // Fallback to generic title prompt for backward compatibility
        const subNicheText = subNiches.length > 0
          ? `\nSelected sub-niches: ${subNiches.map(s => s.name || s).join(', ')}`
          : '';

        prompt = `You are an expert copywriter and content strategist. Generate 8 compelling document titles for the following specifications:

Topic: ${topic}
Target Audience: ${audience}${subNicheText}
Language: ${language}

Requirements:
- Create titles that are engaging and click-worthy
- Vary the styles: some descriptive, some catchy, some professional
- Make them specific to the target audience
- Include power words that drive engagement
- Ensure they clearly communicate value
- Length should be 40-80 characters for optimal readability

Return ONLY a valid JSON array with objects containing:
- id: unique identifier (e.g., "title-1")
- text: the actual title text
- style: one of "descriptive", "catchy", "professional", "inspirational", "academic"

JSON Response:`;
      }

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract JSON from the response
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const titles = JSON.parse(jsonMatch[0]);

        // Validate the structure
        if (Array.isArray(titles) && titles.length > 0) {
          return titles.map((title, index) => ({
            id: title.id || `title-${index + 1}`,
            text: title.text || title.title || 'Untitled Document',
            style: title.style || 'professional'
          }));
        }
      }

      // If parsing fails, fall back to mock data
      console.warn('Failed to parse Gemini titles response, falling back to mock data');
      return await mockGenerateTitles({ topic, audience, subNiches, language });
    }

    // Fallback to mock implementation if Gemini is not available
    console.warn('Gemini AI not configured, using mock titles');
    return await mockGenerateTitles({ topic, audience, subNiches, language });

  } catch (error) {
    console.error('Error generating titles with Gemini:', error);

    // Fallback to mock data on error
    try {
      return await mockGenerateTitles({ topic, audience, subNiches, language });
    } catch (fallbackError) {
      console.error('Title fallback also failed:', fallbackError);
      throw new Error('Failed to generate titles');
    }
  }
};

/**
 * Generate document outline based on all collected parameters using Gemini AI
 * Enhanced to incorporate imported content structure
 * @param {Object} documentData - Complete document configuration
 * @returns {Promise<Object>} Document outline object
 */
export const generateDocumentOutline = async (documentData) => {
  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      // Use new document type-specific prompt system
      let prompt = createDocumentTypePrompt(documentData, 'outline');

      // Check if we have imported content to enhance outline generation
      const hasImportedContent = documentData?.documentPurpose?.importedContent?.extractedContent;

      if (hasImportedContent) {
        const importedContent = documentData.documentPurpose.importedContent;
        prompt += `\n\nIMPORTED CONTENT TO ENHANCE:
Original Title: "${importedContent.originalTitle}"
Source: ${importedContent.sourceUrl}
Content Length: ${importedContent.wordCount} words

Content to Structure and Improve:
"${importedContent.extractedContent}"

Please create an outline that:
1. Improves upon the structure of the imported content
2. Fills in any gaps or missing sections
3. Reorganizes content for better flow and readability
4. Adds relevant sections that would enhance the document
5. Maintains the core message and value of the original content
6. Adapts the structure to the target audience and document type`;
      }

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const outline = JSON.parse(jsonMatch[0]);

        // Validate the structure
        if (outline.chapters && Array.isArray(outline.chapters)) {
          return {
            title: outline.title || title,
            chapters: outline.chapters.map((chapter, index) => ({
              number: chapter.number || index + 1,
              title: chapter.title || `Chapter ${index + 1}`,
              sections: Array.isArray(chapter.sections) ? chapter.sections : []
            }))
          };
        }
      }

      // If parsing fails, fall back to mock data
      console.warn('Failed to parse Gemini outline response, falling back to mock data');
      return await mockGenerateOutline(documentData);
    }

    // Fallback to mock implementation if Gemini is not available
    console.warn('Gemini AI not configured, using mock outline');
    return await mockGenerateOutline(documentData);

  } catch (error) {
    console.error('Error generating outline with Gemini:', error);

    // Fallback to mock data on error
    try {
      return await mockGenerateOutline(documentData);
    } catch (fallbackError) {
      console.error('Outline fallback also failed:', fallbackError);
      throw new Error('Failed to generate document outline');
    }
  }
};

/**
 * Generate full document content using Gemini AI with chapter-by-chapter structure
 * Enhanced to improve and expand imported content
 * @param {Object} documentData - Complete document configuration
 * @returns {Promise<Object>} Generated document content with structured chapters
 */
export const generateDocumentContent = async (documentData) => {
  const startTime = performance.now();
  const documentId = documentData?.id || 'unknown';

  aiLogger.info('Starting document content generation', {
    documentId,
    documentType: documentData.documentPurpose?.primaryType || 'unknown',
    hasOutline: !!documentData.documentOutline?.generatedOutline,
    hasImportedContent: !!documentData.documentPurpose?.importedContent?.extractedContent,
    timestamp: new Date().toISOString()
  });

  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      aiLogger.debug('Using Gemini AI for content generation', {
        documentId,
        modelName: "gemini-1.5-flash",
        apiKeyPresent: !!GEMINI_API_KEY
      });

      const topic = documentData.topicAndNiche?.mainTopic || 'General Topic';
      const audience = documentData.audienceAnalysis?.primaryAudience || 'General Audience';

      // Check if we have imported content to enhance
      const hasImportedContent = documentData?.documentPurpose?.importedContent?.extractedContent;
      let contentEnhancementContext = '';

      if (hasImportedContent) {
        const importedContent = documentData.documentPurpose.importedContent;
        aiLogger.debug('Processing imported content for enhancement', {
          documentId,
          sourceUrl: importedContent.sourceUrl,
          originalTitle: importedContent.originalTitle,
          wordCount: importedContent.wordCount
        });

        contentEnhancementContext = `\n\nIMPORTED CONTENT ENHANCEMENT:
This document is based on imported content from: ${importedContent.sourceUrl}
Original Title: "${importedContent.originalTitle}"
Original Length: ${importedContent.wordCount} words

ORIGINAL CONTENT TO ENHANCE:
"${importedContent.extractedContent}"

ENHANCEMENT GUIDELINES:
- Use the imported content as foundation but significantly expand and improve it
- Maintain core insights while adding substantial new value
- Restructure according to the outline for better flow
- Add examples, case studies, and practical applications
- Improve clarity, engagement, and actionability
- Ensure final content is 2-3x more comprehensive than original`;
      }

      const title = documentData.titleSelection?.selectedTitle || `Guide to ${topic}`;
      const subNiches = documentData.topicAndNiche?.subNiches || [];
      const tone = documentData.toneAndVoice?.toneOfVoice || 'informative';
      const documentType = documentData.documentPurpose?.primaryType || 'ebook';
      const outline = documentData.documentOutline?.generatedOutline;

      if (!outline?.chapters) {
        aiLogger.error(new Error('Missing document outline'), {
          documentId,
          action: 'content_generation',
          issue: 'missing_outline'
        });
        throw new Error('Document outline is required for content generation');
      }

      const subNicheText = subNiches.length > 0
        ? `\nFocus areas: ${subNiches.map(s => s.name || s).join(', ')}`
        : '';

      // Generate content chapter by chapter for better structure
      const chapters = [];
      const chapterCount = outline.chapters.length;

      aiLogger.info('Beginning chapter-by-chapter generation', {
        documentId,
        chapterCount,
        documentType,
        tone
      });

      for (let i = 0; i < outline.chapters.length; i++) {
        const chapterOutline = outline.chapters[i];
        aiLogger.debug(`Generating chapter ${i + 1} of ${chapterCount}`, {
          documentId,
          chapterNumber: chapterOutline.number,
          chapterTitle: chapterOutline.title,
          sectionCount: chapterOutline.sections?.length || 0
        });

        const chapterStartTime = performance.now();

        try {
          const chapterContent = await generateChapterContent({
            chapterOutline,
            documentData,
            topic,
            audience,
            tone,
            subNiches,
            contentEnhancementContext: hasImportedContent ? contentEnhancementContext : null
          });

          const chapterDuration = performance.now() - chapterStartTime;

          aiLogger.debug(`Chapter ${i + 1} generated successfully`, {
            documentId,
            chapterNumber: chapterOutline.number,
            wordCount: chapterContent.wordCount,
            durationMs: Math.round(chapterDuration)
          });

          chapters.push(chapterContent);
        } catch (chapterError) {
          aiLogger.error(chapterError, {
            documentId,
            action: 'chapter_generation',
            chapterNumber: chapterOutline.number,
            chapterTitle: chapterOutline.title
          });

          // Continue with other chapters despite error
          chapters.push({
            id: `chapter-${chapterOutline.number}`,
            number: chapterOutline.number,
            title: chapterOutline.title,
            content: `[Error generating content for this chapter: ${chapterError.message}]`,
            sections: chapterOutline.sections || [],
            wordCount: 0,
            estimatedReadingTime: 0,
            error: true
          });
        }
      }

      // Generate introduction and conclusion
      aiLogger.debug('Generating introduction', { documentId });
      const introduction = await generateIntroduction(documentData);

      aiLogger.debug('Generating conclusion', { documentId });
      const conclusion = await generateConclusion(documentData);

      // Calculate metrics
      const totalWordCount = chapters.reduce((total, chapter) => total + chapter.wordCount, 0) +
        introduction.wordCount + conclusion.wordCount;
      const estimatedReadingTime = Math.ceil(totalWordCount / 200);
      const totalDuration = performance.now() - startTime;

      aiLogger.info('Document content generation completed successfully', {
        documentId,
        totalWordCount,
        chapterCount: chapters.length,
        estimatedReadingTime: `${estimatedReadingTime} minutes`,
        durationMs: Math.round(totalDuration),
        errorChapters: chapters.filter(ch => ch.error).length
      });

      return {
        success: true,
        content: {
          title: title,
          introduction: introduction,
          chapters: chapters,
          conclusion: conclusion,
          wordCount: totalWordCount,
          estimatedReadingTime: `${estimatedReadingTime} minutes`,
          metadata: {
            topic,
            audience,
            tone,
            documentType,
            generatedAt: new Date().toISOString()
          }
        }
      };
    }

    // Fallback to mock implementation if Gemini is not available
    aiLogger.warn('Gemini AI not configured, using mock content', {
      documentId,
      reason: 'api_key_missing'
    });
    return await mockGenerateContent(documentData);

  } catch (error) {
    const duration = performance.now() - startTime;

    aiLogger.error(error, {
      documentId,
      action: 'document_generation',
      durationMs: Math.round(duration),
      errorType: error.name,
      errorMessage: error.message
    });

    // Fallback to mock data on error
    try {
      aiLogger.info('Attempting fallback to mock content generation', {
        documentId,
        originalError: error.message
      });
      return await mockGenerateContent(documentData);
    } catch (fallbackError) {
      aiLogger.error(fallbackError, {
        documentId,
        action: 'mock_fallback',
        originalError: error.message,
        fallbackError: fallbackError.message
      });
      throw new Error('Failed to generate document content');
    }
  }
};

/**
 * Generate content for a specific chapter
 * @param {Object} params - Chapter generation parameters
 * @returns {Promise<Object>} Generated chapter content
 */
const generateChapterContent = async ({ chapterOutline, documentData, topic, audience, tone, subNiches, contentEnhancementContext = null }) => {
  try {
    if (!model || !GEMINI_API_KEY) {
      return mockGenerateChapter(chapterOutline);
    }

    const sectionsText = chapterOutline.sections?.join('\n- ') || '';

    // Use document type-specific chapter prompt
    let prompt = createDocumentTypePrompt(documentData, 'chapter', chapterOutline);

    // Add content enhancement context if available
    if (contentEnhancementContext) {
      prompt += contentEnhancementContext;
      prompt += `\n\nFor this specific chapter "${chapterOutline.title}", please:
1. Look for relevant content in the imported material
2. Expand and improve upon any related sections
3. Add new insights and examples where appropriate
4. Ensure the chapter flows well with the overall document structure`;
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const rawContent = response.text();

    // Process content to ensure markdown format
    const content = processAIContent(rawContent);

    const wordCount = content.split(/\s+/).length;

    return {
      id: `chapter-${chapterOutline.number}`,
      number: chapterOutline.number,
      title: chapterOutline.title,
      content: content,
      sections: chapterOutline.sections || [],
      wordCount: wordCount,
      estimatedReadingTime: Math.ceil(wordCount / 200)
    };

  } catch (error) {
    console.error(`Error generating chapter ${chapterOutline.number}:`, error);
    return mockGenerateChapter(chapterOutline);
  }
};

/**
 * Generate document introduction
 * @param {Object} documentData - Document configuration
 * @returns {Promise<Object>} Generated introduction
 */
const generateIntroduction = async (documentData) => {
  try {
    if (!model || !GEMINI_API_KEY) {
      return mockGenerateIntroduction(documentData);
    }

    const topic = documentData.topicAndNiche?.mainTopic;
    const audience = documentData.audienceAnalysis?.primaryAudience;
    const tone = documentData.toneAndVoice?.toneOfVoice;
    const title = documentData.titleSelection?.selectedTitle;

    // Use document type-specific introduction prompt
    const prompt = createDocumentTypePrompt(documentData, 'introduction');

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const rawContent = response.text();

    // Process content to ensure markdown format
    const content = processAIContent(rawContent);

    const wordCount = content.split(/\s+/).length;

    return {
      content: content,
      wordCount: wordCount
    };

  } catch (error) {
    console.error('Error generating introduction:', error);
    return mockGenerateIntroduction(documentData);
  }
};

/**
 * Generate document conclusion
 * @param {Object} documentData - Document configuration
 * @returns {Promise<Object>} Generated conclusion
 */
const generateConclusion = async (documentData) => {
  try {
    if (!model || !GEMINI_API_KEY) {
      return mockGenerateConclusion(documentData);
    }

    const topic = documentData.topicAndNiche?.mainTopic;
    const audience = documentData.audienceAnalysis?.primaryAudience;
    const tone = documentData.toneAndVoice?.toneOfVoice;

    const prompt = `Write a compelling conclusion for this document:

Topic: ${topic}
Audience: ${audience}
Tone: ${tone}

Requirements:
- 200-400 words
- Summarize key takeaways
- Provide actionable next steps
- End with motivation/inspiration
- Use ${tone} tone
- Leave ${audience} feeling empowered

Generate the conclusion:`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const rawContent = response.text();

    // Process content to ensure markdown format
    const content = processAIContent(rawContent);

    const wordCount = content.split(/\s+/).length;

    return {
      content: content,
      wordCount: wordCount
    };

  } catch (error) {
    console.error('Error generating conclusion:', error);
    return mockGenerateConclusion(documentData);
  }
};

// Mock implementations (to be replaced with real AI calls)

const mockGenerateSubNiches = async (topic, language) => {
  await new Promise(resolve => setTimeout(resolve, 1)); // Simulate API delay

  const topicLower = topic.toLowerCase();

  if (topicLower.includes('life') || topicLower.includes('coaching')) {
    return [
      { id: 'wellness-health', name: 'Wellness and Health Coaching', description: 'Focus on physical and mental wellness' },
      { id: 'career-transition', name: 'Career Transition Coaching', description: 'Professional development and career changes' },
      { id: 'relationship-coaching', name: 'Relationship Coaching', description: 'Personal relationships and communication' },
      { id: 'confidence-building', name: 'Confidence and Self-Esteem', description: 'Building self-confidence and self-worth' },
      { id: 'mindfulness-stress', name: 'Mindfulness and Stress Management', description: 'Stress reduction and mindfulness practices' },
      { id: 'goal-achievement', name: 'Goal Setting and Achievement', description: 'Strategic goal planning and execution' },
      { id: 'work-life-balance', name: 'Work-Life Balance', description: 'Balancing professional and personal life' },
      { id: 'leadership-development', name: 'Leadership Development', description: 'Developing leadership skills and presence' },
    ];
  }

  // Add more topic-specific generations...
  return [
    { id: 'fundamentals', name: `${topic} Fundamentals`, description: 'Core concepts and basics' },
    { id: 'practical-guide', name: `Practical ${topic} Guide`, description: 'Hands-on approach and implementation' },
    { id: 'advanced-techniques', name: `Advanced ${topic} Techniques`, description: 'Expert-level strategies' },
    { id: 'best-practices', name: `${topic} Best Practices`, description: 'Proven methods and recommendations' },
  ];
};

const mockGenerateTitles = async ({ topic, audience, subNiches, language }) => {
  await new Promise(resolve => setTimeout(resolve, 1)); // Simulate API delay

  const audienceCapitalized = audience.charAt(0).toUpperCase() + audience.slice(1);

  return [
    {
      id: 'title-1',
      text: `The Complete ${audienceCapitalized}'s Guide to ${topic}`,
      style: 'descriptive'
    },
    {
      id: 'title-2',
      text: `Mastering ${topic}: A ${audienceCapitalized}'s Journey`,
      style: 'professional'
    },
    {
      id: 'title-3',
      text: `${topic} Simplified: Essential Strategies for ${audienceCapitalized}`,
      style: 'catchy'
    },
    {
      id: 'title-4',
      text: `From Beginner to Expert: Your ${topic} Transformation`,
      style: 'inspirational'
    },
    {
      id: 'title-5',
      text: `The ${audienceCapitalized}'s Handbook to ${topic} Success`,
      style: 'professional'
    }
  ];
};

const mockGenerateOutline = async (documentData) => {
  await new Promise(resolve => setTimeout(resolve, 1)); // Simulate API delay

  const topic = documentData.topicAndNiche?.mainTopic || 'Your Topic';

  return {
    title: documentData.titleSelection?.selectedTitle || `Guide to ${topic}`,
    chapters: [
      {
        number: 1,
        title: `Understanding ${topic}`,
        sections: [
          `What is ${topic}?`,
          'Why it matters',
          'Common misconceptions',
          'Getting started'
        ]
      },
      {
        number: 2,
        title: 'Fundamentals and Core Concepts',
        sections: [
          'Key principles',
          'Essential terminology',
          'Building blocks',
          'Foundation strategies'
        ]
      },
      {
        number: 3,
        title: 'Practical Implementation',
        sections: [
          'Step-by-step approach',
          'Tools and resources',
          'Real-world examples',
          'Common challenges'
        ]
      },
      {
        number: 4,
        title: 'Advanced Strategies',
        sections: [
          'Expert techniques',
          'Optimization methods',
          'Scaling approaches',
          'Long-term success'
        ]
      }
    ]
  };
};

const mockGenerateContent = async (documentData) => {
  await new Promise(resolve => setTimeout(resolve, 1)); // Simulate longer API delay

  const topic = documentData.topicAndNiche?.mainTopic || 'Your Topic';
  const outline = documentData.documentOutline?.generatedOutline;

  // Generate mock chapters based on outline
  const chapters = outline?.chapters?.map(chapterOutline =>
    mockGenerateChapter(chapterOutline)
  ) || [];

  const introduction = mockGenerateIntroduction(documentData);
  const conclusion = mockGenerateConclusion(documentData);

  const totalWordCount = chapters.reduce((total, chapter) => total + chapter.wordCount, 0) +
    introduction.wordCount + conclusion.wordCount;

  return {
    success: true,
    content: {
      title: documentData.titleSelection?.selectedTitle || `Guide to ${topic}`,
      introduction: introduction,
      chapters: chapters,
      conclusion: conclusion,
      wordCount: totalWordCount,
      estimatedReadingTime: `${Math.ceil(totalWordCount / 200)} minutes`,
      metadata: {
        topic: topic,
        audience: documentData.audienceAnalysis?.primaryAudience || 'General Audience',
        tone: documentData.toneAndVoice?.toneOfVoice || 'informative',
        documentType: documentData.documentPurpose?.primaryType || 'ebook',
        generatedAt: new Date().toISOString()
      }
    }
  };
};

// Mock chapter generation
const mockGenerateChapter = (chapterOutline) => {
  const mockContent = `# Chapter ${chapterOutline.number}: ${chapterOutline.title}

${chapterOutline.sections?.map(section => `## ${section}

This section covers important aspects of ${section.toLowerCase()}. Here you'll learn practical strategies and actionable insights that you can apply immediately.

### Key Points

- Important concept related to ${section.toLowerCase()}
- Practical application you can implement today
- Common mistakes to avoid
- Best practices from industry experts

### Real-World Example

Consider this scenario: [Detailed example that illustrates the concepts in action, showing how the principles apply in real situations.]

### Action Steps

1. Start by implementing the basic framework
2. Apply the concepts to your specific situation
3. Monitor your progress and adjust as needed
4. Scale your approach based on results

`).join('\n\n')}

## Chapter Summary

This chapter has provided you with a comprehensive understanding of ${chapterOutline.title.toLowerCase()}. The key takeaways include practical strategies, real-world applications, and actionable steps you can implement immediately.`;

  return {
    id: `chapter-${chapterOutline.number}`,
    number: chapterOutline.number,
    title: chapterOutline.title,
    content: mockContent,
    sections: chapterOutline.sections || [],
    wordCount: mockContent.split(/\s+/).length,
    estimatedReadingTime: Math.ceil(mockContent.split(/\s+/).length / 200)
  };
};

// Mock introduction generation
const mockGenerateIntroduction = (documentData) => {
  const topic = documentData.topicAndNiche?.mainTopic || 'this important topic';
  const audience = documentData.audienceAnalysis?.primaryAudience || 'readers';

  const content = `# Introduction

Welcome to this comprehensive guide on ${topic}. Whether you're a beginner just starting your journey or someone looking to deepen your understanding, this document has been carefully crafted with ${audience} like you in mind.

In today's fast-paced world, mastering ${topic} has become more important than ever. This guide will take you through a step-by-step journey, providing you with practical insights, real-world examples, and actionable strategies that you can implement immediately.

## What You'll Learn

Throughout this document, you'll discover:

- Fundamental concepts that form the foundation of ${topic}
- Practical strategies you can apply in real-world situations
- Common pitfalls to avoid and how to overcome challenges
- Advanced techniques used by experts in the field
- Step-by-step action plans for immediate implementation

## How to Use This Guide

This guide is designed to be both comprehensive and practical. Each chapter builds upon the previous one, creating a logical progression that will take you from understanding the basics to mastering advanced concepts.

Take your time with each section, and don't hesitate to revisit chapters as needed. The real value comes from applying what you learn, so be sure to implement the strategies and techniques as you progress through the material.

Let's begin this exciting journey together!`;

  return {
    content: content,
    wordCount: content.split(/\s+/).length
  };
};

// Mock conclusion generation
const mockGenerateConclusion = (documentData) => {
  const topic = documentData.topicAndNiche?.mainTopic || 'this topic';

  const content = `# Conclusion

Congratulations! You've completed this comprehensive journey through ${topic}. By now, you should have a solid understanding of the key concepts, practical strategies, and actionable techniques that will help you succeed.

## Key Takeaways

As you move forward, remember these essential points:

- Consistency is key to long-term success
- Start with small, manageable steps and build momentum
- Don't be afraid to experiment and adapt strategies to your unique situation
- Continuous learning and improvement are essential for mastery

## Your Next Steps

Now that you have the knowledge, it's time to take action:

1. **Start Today**: Choose one strategy from this guide and implement it immediately
2. **Track Your Progress**: Monitor your results and adjust your approach as needed
3. **Stay Connected**: Continue learning and stay updated with the latest developments
4. **Share Your Success**: Help others by sharing what you've learned

## Final Thoughts

Remember, knowledge without action is just information. The real transformation happens when you apply what you've learned consistently over time. You now have the tools and strategies you need to succeed.

The journey doesn't end here—it's just the beginning. Take what you've learned, apply it with confidence, and watch as you achieve the results you've been working toward.

Your success story starts now!`;

  return {
    content: content,
    wordCount: content.split(/\s+/).length
  };
};

// Utility function to check if AI services are configured
export const isAIConfigured = () => {
  return !!(GEMINI_API_KEY || OPENAI_API_KEY || ANTHROPIC_API_KEY);
};

// Get available AI providers
export const getAvailableProviders = () => {
  const providers = [];
  if (GEMINI_API_KEY) providers.push('gemini');
  if (OPENAI_API_KEY) providers.push('openai');
  if (ANTHROPIC_API_KEY) providers.push('anthropic');
  return providers;
};

// Check if Gemini is specifically configured and working
export const isGeminiConfigured = () => {
  return !!(GEMINI_API_KEY && model);
};

// Test Gemini connection
export const testGeminiConnection = async () => {
  try {
    if (!model || !GEMINI_API_KEY) {
      return { success: false, error: 'Gemini not configured' };
    }

    const result = await model.generateContent('Test connection. Respond with "OK".');
    const response = await result.response;
    const text = response.text();

    return {
      success: true,
      message: 'Gemini connection successful',
      response: text
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};