import React, { useEffect } from 'react';
import CustomDropdown from '../CustomDropdown';
import MultiSelectCard from '../questionnaire/MultiSelectCard';
import { toneAndVoiceOptions, themeConfigurations } from '../../utils/questionnaireDataStructure';

/**
 * ToneVoiceStep - Designrr-style tone and voice selection
 * Includes tone dropdown, theme selection, and popular themes
 */
const ToneVoiceStep = ({ 
  formData, 
  onInputChange, 
  onValidationChange,
  className = '' 
}) => {

  // Convert tone options for dropdown
  const toneOptions = toneAndVoiceOptions.toneOfVoice.map(option => ({
    id: option.id,
    name: option.name,
    description: option.description
  }));

  // Convert theme configurations for display
  const themeOptions = Object.entries(themeConfigurations).map(([key, theme]) => ({
    id: key,
    name: theme.name,
    description: `Popular: ${theme.popularTopics.slice(0, 2).join(', ')}`,
    icon: theme.icon,
    color: theme.color
  }));

  // Popular themes for quick selection
  const popularThemes = [
    { id: 'self-development', name: 'Self Development', icon: '🌱' },
    { id: 'education', name: 'Education', icon: '📚' },
    { id: 'health-wellness', name: 'Health & wellness', icon: '🏃‍♂️' },
    { id: 'business', name: 'Business', icon: '💼' },
    { id: 'digital-marketing', name: 'Digital Marketing', icon: '📱' },
    { id: 'life-coaching', name: 'Life coaching', icon: '🎯' },
    { id: 'spiritual-self-development', name: 'Spiritual Self Development', icon: '🧘‍♀️' },
    { id: 'training-development', name: 'Training and Development', icon: '🎓' },
    { id: 'business-development-sales', name: 'Business Development / Sales', icon: '📈' },
    { id: 'writing-non-fiction', name: 'Writing Non-Fiction', icon: '✍️' },
    { id: 'author', name: 'Author', icon: '📖' },
    { id: 'other', name: 'Other', icon: '🔧' },
    { id: 'e-commerce', name: 'E-Commerce', icon: '🛒' },
    { id: 'blogging', name: 'Blogging', icon: '📝' },
    { id: 'advertising', name: 'Advertising', icon: '📢' },
  ];

  const handleToneChange = (toneId) => {
    onInputChange('toneAndVoice.toneOfVoice', toneId);
    
    // Set default writing style based on tone
    const toneOption = toneAndVoiceOptions.toneOfVoice.find(t => t.id === toneId);
    if (toneOption) {
      // Set some intelligent defaults
      if (toneId === 'academic' || toneId === 'analytical') {
        onInputChange('toneAndVoice.writingStyle', 'academic');
        onInputChange('toneAndVoice.formalityLevel', 'formal');
      } else if (toneId === 'conversational' || toneId === 'empathetic') {
        onInputChange('toneAndVoice.writingStyle', 'conversational');
        onInputChange('toneAndVoice.formalityLevel', 'casual');
      } else {
        onInputChange('toneAndVoice.writingStyle', 'professional');
        onInputChange('toneAndVoice.formalityLevel', 'semi-formal');
      }
    }
  };

  const handleThemeChange = (themeId) => {
    onInputChange('toneAndVoice.selectedTheme', themeId);
    
    // Update topic if not already set
    if (!formData.topicAndNiche?.mainTopic && themeConfigurations[themeId]) {
      const theme = themeConfigurations[themeId];
      onInputChange('topicAndNiche.mainTopic', theme.name);
    }
  };

  // Validation
  useEffect(() => {
    const isValid = formData.toneAndVoice?.toneOfVoice &&
                   formData.toneAndVoice?.selectedTheme;
    onValidationChange?.(isValid);
  }, [formData.toneAndVoice]);

  return (
    <div className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Select the tone of voice for your eBook
        </h2>
      </div>

      {/* Tone of Voice Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Tone of voice
        </label>
        <CustomDropdown
          value={formData.toneAndVoice?.toneOfVoice || 'informative'}
          onChange={handleToneChange}
          options={toneOptions}
          placeholder="Informative"
          className="w-full max-w-md"
        />
      </div>

      {/* Theme Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Theme of project
        </label>
        <CustomDropdown
          value={formData.toneAndVoice?.selectedTheme || 'health-wellness'}
          onChange={handleThemeChange}
          options={themeOptions.slice(0, 8)} // Show first 8 themes in dropdown
          placeholder="Health & wellness, Kids, Self Development"
          className="w-full max-w-md"
        />
      </div>

      {/* Most Popular Themes */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-text-primary">
          Most popular themes
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
          {popularThemes.map((theme) => (
            <button
              key={theme.id}
              onClick={() => handleThemeChange(theme.id)}
              className={`
                flex flex-col items-center p-4 rounded-lg border-2 transition-all hover:shadow-md text-center
                ${formData.toneAndVoice?.selectedTheme === theme.id
                  ? 'border-primary bg-primary/5 shadow-lg'
                  : 'border-border hover:border-primary/50'
                }
              `}
            >
              <span className="text-2xl mb-2">{theme.icon}</span>
              <span className="text-sm font-medium text-text-primary leading-tight">
                {theme.name}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Additional Voice Settings */}
      <div className="space-y-6 pt-6 border-t border-border">
        <h3 className="text-lg font-semibold text-text-primary">
          Additional Voice Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Formality Level */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-text-primary">
              Formality Level
            </label>
            <CustomDropdown
              value={formData.toneAndVoice?.formalityLevel || 'semi-formal'}
              onChange={(value) => onInputChange('toneAndVoice.formalityLevel', value)}
              options={[
                { id: 'very-formal', name: 'Very Formal' },
                { id: 'formal', name: 'Formal' },
                { id: 'semi-formal', name: 'Semi-formal' },
                { id: 'casual', name: 'Casual' },
                { id: 'very-casual', name: 'Very Casual' }
              ]}
              placeholder="Semi-formal"
              className="w-full"
            />
          </div>

          {/* Perspective */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-text-primary">
              Perspective
            </label>
            <CustomDropdown
              value={formData.toneAndVoice?.perspectiveVoice || 'third-person'}
              onChange={(value) => onInputChange('toneAndVoice.perspectiveVoice', value)}
              options={[
                { id: 'first-person', name: 'First Person (I, we)' },
                { id: 'second-person', name: 'Second Person (you)' },
                { id: 'third-person', name: 'Third Person (they, it)' },
                { id: 'mixed', name: 'Mixed' }
              ]}
              placeholder="Third Person"
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Suggestion Text */}
      <div className="text-center pt-4">
        <p className="text-sm text-text-secondary">
          Didn't find your perfect tone of voice?{' '}
          <button className="text-primary hover:text-primary/80 underline">
            Share your suggestions with us
          </button>
        </p>
      </div>
    </div>
  );
};

export default ToneVoiceStep;
