/**
 * Image Export Test Utility
 * Helps verify that PDF exports only include block-based images
 */

// Test data with both legacy placedImages and block-based images
export const createTestDocumentData = () => ({
  title: 'Test Document',
  author: 'Test Author',
  description: 'Test document for image export verification'
});

export const createTestContentWithLegacyImages = () => ({
  chapters: [
    {
      id: 'chapter-1',
      number: 1,
      title: 'Chapter 1',
      content: '# Chapter 1\n\nThis chapter has NO block-based images.\n\nJust text content.'
    },
    {
      id: 'chapter-2',
      number: 2,
      title: 'Chapter 2', 
      content: '# Chapter 2\n\nThis chapter has a block-based image:\n\n![Block Image](https://example.com/block-image.jpg)\n\nMore content here.'
    }
  ],
  // Legacy placedImages that should NOT appear in exports
  placedImages: {
    'chapter-1': [
      {
        id: 'legacy-image-1',
        url: 'https://example.com/legacy-image-1.jpg',
        description: 'Legacy Image 1',
        position: 'top',
        photographer: 'Legacy Photographer'
      }
    ],
    'chapter-2': [
      {
        id: 'legacy-image-2', 
        url: 'https://example.com/legacy-image-2.jpg',
        description: 'Legacy Image 2',
        position: 'middle',
        photographer: 'Another Legacy Photographer'
      }
    ]
  }
});

export const createTestContentBlockOnly = () => ({
  chapters: [
    {
      id: 'chapter-1',
      number: 1,
      title: 'Chapter 1',
      content: '# Chapter 1\n\nThis chapter has a block-based image:\n\n![Block Image 1](https://example.com/block-image-1.jpg)\n\nSome content here.'
    },
    {
      id: 'chapter-2',
      number: 2,
      title: 'Chapter 2',
      content: '# Chapter 2\n\nThis chapter has multiple block images:\n\n![Block Image 2](https://example.com/block-image-2.jpg)\n\nMore content.\n\n![Block Image 3](https://example.com/block-image-3.jpg)\n\nFinal content.'
    }
  ]
  // No placedImages - clean block-based content
});

// Test function to verify export content
export const analyzeExportContent = (htmlContent) => {
  const analysis = {
    hasLegacyImages: false,
    legacyImageUrls: [],
    hasBlockImages: false,
    blockImageUrls: [],
    totalImages: 0
  };

  // Check for legacy image URLs that should NOT be present
  const legacyUrls = [
    'https://example.com/legacy-image-1.jpg',
    'https://example.com/legacy-image-2.jpg'
  ];

  legacyUrls.forEach(url => {
    if (htmlContent.includes(url)) {
      analysis.hasLegacyImages = true;
      analysis.legacyImageUrls.push(url);
    }
  });

  // Check for block-based image URLs that SHOULD be present
  const blockUrls = [
    'https://example.com/block-image.jpg',
    'https://example.com/block-image-1.jpg', 
    'https://example.com/block-image-2.jpg',
    'https://example.com/block-image-3.jpg'
  ];

  blockUrls.forEach(url => {
    if (htmlContent.includes(url)) {
      analysis.hasBlockImages = true;
      analysis.blockImageUrls.push(url);
    }
  });

  // Count total img tags
  const imgMatches = htmlContent.match(/<img[^>]*>/g);
  analysis.totalImages = imgMatches ? imgMatches.length : 0;

  return analysis;
};

// Test function to run complete verification
export const runImageExportTest = async (exportFunction) => {
  console.log('🧪 Running Image Export Test...');

  const documentData = createTestDocumentData();
  
  // Test 1: Content with legacy images (should be ignored)
  console.log('\n📋 Test 1: Legacy images should be ignored');
  const contentWithLegacy = createTestContentWithLegacyImages();
  
  try {
    // Mock the export to capture HTML content
    let capturedHtml = '';
    const originalDownloadFile = global.downloadFile;
    global.downloadFile = (content) => { capturedHtml = content; };
    
    await exportFunction(documentData, contentWithLegacy);
    
    const analysis1 = analyzeExportContent(capturedHtml);
    console.log('📊 Test 1 Results:', analysis1);
    
    if (analysis1.hasLegacyImages) {
      console.error('❌ FAIL: Legacy images found in export!', analysis1.legacyImageUrls);
    } else {
      console.log('✅ PASS: No legacy images in export');
    }
    
    if (analysis1.hasBlockImages) {
      console.log('✅ PASS: Block images included in export');
    } else {
      console.warn('⚠️ WARNING: No block images found in export');
    }
    
    // Restore original function
    global.downloadFile = originalDownloadFile;
    
  } catch (error) {
    console.error('❌ Test 1 failed:', error);
  }

  // Test 2: Clean block-only content
  console.log('\n📋 Test 2: Block-only content');
  const contentBlockOnly = createTestContentBlockOnly();
  
  try {
    let capturedHtml = '';
    const originalDownloadFile = global.downloadFile;
    global.downloadFile = (content) => { capturedHtml = content; };
    
    await exportFunction(documentData, contentBlockOnly);
    
    const analysis2 = analyzeExportContent(capturedHtml);
    console.log('📊 Test 2 Results:', analysis2);
    
    if (analysis2.hasBlockImages && analysis2.blockImageUrls.length >= 3) {
      console.log('✅ PASS: All block images included in export');
    } else {
      console.warn('⚠️ WARNING: Not all block images found in export');
    }
    
    global.downloadFile = originalDownloadFile;
    
  } catch (error) {
    console.error('❌ Test 2 failed:', error);
  }

  console.log('\n🏁 Image Export Test Complete');
};

// Usage example:
// import { runImageExportTest } from './utils/imageExportTest';
// import { exportAsHtml } from './services/exportService';
// runImageExportTest(exportAsHtml);
