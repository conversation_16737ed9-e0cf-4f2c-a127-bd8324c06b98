import React from 'react';
import Icon from '../AppIcon';
import Button from './Button';

/**
 * Enhanced modal for PDF extraction results and error handling
 * Provides informative feedback for both success and failure cases
 * Follows the same pattern as DocxExtractionModal for consistency
 */
const PdfExtractionModal = ({ 
  isOpen, 
  onClose, 
  type = 'error', // 'error', 'success', 'loading'
  title,
  message,
  extractedData = null,
  onRetry = null,
  onContinue = null,
  progress = 0
}) => {
  if (!isOpen) return null;

  const getModalIcon = () => {
    switch (type) {
      case 'success':
        return <Icon name="CheckCircle" size={48} className="text-green-500" />;
      case 'loading':
        return <Icon name="Loader2" size={48} className="text-blue-500 animate-spin" />;
      case 'error':
      default:
        return <Icon name="AlertCircle" size={48} className="text-red-500" />;
    }
  };

  const getModalColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'loading':
        return 'bg-blue-50 border-blue-200';
      case 'error':
      default:
        return 'bg-red-50 border-red-200';
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderLoadingContent = () => (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        {getModalIcon()}
      </div>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <p className="text-gray-600">{message}</p>
      </div>

      {/* Progress bar */}
      {progress > 0 && (
        <div className="space-y-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-sm text-gray-500">{progress}% complete</p>
        </div>
      )}
    </div>
  );

  const renderSuccessContent = () => (
    <div className="space-y-6">
      {/* Success header */}
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          {getModalIcon()}
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-gray-600">{message}</p>
        </div>
      </div>

      {/* Extracted content summary */}
      {extractedData && (
        <div className={`p-4 rounded-lg ${getModalColor()}`}>
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Extraction Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">File:</span>
                <p className="font-medium text-gray-900 truncate">{extractedData.fileName}</p>
              </div>
              <div>
                <span className="text-gray-600">Size:</span>
                <p className="font-medium text-gray-900">{formatFileSize(extractedData.fileSize)}</p>
              </div>
              <div>
                <span className="text-gray-600">Pages:</span>
                <p className="font-medium text-gray-900">{extractedData.structure?.totalPages || 'N/A'}</p>
              </div>
              <div>
                <span className="text-gray-600">Word Count:</span>
                <p className="font-medium text-gray-900">{extractedData.wordCount?.toLocaleString() || 'N/A'}</p>
              </div>
            </div>
            
            {extractedData.originalTitle && (
              <div>
                <span className="text-gray-600">Title:</span>
                <p className="font-medium text-gray-900">{extractedData.originalTitle}</p>
              </div>
            )}
            
            {extractedData.headings && extractedData.headings.length > 0 && (
              <div>
                <span className="text-gray-600">Structure:</span>
                <p className="font-medium text-gray-900">{extractedData.headings.length} headings detected</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Content preview */}
      {extractedData?.extractedContent && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900">Content Preview</h4>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 max-h-32 overflow-y-auto">
            <p className="text-sm text-gray-700 whitespace-pre-wrap">
              {extractedData.extractedContent.substring(0, 300)}
              {extractedData.extractedContent.length > 300 && '...'}
            </p>
          </div>
        </div>
      )}
    </div>
  );

  const renderErrorContent = () => (
    <div className="space-y-6">
      {/* Error header */}
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          {getModalIcon()}
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-gray-600">{message}</p>
        </div>
      </div>

      {/* Common Issues and Solutions */}
      <div className={`p-4 rounded-lg ${getModalColor()}`}>
        <div className="flex items-start space-x-3">
          <Icon name="Info" size={20} className="text-red-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Common Issues</h4>
            <ul className="text-sm text-gray-700 space-y-1 ml-4">
              <li>• File may be password-protected or corrupted</li>
              <li>• PDF might be image-based (scanned) with no text content</li>
              <li>• File size exceeds the 10MB limit</li>
              <li>• PDF version might not be supported</li>
              <li>• Document contains complex formatting that couldn't be processed</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Troubleshooting tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Icon name="Lightbulb" size={20} className="text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Troubleshooting Tips</h4>
            <ul className="text-sm text-gray-700 space-y-1 ml-4">
              <li>• Try saving the PDF in a newer format</li>
              <li>• Remove password protection if present</li>
              <li>• For scanned PDFs, use OCR software to convert to text-based PDF</li>
              <li>• Reduce file size if it's too large</li>
              <li>• Try a different PDF file to test the functionality</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (type) {
      case 'loading':
        return renderLoadingContent();
      case 'success':
        return renderSuccessContent();
      case 'error':
      default:
        return renderErrorContent();
    }
  };

  const renderActions = () => {
    if (type === 'loading') {
      return null; // No actions during loading
    }

    return (
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        {type === 'error' && onRetry && (
          <Button
            variant="outline"
            onClick={onRetry}
            className="flex items-center space-x-2"
          >
            <Icon name="RotateCcw" size={16} />
            <span>Try Again</span>
          </Button>
        )}
        
        {type === 'success' && onContinue && (
          <Button
            variant="primary"
            onClick={onContinue}
            className="flex items-center space-x-2"
          >
            <Icon name="ArrowRight" size={16} />
            <span>Continue</span>
          </Button>
        )}
        
        <Button
          variant={type === 'success' ? 'outline' : 'primary'}
          onClick={onClose}
        >
          {type === 'success' ? 'Close' : 'OK'}
        </Button>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            PDF Content Extraction
          </h2>
          {type !== 'loading' && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Icon name="X" size={20} className="text-gray-500" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {renderContent()}
        </div>

        {/* Actions */}
        {renderActions() && (
          <div className="px-6 pb-6">
            {renderActions()}
          </div>
        )}
      </div>
    </div>
  );
};

export default PdfExtractionModal;
