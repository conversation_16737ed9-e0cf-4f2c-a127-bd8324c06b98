@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Colors */
    --color-primary: #1E3A8A; /* blue-800 */
    --color-primary-foreground: #FFFFFF; /* white */
    
    /* Secondary Colors */
    --color-secondary: #3B82F6; /* blue-500 */
    --color-secondary-foreground: #FFFFFF; /* white */
    
    /* Accent Colors */
    --color-accent: #10B981; /* emerald-500 */
    --color-accent-foreground: #FFFFFF; /* white */
    
    /* Background Colors */
    --color-background: #FAFBFC; /* gray-50 */
    --color-surface: #FFFFFF; /* white */
    
    /* Text Colors */
    --color-text-primary: #1F2937; /* gray-800 */
    --color-text-secondary: #6B7280; /* gray-500 */
    
    /* Status Colors */
    --color-success: #059669; /* emerald-600 */
    --color-success-foreground: #FFFFFF; /* white */
    --color-warning: #D97706; /* amber-600 */
    --color-warning-foreground: #FFFFFF; /* white */
    --color-error: #DC2626; /* red-600 */
    --color-error-foreground: #FFFFFF; /* white */
    
    /* Border Colors */
    --color-border: #E5E7EB; /* gray-200 */
    --color-border-strong: #D1D5DB; /* gray-300 */
    
    /* Shadow Colors */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-text-primary font-body;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
}

@layer components {
  .shadow-elevation-1 {
    box-shadow: var(--shadow-sm);
  }
  
  .shadow-elevation-2 {
    box-shadow: var(--shadow-md);
  }
  
  .shadow-elevation-3 {
    box-shadow: var(--shadow-lg);
  }
  
  .transition-micro {
    transition: all 150ms ease-in-out;
  }
  
  .transition-standard {
    transition: all 200ms ease-out;
  }
  
  .transition-complex {
    transition: all 300ms ease-out;
  }
  
  .scale-micro {
    transform: scale(1.02);
  }
}