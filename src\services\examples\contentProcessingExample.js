/**
 * Content Processing Examples
 * 
 * This file demonstrates how to use the HTML and markdown content processing
 * utilities for DOCX export functionality.
 */

import {
    generateDocxFromContent,
    generateAndDownloadDocxFromContent
} from '../docxGenerationService.js';

import {
    processContent,
    parseHTMLContent,
    parseMarkdownContent,
    convertMarkdownToHTML,
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    detectContentType
} from '../contentProcessingService.js';

/**
 * Example 1: Process HTML content from TipTap editor
 */
export const processHTMLExample = async () => {
    const htmlContent = `
        <h1>Document Title</h1>
        <h2>Introduction</h2>
        <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
        <h2>Features</h2>
        <ul>
            <li>HTML to DOCX conversion</li>
            <li>Markdown to DOCX conversion</li>
            <li>Image extraction and processing</li>
        </ul>
        <p>Here's an image: <img src="example.jpg" alt="Example image" /></p>
    `;

    console.log('Processing HTML content...');

    // Process the HTML content
    const result = await processContent(htmlContent, 'html');
    console.log('Processed content:', result);

    // Extract images
    const images = extractImagesFromHTML(htmlContent);
    console.log('Extracted images:', images);

    return result;
};

/**
 * Example 2: Process markdown content
 */
export const processMarkdownExample = async () => {
    const markdownContent = `
# Document Title

## Introduction

This is a paragraph with **bold text** and *italic text*.

## Features

- HTML to DOCX conversion
- Markdown to DOCX conversion  
- Image extraction and processing

Here's an image: ![Example image](example.jpg)

[Link to documentation](https://example.com/docs)
    `.trim();

    console.log('Processing markdown content...');

    // Process the markdown content
    const result = await processContent(markdownContent, 'markdown');
    console.log('Processed content:', result);

    // Extract images
    const images = extractImagesFromMarkdown(markdownContent);
    console.log('Extracted images:', images);

    return result;
};

/**
 * Example 3: Auto-detect content type and process
 */
export const autoDetectExample = async () => {
    const htmlContent = '<h1>HTML Title</h1><p>This is HTML content.</p>';
    const markdownContent = '# Markdown Title\n\nThis is **markdown** content.';

    console.log('Auto-detecting content types...');

    const htmlType = detectContentType(htmlContent);
    const markdownType = detectContentType(markdownContent);

    console.log('HTML detected as:', htmlType);
    console.log('Markdown detected as:', markdownType);

    // Process both with auto-detection
    const htmlResult = await processContent(htmlContent);
    const markdownResult = await processContent(markdownContent);

    return { htmlResult, markdownResult };
};

/**
 * Example 4: Generate DOCX from HTML content
 */
export const generateDocxFromHTMLExample = async () => {
    const documentData = {
        title: 'HTML to DOCX Example',
        author: 'DocForge AI',
        description: 'Example of converting HTML content to DOCX format'
    };

    const htmlContent = `
        <h1>Sample Document</h1>
        <h2>Overview</h2>
        <p>This document demonstrates the conversion of HTML content to DOCX format.</p>
        <h2>Features</h2>
        <ul>
            <li>Heading conversion (H1, H2, H3)</li>
            <li>Text formatting (<strong>bold</strong>, <em>italic</em>)</li>
            <li>List processing</li>
            <li>Image handling</li>
        </ul>
        <h2>Conclusion</h2>
        <p>The HTML to DOCX conversion preserves formatting and structure.</p>
    `;

    console.log('Generating DOCX from HTML...');

    const result = await generateDocxFromContent(documentData, htmlContent, 'html');

    if (result.success) {
        console.log('DOCX generated successfully!');
        console.log('Content type:', result.contentType);
        console.log('Processed elements:', result.processedContent.processedContent.length);

        // In a real application, you would save or download the blob
        // For this example, we just log the success
        return result.blob;
    } else {
        console.error('DOCX generation failed:', result.error);
        return null;
    }
};

/**
 * Example 5: Generate DOCX from markdown content
 */
export const generateDocxFromMarkdownExample = async () => {
    const documentData = {
        title: 'Markdown to DOCX Example',
        author: 'DocForge AI',
        description: 'Example of converting Markdown content to DOCX format'
    };

    const markdownContent = `
# Sample Document

## Overview

This document demonstrates the conversion of Markdown content to DOCX format.

## Features

- Heading conversion (# ## ###)
- Text formatting (**bold**, *italic*)
- List processing
- Image handling: ![Sample](sample.jpg)
- Link processing: [DocForge AI](https://docforge.ai)

## Code Example

Here's how to use the content processing:

\`\`\`javascript
const result = await processContent(content, 'markdown');
\`\`\`

## Conclusion

The Markdown to DOCX conversion preserves formatting and structure.
    `.trim();

    console.log('Generating DOCX from Markdown...');

    const result = await generateDocxFromContent(documentData, markdownContent, 'markdown');

    if (result.success) {
        console.log('DOCX generated successfully!');
        console.log('Content type:', result.contentType);
        console.log('Processed elements:', result.processedContent.processedContent.length);

        return result.blob;
    } else {
        console.error('DOCX generation failed:', result.error);
        return null;
    }
};

/**
 * Example 6: Convert markdown to HTML (useful for preview)
 */
export const markdownToHTMLExample = () => {
    const markdownContent = `
# Title

This is a paragraph with **bold** and *italic* text.

## Subtitle

- List item 1
- List item 2

![Image](image.jpg)

[Link](https://example.com)
    `.trim();

    console.log('Converting markdown to HTML...');

    const htmlContent = convertMarkdownToHTML(markdownContent);
    console.log('Converted HTML:', htmlContent);

    return htmlContent;
};

/**
 * Run all examples
 */
export const runAllExamples = async () => {
    console.log('=== Content Processing Examples ===\n');

    try {
        console.log('1. HTML Processing Example:');
        await processHTMLExample();
        console.log('\n');

        console.log('2. Markdown Processing Example:');
        await processMarkdownExample();
        console.log('\n');

        console.log('3. Auto-detect Example:');
        await autoDetectExample();
        console.log('\n');

        console.log('4. HTML to DOCX Example:');
        await generateDocxFromHTMLExample();
        console.log('\n');

        console.log('5. Markdown to DOCX Example:');
        await generateDocxFromMarkdownExample();
        console.log('\n');

        console.log('6. Markdown to HTML Example:');
        markdownToHTMLExample();
        console.log('\n');

        console.log('All examples completed successfully!');
    } catch (error) {
        console.error('Example execution failed:', error);
    }
};

// Export all examples
export default {
    processHTMLExample,
    processMarkdownExample,
    autoDetectExample,
    generateDocxFromHTMLExample,
    generateDocxFromMarkdownExample,
    markdownToHTMLExample,
    runAllExamples
};