# Design Document

## Overview

This design outlines the implementation of proper DOCX export functionality to replace the current RTF-based workaround. The solution will use the `docx` npm library to generate true Microsoft Word documents with embedded images, proper formatting, and full compatibility with Word processors.

## Architecture

### Library Selection: `docx` npm package

**Chosen Library:** `docx` (https://github.com/dolanmiu/docx)

**Rationale:**
- **Active Maintenance**: Actively maintained with regular updates and bug fixes
- **TypeScript Support**: Written in TypeScript with excellent type definitions
- **Feature Complete**: Supports all required features (images, formatting, styles, page breaks)
- **Browser Compatible**: Works in both Node.js and browser environments
- **Microsoft Word Compatibility**: Generates true OOXML format compatible with Word 2007+
- **Size Efficient**: Reasonable bundle size for client-side usage
- **Documentation**: Comprehensive documentation and examples

**Alternative Libraries Considered:**
- `mammoth`: Only for reading DOCX files, not generation
- `officegen`: Outdated, poor maintenance, complex API
- `html-docx-js`: Limited formatting support, poor image handling

### Service Architecture

The implementation will follow the existing service pattern in the codebase:

```
src/services/
├── exportService.js (existing - will be updated)
└── docxGenerationService.js (new - dedicated DOCX logic)
```

### Data Flow

1. **Input Processing**: Document data and generated content from editor
2. **Content Transformation**: Convert markdown/HTML content to DOCX-compatible format
3. **Image Processing**: Download and embed images from URLs
4. **Document Generation**: Use `docx` library to create structured document
5. **File Download**: Generate blob and trigger download

## Components and Interfaces

### DocxGenerationService

```javascript
// Main service interface
export const generateDocxDocument = async (documentData, generatedContent) => {
  // Returns: { success: boolean, blob?: Blob, error?: string }
}

// Content processing utilities
export const processMarkdownContent = (markdownText) => {
  // Converts markdown to docx-compatible format
}

export const processImages = async (content) => {
  // Downloads and processes images for embedding
}

export const createDocumentStructure = (documentData, processedContent) => {
  // Creates the docx document structure
}
```

### Updated ExportService

```javascript
// Updated export function in exportService.js
export const exportAsDocx = async (documentData, generatedContent, editorInstance) => {
  // Will use DocxGenerationService instead of RTF generation
  // editorInstance provides access to TipTap editor for HTML content extraction
}
```

### Integration with TipTap Editor

The DOCX export will integrate with the existing TipTap editor system:

1. **Content Source**: Export will use `editor.getHTML()` to get the current editor content
2. **Image Handling**: Both TipTap images (`<img>` tags) and legacy markdown images will be processed
3. **Formatting Preservation**: HTML formatting from TipTap will be converted to Word styles
4. **Editor State**: Export will work with the current editor state, not just saved document data

### Content Processing Pipeline

Based on the current TipTap editor implementation, images are handled in two ways:

1. **TipTap Editor Images**: Images inserted directly into the editor using `@tiptap/extension-image`
   - Images are stored as HTML `<img>` tags within the editor content
   - Accessible via TipTap's HTML output using `editor.getHTML()`
   - Include src, alt, and optional class attributes for sizing

2. **Legacy Markdown Images**: Images embedded as markdown syntax in chapter content
   - Format: `![alt text](image_url)`
   - Used in older documents and AI-generated content
   - Converted to HTML during export processing

**Processing Steps:**

1. **Content Extraction**: Get HTML content from TipTap editor using `editor.getHTML()`
2. **Image Detection**: Parse both HTML `<img>` tags and markdown image syntax
3. **Image Download**: Fetch images from URLs and convert to binary data
4. **Format Conversion**: Convert images to DOCX-compatible formats (JPEG/PNG)
5. **Document Integration**: Embed processed images into DOCX structure with proper positioning

**Content Processing Functions:**

1. **HTML Parser**: Extract and convert HTML elements from TipTap editor
   - Parse `<h1>`, `<h2>`, `<h3>` → Word heading styles
   - Parse `<strong>`, `<em>` → Word text formatting
   - Parse `<ul>`, `<ol>`, `<li>` → Word list formatting
   - Parse `<img>` tags → Extract URLs and attributes for processing

2. **Markdown Parser**: Handle legacy markdown content
   - Convert markdown headers, formatting, and images
   - Support for existing documents with markdown syntax

3. **Image Processor**: Handle image embedding
   - Download images from URLs (both from HTML and markdown)
   - Convert to appropriate format (JPEG/PNG)
   - Resize if necessary for DOCX compatibility
   - Handle CORS and loading errors gracefully

4. **Document Builder**: Construct DOCX structure
   - Title page with metadata
   - Chapter organization with page breaks
   - Proper styling and formatting
   - Image embedding with positioning and sizing

## Data Models

### Document Structure

```javascript
const documentStructure = {
  metadata: {
    title: string,
    author: string,
    description: string,
    created: Date
  },
  titlePage: {
    title: string,
    author: string,
    description: string
  },
  chapters: [
    {
      number: number,
      title: string,
      content: ProcessedContent[],
      pageBreak: boolean
    }
  ]
}
```

### Processed Content

```javascript
const processedContent = {
  type: 'paragraph' | 'heading' | 'image' | 'list',
  level?: number, // for headings
  text?: string,
  formatting?: {
    bold: boolean,
    italic: boolean,
    underline: boolean
  },
  image?: {
    data: ArrayBuffer,
    width: number,
    height: number,
    alt: string,
    src: string // original URL for reference
  },
  listItems?: string[]
}
```

### TipTap Editor Content Structure

```javascript
// Content extracted from editor.getHTML()
const editorContent = {
  htmlContent: string, // Full HTML from TipTap editor
  images: [
    {
      src: string,      // Image URL
      alt: string,      // Alt text
      class?: string,   // Size class (small, medium, large)
      width?: number,   // Optional width
      height?: number   // Optional height
    }
  ]
}
```

## Error Handling

### Error Categories

1. **Network Errors**: Image download failures
2. **Processing Errors**: Content conversion issues
3. **Generation Errors**: DOCX library errors
4. **Browser Errors**: File download issues

### Error Handling Strategy

```javascript
const errorHandling = {
  imageDownloadFailure: {
    strategy: 'skip_with_placeholder',
    fallback: 'Insert [Image unavailable] text'
  },
  contentProcessingError: {
    strategy: 'graceful_degradation',
    fallback: 'Use plain text formatting'
  },
  docxGenerationError: {
    strategy: 'user_notification',
    fallback: 'Show detailed error message'
  }
}
```

### Retry Logic

- Image downloads: 3 attempts with exponential backoff
- Content processing: Single attempt with fallback
- Document generation: Single attempt with detailed error reporting

## Testing Strategy

### Unit Tests

1. **Content Processing Tests**
   - Markdown to DOCX conversion
   - Image URL extraction
   - Text formatting preservation

2. **Document Generation Tests**
   - Title page creation
   - Chapter organization
   - Style application

3. **Error Handling Tests**
   - Network failure scenarios
   - Invalid content handling
   - Malformed data processing

### Integration Tests

1. **End-to-End Export Tests**
   - Complete document export flow
   - File download verification
   - DOCX file validation

2. **Image Processing Tests**
   - Various image formats
   - Large image handling
   - CORS scenarios

### Manual Testing Checklist

1. **Document Structure**
   - Title page formatting
   - Chapter organization
   - Page breaks

2. **Content Formatting**
   - Header styles
   - Text formatting (bold, italic)
   - List formatting

3. **Image Handling**
   - Image embedding
   - Size and positioning
   - Alt text preservation

4. **Compatibility Testing**
   - Microsoft Word 2016+
   - Google Docs
   - LibreOffice Writer

## Implementation Phases

### Phase 1: Core Infrastructure
- Install and configure `docx` library
- Create DocxGenerationService skeleton
- Implement basic document structure

### Phase 2: Content Processing
- Markdown to DOCX conversion
- Text formatting preservation
- Basic image handling

### Phase 3: Advanced Features
- Image downloading and embedding
- Error handling and retry logic
- Performance optimization

### Phase 4: Integration and Testing
- Update exportService.js
- Comprehensive testing
- Documentation and cleanup

## Performance Considerations

### Bundle Size Impact
- `docx` library adds ~200KB to bundle
- Consider lazy loading for export functionality
- Implement code splitting if necessary

### Memory Usage
- Process images in chunks for large documents
- Clean up blob URLs after download
- Implement progress indicators for large exports

### Network Optimization
- Parallel image downloads with concurrency limits
- Image compression before embedding
- Timeout handling for slow networks

## Security Considerations

### Image Processing
- Validate image URLs before downloading
- Implement CORS handling
- Sanitize image data before embedding

### Content Processing
- Sanitize markdown content
- Prevent XSS in content processing
- Validate document metadata

### File Generation
- Limit document size to prevent memory issues
- Validate generated DOCX structure
- Implement proper error boundaries