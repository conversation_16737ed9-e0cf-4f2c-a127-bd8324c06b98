# Tiptap Document Editor - Product Requirements Document

## Executive Summary

This PRD outlines the progressive development of a rich text document editor using Tiptap, building incrementally from the current minimal implementation to achieve a Notion-like editing experience with AI content integration.

**🎉 STATUS UPDATE: All Phases Complete!** Foundation features, comprehensive floating menu system, and full AI content integration have been successfully implemented. The Tiptap Document Editor is now fully functional with AI content loading, editing capabilities, and floating menus.

## Current State Analysis

### Existing Implementation
- **Location**: `src/pages/document-editor/components/DocumentCanvasMinimal.jsx`
- **Current Setup**: Basic Tiptap editor with StarterKit only
- **Content**: Static placeholder "Start writing..."
- **Styling**: Tailwind prose classes with responsive design
- **Dependencies**: All required Tiptap packages already installed

### Available Tiptap Packages
```json
"@tiptap/core": "^2.25.0",
"@tiptap/react": "^2.25.0", 
"@tiptap/starter-kit": "^2.25.0",
"@tiptap/extension-placeholder": "^2.25.0",
"@tiptap/extension-floating-menu": "^2.25.0",
"@tiptap/extension-bubble-menu": "^2.25.0",
"@tiptap/extension-image": "^2.25.0",
"@tiptap/extension-character-count": "^2.25.0"
```

### AI Content Integration Points
- **AI Service**: `src/services/aiService.js` generates structured markdown content
- **Content Structure**: Introduction, chapters, conclusion with word counts
- **Document Flow**: AI generates → Editor displays → User edits → Save to localStorage
- **Content Format**: Markdown strings need conversion to HTML for Tiptap

## Core Requirements

### 1. Placeholder Text System
- **Requirement**: New lines display "regular text" placeholder when empty
- **Behavior**: Placeholder appears on empty paragraphs, disappears when typing
- **Styling**: Subtle gray text that doesn't interfere with content

### 2. Floating Context Menus
- **Requirement**: Node-specific menus appear on the left when clicked
- **Behavior**: 
  - Menu appears to the left side of focused node
  - Disappears when clicking elsewhere or different node
  - Shows relevant formatting options for node type
- **Node Types**: Paragraph, headings (H1-H6), lists, images

### 3. AI Content Integration
- **Requirement**: Pre-populate editor with AI-generated content
- **Behavior**:
  - Convert markdown to HTML for Tiptap display
  - Maintain content structure (chapters, sections)
  - Handle content updates and persistence
  - Support content regeneration

## Implementation Phases

## ✅ Phase 1: Foundation Setup (COMPLETED - 45-60 minutes)

### ✅ Step 1.1: Enhanced Placeholder System (COMPLETED - 15 minutes)
**Objective**: Add "regular text" placeholder to empty lines

**Implementation**:
- ✅ Configure Placeholder extension in DocumentCanvasMinimal
- ✅ Set placeholder text to "regular text"
- ✅ Style placeholder with subtle gray color

**Acceptance Criteria**:
- ✅ Empty paragraphs show "regular text" placeholder
- ✅ Placeholder disappears when typing starts
- ✅ Placeholder reappears when content is deleted
- ✅ Styling matches design requirements

**Testing Checklist**:
- ✅ Create new paragraph - placeholder appears
- ✅ Type text - placeholder disappears
- ✅ Delete all text - placeholder reappears
- ✅ Multiple empty paragraphs show placeholder
- ✅ Placeholder styling is consistent

**Status**: ✅ COMPLETE - Testing checklist available at `docs/STEP_1_1_TESTING_CHECKLIST.md`

### ✅ Step 1.2: Content Loading Infrastructure (COMPLETED - 20 minutes)
**Objective**: Prepare editor to receive AI-generated content

**Implementation**:
- ✅ Add content prop to DocumentCanvasMinimal
- ✅ Create markdown-to-HTML conversion utility (`src/utils/contentConverter.js`)
- ✅ Handle content loading states with spinner and messaging

**Acceptance Criteria**:
- ✅ Editor accepts content prop
- ✅ Markdown content converts to HTML properly
- ✅ Loading states handled gracefully
- ✅ Content updates trigger re-render

**Testing Checklist**:
- ✅ Pass markdown content - renders as HTML
- ✅ Pass empty content - shows placeholder
- ✅ Pass malformed content - handles gracefully
- ✅ Content updates reflect in editor

**Status**: ✅ COMPLETE - Testing checklist available at `docs/STEP_1_2_TESTING_CHECKLIST.md`

### ✅ Step 1.3: Basic Styling Improvements (COMPLETED - 10 minutes)
**Objective**: Enhance visual presentation

**Implementation**:
- ✅ Improve editor container styling with gradient backgrounds and shadows
- ✅ Add focus states and visual feedback with blue borders and glow effects
- ✅ Ensure responsive design with mobile-optimized spacing
- ✅ Enhanced loading states with better animations and messaging

**Acceptance Criteria**:
- ✅ Editor has clear visual boundaries
- ✅ Focus states provide clear feedback
- ✅ Responsive design works on all screen sizes
- ✅ Styling integrates with existing design system

**Testing Checklist**:
- ✅ Click editor - clear focus indication
- ✅ Resize window - responsive behavior
- ✅ Compare with existing UI - consistent styling
- ✅ Test on mobile viewport

**Status**: ✅ COMPLETE - Testing checklist available at `docs/STEP_1_3_TESTING_CHECKLIST.md`

## ✅ Phase 2: Floating Menu System (COMPLETE - 60-75 minutes)

### Step 2.1: Basic Floating Menu Setup (25 minutes)
**Objective**: Implement floating menu extension

**Implementation**:
- Add FloatingMenu extension to editor
- Create basic menu component
- Position menu to the left of active node

**Acceptance Criteria**:
- [ ] Floating menu appears when node is focused
- [ ] Menu positioned to the left of node
- [ ] Menu disappears when clicking elsewhere
- [ ] Menu follows node selection

**Testing Checklist**:
- [ ] Click paragraph - menu appears on left
- [ ] Click different paragraph - menu moves
- [ ] Click outside editor - menu disappears
- [ ] Menu doesn't interfere with typing

**Rollback**: Remove FloatingMenu extension and related components

### Step 2.2: Node-Specific Menu Content (30 minutes)
**Objective**: Show relevant options based on node type

**Implementation**:
- Detect current node type
- Show appropriate formatting options
- Handle different node types (paragraph, heading, list)

**Acceptance Criteria**:
- [ ] Paragraph nodes show text formatting options
- [ ] Heading nodes show heading level options
- [ ] List nodes show list formatting options
- [ ] Menu content updates based on selection

**Testing Checklist**:
- [ ] Select paragraph - shows bold, italic, etc.
- [ ] Select heading - shows H1-H6 options
- [ ] Select list item - shows list options
- [ ] Switch between nodes - menu updates

**Rollback**: Revert to basic menu without node-specific content

### Step 2.3: Menu Interactions and Styling (20 minutes)
**Objective**: Polish menu behavior and appearance

**Implementation**:
- Add smooth animations
- Improve menu styling
- Handle edge cases (menu positioning)

**Acceptance Criteria**:
- [ ] Menu animations are smooth
- [ ] Menu styling matches design system
- [ ] Menu handles edge cases (screen boundaries)
- [ ] Menu is accessible via keyboard

**Testing Checklist**:
- [ ] Menu appears/disappears smoothly
- [ ] Menu stays within viewport bounds
- [ ] Menu accessible via Tab key
- [ ] Menu styling consistent with app

**Rollback**: Remove animations and styling enhancements

**Status**: ✅ COMPLETE - Comprehensive floating menu system implemented with support for all essential node types (paragraphs, headings, list items, blockquotes, code blocks). Features include:
- Two-stage interaction (button → expanded menu)
- Responsive positioning across all screen sizes
- Node-specific contextual menu options
- Smooth animations and intuitive UX
- Complete Priority 1 node type coverage

## ✅ Phase 3: AI Content Integration (COMPLETE - 45 minutes)

### ✅ Step 3.1: Content Conversion Utilities (COMPLETED - 20 minutes)
**Objective**: Convert AI markdown to Tiptap HTML

**Implementation**:
- ✅ Create markdown-to-HTML converter
- ✅ Handle chapter structure preservation
- ✅ Ensure proper HTML formatting for Tiptap
- ✅ **CRITICAL FIX**: Added user edit preservation to prevent content reset loop

**Acceptance Criteria**:
- ✅ Markdown converts to valid HTML
- ✅ Chapter structure preserved
- ✅ Headings, paragraphs, lists convert properly
- ✅ Special characters handled correctly
- ✅ **NEW**: User edits preserved via editorHTML field

**Testing Checklist**:
- ✅ Convert sample AI content - renders correctly
- ✅ Test with various markdown elements
- ✅ Verify HTML structure in DevTools
- ✅ Test with edge cases (empty chapters, special chars)
- ✅ **NEW**: Verify user typing doesn't cause cursor jumping

**Status**: ✅ COMPLETE - Content conversion utilities working with critical bug fixes for editor functionality

**Key Fixes Applied**:
- Modified `convertAIContentToHTML()` to return `editorHTML` when user edits exist
- Prevents content reset loop that was breaking editor input functionality
- Maintains backward compatibility for initial AI content loading

### ✅ Step 3.2: Content Loading Integration (COMPLETED - 25 minutes)
**Objective**: Load AI content into editor

**Implementation**:
- ✅ Integrate with existing AI content flow
- ✅ Handle content loading states
- ✅ Update DocumentEditor to pass content to editor

**Acceptance Criteria**:
- ✅ AI-generated content loads into editor
- ✅ Loading states show appropriate feedback
- ✅ Content structure maintained in editor
- ✅ Editor updates when content changes

**Testing Checklist**:
- ✅ Generate document - content appears in editor
- ✅ Loading spinner shows during generation
- ✅ Content structure matches AI output
- ✅ Regenerate content - editor updates

**Status**: ✅ COMPLETE - Full AI content integration working with DocumentEditor passing generatedContent prop to DocumentCanvasMinimal, loading states implemented, and content regeneration workflow functional

## Integration Points

### 1. DocumentEditor.jsx Integration
- **Current**: Renders DocumentCanvasMinimal
- **Required**: Pass generatedContent as prop
- **Implementation**: Add content prop passing

### 2. AI Service Integration  
- **Current**: Generates markdown content
- **Required**: Content conversion for Tiptap
- **Implementation**: Conversion utilities

### 3. Review Mode Integration
- **Current**: Uses useReviewMode hook
- **Required**: Editor respects review/edit states
- **Implementation**: Read-only mode support

### 4. Content Persistence
- **Current**: localStorage for document data
- **Required**: Save editor content changes
- **Implementation**: Content change handlers

## Risk Assessment

### High Risk
- **Content Conversion**: Markdown to HTML conversion complexity
- **Mitigation**: Thorough testing with various content types

### Medium Risk  
- **Menu Positioning**: Floating menus may have positioning issues
- **Mitigation**: Extensive cross-browser testing

### Low Risk
- **Performance**: Large documents may impact editor performance
- **Mitigation**: Implement content virtualization if needed

## Success Metrics

### Phase 1 Success
- [ ] Placeholder system working correctly
- [ ] Content loading infrastructure complete
- [ ] Basic styling improvements applied

### Phase 2 Success
- [x] Floating menus appear and position correctly
- [x] Node-specific content shows appropriately
- [x] Menu interactions smooth and intuitive

### Phase 3 Success
- [x] AI content loads and displays correctly
- [x] Content conversion maintains structure
- [x] Integration with existing workflow complete

## Next Steps After PRD Implementation

1. **Advanced Features**: Custom node types, advanced formatting
2. **Collaboration**: Real-time editing capabilities
3. **Export Integration**: Enhanced export with editor content
4. **Performance Optimization**: Large document handling
5. **Accessibility**: Full keyboard navigation and screen reader support

## Technical Implementation Details

### Content Conversion Utility Example
```javascript
// utils/contentConverter.js
import { marked } from 'marked';
import DOMPurify from 'dompurify';

export const convertMarkdownToHTML = (markdown) => {
  if (!markdown) return '<p></p>';

  try {
    // Convert markdown to HTML
    const html = marked(markdown);

    // Sanitize HTML for security
    const cleanHTML = DOMPurify.sanitize(html);

    return cleanHTML;
  } catch (error) {
    console.error('Error converting markdown:', error);
    return '<p>Error loading content</p>';
  }
};

export const convertAIContentToHTML = (generatedContent) => {
  if (!generatedContent) return '';

  let fullHTML = '';

  // Add introduction
  if (generatedContent.introduction?.content) {
    fullHTML += convertMarkdownToHTML(generatedContent.introduction.content);
  }

  // Add chapters
  if (generatedContent.chapters) {
    generatedContent.chapters.forEach(chapter => {
      if (chapter.content) {
        fullHTML += convertMarkdownToHTML(chapter.content);
      }
    });
  }

  // Add conclusion
  if (generatedContent.conclusion?.content) {
    fullHTML += convertMarkdownToHTML(generatedContent.conclusion.content);
  }

  return fullHTML;
};
```

### Enhanced DocumentCanvasMinimal Example
```javascript
// DocumentCanvasMinimal.jsx - Phase 1 Complete
import React from 'react';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { convertAIContentToHTML } from '../../../utils/contentConverter';

const DocumentCanvasMinimal = ({ content, onContentChange }) => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'regular text',
        showOnlyWhenEditable: true,
        showOnlyCurrent: false,
      }),
    ],
    content: content ? convertAIContentToHTML(content) : '<p></p>',
    onUpdate: ({ editor }) => {
      if (onContentChange) {
        onContentChange(editor.getHTML());
      }
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[700px] p-4',
      },
    },
  });

  return (
    <div className="h-full bg-gray-50 overflow-auto">
      <div className="min-h-full py-8 px-4">
        <div className="max-w-5xl mx-auto">
          <div className="bg-white shadow-lg rounded-lg min-h-[800px] p-8 focus-within:shadow-xl transition-shadow">
            <EditorContent
              editor={editor}
              className="min-h-[700px] focus:outline-none"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentCanvasMinimal;
```

### Floating Menu Component Example
```javascript
// components/FloatingNodeMenu.jsx - Phase 2
import React from 'react';
import { FloatingMenu } from '@tiptap/extension-floating-menu';
import { BubbleMenu } from '@tiptap/extension-bubble-menu';

const NodeFormatMenu = ({ editor }) => {
  if (!editor) return null;

  const isHeading = editor.isActive('heading');
  const isParagraph = editor.isActive('paragraph');
  const isList = editor.isActive('bulletList') || editor.isActive('orderedList');

  return (
    <div className="floating-menu bg-white shadow-lg rounded-lg border p-2 flex gap-1">
      {isParagraph && (
        <>
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`px-2 py-1 rounded text-sm ${
              editor.isActive('bold') ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
            }`}
          >
            Bold
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`px-2 py-1 rounded text-sm ${
              editor.isActive('italic') ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
            }`}
          >
            Italic
          </button>
        </>
      )}

      {isHeading && (
        <>
          {[1, 2, 3, 4, 5, 6].map(level => (
            <button
              key={level}
              onClick={() => editor.chain().focus().toggleHeading({ level }).run()}
              className={`px-2 py-1 rounded text-sm ${
                editor.isActive('heading', { level }) ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
              }`}
            >
              H{level}
            </button>
          ))}
        </>
      )}

      {isList && (
        <>
          <button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`px-2 py-1 rounded text-sm ${
              editor.isActive('bulletList') ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
            }`}
          >
            • List
          </button>
          <button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`px-2 py-1 rounded text-sm ${
              editor.isActive('orderedList') ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
            }`}
          >
            1. List
          </button>
        </>
      )}
    </div>
  );
};
```

## Implementation Timeline

- **Phase 1**: ✅ COMPLETE (1-1.5 hours)
- **Phase 2**: ✅ COMPLETE (1-1.25 hours)
- **Phase 3**: 45 minutes - 1 hour (NEXT)
- **Total Estimated Time**: 2.75 - 3.75 hours

## Development Workflow

### Before Each Phase
1. **Backup Current State**: Commit current changes to git
2. **Review Requirements**: Understand phase objectives
3. **Prepare Testing Environment**: Ensure dev server running
4. **Open DevTools**: Monitor console for errors

### During Implementation
1. **Follow Step-by-Step Process**: Complete each step fully before next
2. **Test Incrementally**: Verify each change works before proceeding
3. **Use Console Logging**: Add strategic console.log statements for debugging
4. **Document Issues**: Note any unexpected behavior immediately

### After Each Phase
1. **Complete Testing Checklist**: Verify all acceptance criteria met
2. **Cross-Browser Testing**: Test in Chrome, Firefox, Safari
3. **Mobile Testing**: Verify responsive behavior
4. **Performance Check**: Monitor editor performance with large content
5. **Commit Changes**: Save working state before next phase

This progressive approach ensures each phase builds solid foundations for the next, with clear rollback options and comprehensive testing at each step.
