import React, { useState } from 'react';
import Button from '../../../components/ui/Button';

/**
 * PlacedImage - Displays a placed image within the document canvas
 * Provides options to reposition, resize, or remove the image
 */
const PlacedImage = ({
  image,
  chapterId,
  position = 'top',
  onRemove,
  className = '',
  isReviewMode = false
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleRemove = () => {
    if (onRemove) {
      onRemove(chapterId, image.id);
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'mb-6';
      case 'middle':
        return 'my-6';
      case 'bottom':
        return 'mt-6';
      case 'left':
        return 'float-left mr-6 mb-4 max-w-sm';
      case 'right':
        return 'float-right ml-6 mb-4 max-w-sm';
      default:
        return 'mb-6';
    }
  };

  return (
    <div 
      className={`relative group ${getPositionClasses()} ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image */}
      <div className="relative rounded-lg overflow-hidden shadow-md">
        <img
          src={image.url}
          alt={image.description}
          className="w-full h-auto object-cover"
          style={{ maxHeight: '400px' }}
        />
        
        {/* Overlay controls - Only show in edit mode */}
        {isHovered && !isReviewMode && (
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              size="sm"
              variant="danger"
              onClick={handleRemove}
              iconName="Trash2"
              className="bg-red-500/90 hover:bg-red-500"
            >
              Remove
            </Button>
          </div>
        )}
      </div>

      {/* Image caption */}
      <div className="mt-2 text-sm text-gray-600 text-center">
        <p className="font-medium">{image.description}</p>
        <p className="text-xs">Photo by {image.photographer}</p>
      </div>
    </div>
  );
};

export default PlacedImage;
