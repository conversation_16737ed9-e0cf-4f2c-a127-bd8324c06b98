import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const FilterSidebar = ({ filters, onFilterChange, isCollapsed, onToggleCollapse }) => {
  const [expandedSections, setExpandedSections] = useState({
    documentType: true,
    industry: true,
    language: false,
    complexity: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFilterChange = (category, value) => {
    const currentValues = filters[category] || [];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    onFilterChange(category, newValues);
  };

  const clearAllFilters = () => {
    onFilterChange('all', {});
  };

  const filterSections = [
    {
      key: 'documentType',
      title: 'Document Type',
      icon: 'FileText',
      options: [
        { value: 'ebook', label: 'eBooks', count: 45 },
        { value: 'academic', label: 'Academic Papers', count: 32 },
        { value: 'business', label: 'Business Documents', count: 28 },
        { value: 'report', label: 'Reports', count: 18 },
        { value: 'presentation', label: 'Presentations', count: 15 }
      ]
    },
    {
      key: 'industry',
      title: 'Industry',
      icon: 'Building',
      options: [
        { value: 'technology', label: 'Technology', count: 25 },
        { value: 'healthcare', label: 'Healthcare', count: 20 },
        { value: 'finance', label: 'Finance', count: 18 },
        { value: 'education', label: 'Education', count: 22 },
        { value: 'marketing', label: 'Marketing', count: 16 },
        { value: 'consulting', label: 'Consulting', count: 12 }
      ]
    },
    {
      key: 'language',
      title: 'Language',
      icon: 'Globe',
      options: [
        { value: 'english', label: 'English', count: 138 },
        { value: 'yoruba', label: 'Yoruba', count: 8 },
        { value: 'french', label: 'French', count: 12 }
      ]
    },
    {
      key: 'complexity',
      title: 'Complexity Level',
      icon: 'BarChart3',
      options: [
        { value: 'beginner', label: 'Beginner', count: 45 },
        { value: 'intermediate', label: 'Intermediate', count: 68 },
        { value: 'advanced', label: 'Advanced', count: 25 }
      ]
    }
  ];

  const activeFilterCount = Object.values(filters).reduce((count, filterArray) => {
    return count + (Array.isArray(filterArray) ? filterArray.length : 0);
  }, 0);

  if (isCollapsed) {
    return (
      <div className="w-16 bg-surface border-r border-border h-full">
        <div className="p-4">
          <Button
            variant="ghost"
            onClick={onToggleCollapse}
            className="w-full p-2"
          >
            <Icon name="ChevronRight" size={16} />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-surface border-r border-border h-full overflow-y-auto">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Icon name="Filter" size={20} />
            <h2 className="text-lg font-semibold text-text-primary">Filters</h2>
            {activeFilterCount > 0 && (
              <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                {activeFilterCount}
              </span>
            )}
          </div>
          <Button
            variant="ghost"
            onClick={onToggleCollapse}
            className="p-2"
          >
            <Icon name="ChevronLeft" size={16} />
          </Button>
        </div>

        {/* Clear All Filters */}
        {activeFilterCount > 0 && (
          <Button
            variant="outline"
            onClick={clearAllFilters}
            className="w-full mb-6"
          >
            <Icon name="X" size={16} />
            Clear All Filters
          </Button>
        )}

        {/* Filter Sections */}
        <div className="space-y-6">
          {filterSections.map((section) => (
            <div key={section.key} className="border-b border-border pb-6 last:border-b-0">
              <button
                onClick={() => toggleSection(section.key)}
                className="flex items-center justify-between w-full mb-4 text-left"
              >
                <div className="flex items-center space-x-2">
                  <Icon name={section.icon} size={16} />
                  <span className="font-medium text-text-primary">{section.title}</span>
                </div>
                <Icon 
                  name={expandedSections[section.key] ? "ChevronUp" : "ChevronDown"} 
                  size={16} 
                />
              </button>

              {expandedSections[section.key] && (
                <div className="space-y-2">
                  {section.options.map((option) => (
                    <label
                      key={option.value}
                      className="flex items-center justify-between p-2 rounded-lg hover:bg-background cursor-pointer transition-micro"
                    >
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={(filters[section.key] || []).includes(option.value)}
                          onChange={() => handleFilterChange(section.key, option.value)}
                          className="w-4 h-4 text-primary border-border-strong rounded focus:ring-primary focus:ring-2"
                        />
                        <span className="text-sm text-text-primary">{option.label}</span>
                      </div>
                      <span className="text-xs text-text-secondary bg-background px-2 py-1 rounded">
                        {option.count}
                      </span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Quick Filters */}
        <div className="mt-8 pt-6 border-t border-border">
          <h3 className="text-sm font-medium text-text-primary mb-4">Quick Filters</h3>
          <div className="space-y-2">
            <Button
              variant="ghost"
              onClick={() => onFilterChange('quick', 'popular')}
              className="w-full justify-start text-sm"
            >
              <Icon name="TrendingUp" size={16} />
              Most Popular
            </Button>
            <Button
              variant="ghost"
              onClick={() => onFilterChange('quick', 'recent')}
              className="w-full justify-start text-sm"
            >
              <Icon name="Clock" size={16} />
              Recently Added
            </Button>
            <Button
              variant="ghost"
              onClick={() => onFilterChange('quick', 'free')}
              className="w-full justify-start text-sm"
            >
              <Icon name="Gift" size={16} />
              Free Templates
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterSidebar;