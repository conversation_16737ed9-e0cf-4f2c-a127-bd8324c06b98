# Design Document

## Overview

This design implements a robust "click outside to close" mechanism for the floating context menu in the TipTap editor. The solution will use <PERSON>act's useEffect hook with event delegation to detect clicks outside the menu area, similar to the existing implementation in `InlineContentToolbar.jsx` but adapted for the more complex two-stage menu system in `DocumentCanvasMinimal.jsx`.

The design focuses on performance, reliability, and seamless integration with the existing menu positioning and scroll-aware systems already in place.

## Architecture

### Component Structure

The implementation will extend the existing `DocumentCanvasMinimal.jsx` component by adding:

1. **Click Outside Detection Hook**: A useEffect that manages document-level click event listeners
2. **Menu Reference System**: Proper ref management for the expanded menu container
3. **Event Handler Integration**: Coordination with existing menu state management
4. **Cleanup Mechanism**: Proper event listener cleanup to prevent memory leaks

### State Management Integration

The click-outside functionality will integrate with the existing state variables:
- `isMenuExpanded`: Controls expanded menu visibility
- `showFloatingMenu`: Controls base menu button visibility  
- `preventAutoClose`: Existing flag to prevent unwanted menu closures

## Components and Interfaces

### Enhanced Menu Reference System

```javascript
// New ref for expanded menu container
const expandedMenuRef = useRef(null);

// Integration with existing refs
const currentTargetElementRef = useRef(null); // Already exists
```

### Click Outside Detection Hook

```javascript
useEffect(() => {
  const handleClickOutside = (event) => {
    // Check if click is outside expanded menu
    if (expandedMenuRef.current && 
        !expandedMenuRef.current.contains(event.target) &&
        !preventAutoClose) {
      setIsMenuExpanded(false);
    }
  };

  // Only add listener when expanded menu is open
  if (isMenuExpanded) {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside); // Mobile support
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }
}, [isMenuExpanded, preventAutoClose]);
```

### Menu Container Enhancement

The expanded menu JSX will be enhanced with proper ref attachment:

```javascript
{isMenuExpanded && (
  <div
    ref={expandedMenuRef}
    className="absolute bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-[200px] z-50"
    style={{
      top: expandedMenuPosition.top,
      left: expandedMenuPosition.left
    }}
  >
    {/* Existing menu content */}
  </div>
)}
```

## Data Models

### Event Handler Interface

```javascript
interface ClickOutsideHandler {
  event: MouseEvent | TouchEvent;
  target: EventTarget;
  menuRef: RefObject<HTMLElement>;
  preventClose: boolean;
}
```

### Menu State Model

```javascript
interface MenuState {
  isMenuExpanded: boolean;
  showFloatingMenu: boolean;
  preventAutoClose: boolean;
  menuPosition: { top: number; left: number };
  expandedMenuPosition: { top: number; left: number };
}
```

## Error Handling

### Event Listener Management

1. **Null Reference Protection**: Check for ref existence before accessing DOM elements
2. **Event Target Validation**: Ensure event.target exists and is a valid DOM node
3. **Cleanup Guarantee**: Use cleanup functions in useEffect to prevent memory leaks
4. **Error Boundaries**: Wrap click detection in try-catch for robustness

### Edge Cases

1. **Rapid Clicking**: Debounce or throttle rapid state changes if needed
2. **Menu Repositioning**: Handle cases where menu position changes while open
3. **Scroll Events**: Coordinate with existing scroll-aware positioning system
4. **Component Unmounting**: Ensure cleanup occurs during component unmount

## Testing Strategy

### Unit Tests

1. **Click Outside Detection**: Test that clicks outside menu area close the menu
2. **Click Inside Preservation**: Test that clicks inside menu keep it open
3. **Event Listener Cleanup**: Test that listeners are removed when menu closes
4. **Mobile Touch Events**: Test touch event handling on mobile devices

### Integration Tests

1. **Menu State Coordination**: Test integration with existing menu state management
2. **Scroll Interaction**: Test behavior during scroll events
3. **Multiple Menu Scenarios**: Test behavior with multiple overlapping UI elements
4. **Performance Impact**: Test that click detection doesn't impact editor performance

### Manual Testing Scenarios

1. **Basic Functionality**: Click + button, then click outside to verify menu closes
2. **Menu Item Interaction**: Click menu items to verify they work and close menu
3. **Scroll Testing**: Open menu, scroll document, verify click-outside still works
4. **Mobile Testing**: Test touch interactions on mobile devices
5. **Edge Cases**: Test rapid clicking, window resizing, and other edge scenarios

## Implementation Notes

### Performance Considerations

1. **Conditional Event Listeners**: Only attach listeners when menu is expanded
2. **Event Delegation**: Use document-level listeners for efficiency
3. **Memory Management**: Proper cleanup to prevent memory leaks
4. **Throttling**: Consider throttling if performance issues arise

### Integration Points

1. **Existing Scroll System**: Coordinate with `useScrollAwarePosition` hook
2. **Menu Positioning**: Work with existing `calculateExpandedMenuPosition` function
3. **State Management**: Integrate with existing menu state variables
4. **Event Handling**: Coordinate with existing click handlers

### Browser Compatibility

1. **Event Types**: Support both mouse and touch events
2. **Event Capturing**: Use appropriate event phases for reliable detection
3. **Mobile Browsers**: Ensure touch events work correctly on mobile
4. **Legacy Support**: Maintain compatibility with existing browser support