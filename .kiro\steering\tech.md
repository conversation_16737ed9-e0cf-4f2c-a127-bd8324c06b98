# Technology Stack

## Frontend Framework
- **React 18** with modern hooks and concurrent features
- **Vite** as build tool and development server (port 4028)
- **React Router v6** for client-side routing with `useRoutes` pattern

## State Management & Data
- **Redux Toolkit** for global state management
- **React Context** for auth and sidebar state
- **React Hook Form** for form handling and validation
- **Supabase** for backend services, authentication, and database

## Styling & UI
- **Tailwind CSS** with extensive custom design system
- **Framer Motion** for animations and transitions
- **Lucide React** for consistent iconography
- **Custom component library** in `src/components/ui/`

## Rich Text Editing
- **TipTap** editor with extensions:
  - Bubble menu, floating menu
  - Image handling and placeholders
  - Character count and starter kit
  - Custom nodes and extensions

## AI & External Services
- **Google Generative AI** for content generation
- **Unsplash API** for image suggestions
- **Document processing**: Mammoth (DOCX), PDF.js (PDF)
- **Content parsing**: Unified, Remark, Rehype ecosystem

## Testing & Development
- **Jest** with React Testing Library
- **Babel** for transpilation
- **ESLint** with React app configuration

## Common Commands

### Development
```bash
npm run dev          # Start development server
npm start           # Alternative start command
npm run serve       # Preview production build
```

### Build & Deploy
```bash
npm run build       # Production build with sourcemaps
```

### Testing
```bash
npm test           # Run tests
npm run test:watch # Run tests in watch mode
```

### Database
```bash
npm run db:setup   # Setup database schema
npm run db:test    # Test database connection
```

## Environment Variables
Required environment variables (stored in `.env`):
- `VITE_SUPABASE_URL` - Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Supabase anonymous key
- Additional API keys for external services

## Build Configuration
- Output directory: `build/` (not `dist/`)
- Chunk size warning limit: 2000kb
- Source maps enabled for production
- Host: `0.0.0.0` for development