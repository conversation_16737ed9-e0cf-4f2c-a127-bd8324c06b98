import React from 'react';

/**
 * LoadingScreen - Shows progress during document generation
 * Displays current step and progress bar
 */
const LoadingScreen = ({ progress, currentStep, documentTitle }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center px-6">
        {/* Logo/Icon */}
        <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-8">
          <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.53-1.053l-.548-.547z" />
          </svg>
        </div>

        {/* Title */}
        <h1 className="text-2xl md:text-3xl font-bold text-text-primary mb-2">
          Creating Your Document
        </h1>
        <h2 className="text-lg text-text-secondary mb-8">
          {documentTitle}
        </h2>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-text-secondary mb-2">
            <span>Progress</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-primary to-purple-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Current Step */}
        <div className="mb-8">
          <p className="text-text-secondary mb-4">{currentStep}</p>
          
          {/* Animated dots */}
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>

        {/* Steps List */}
        <div className="text-left bg-white/50 rounded-lg p-6 backdrop-blur-sm">
          <h3 className="font-semibold text-text-primary mb-4">Generation Steps:</h3>
          <div className="space-y-3">
            <div className={`flex items-center space-x-3 ${progress >= 20 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-5 h-5 rounded-full flex items-center justify-center ${progress >= 20 ? 'bg-green-100' : 'bg-gray-100'}`}>
                {progress >= 20 ? (
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <span className="text-xs">1</span>
                )}
              </div>
              <span className="text-sm">Analyzing your requirements</span>
            </div>
            
            <div className={`flex items-center space-x-3 ${progress >= 60 ? 'text-green-600' : progress >= 20 ? 'text-primary' : 'text-gray-400'}`}>
              <div className={`w-5 h-5 rounded-full flex items-center justify-center ${progress >= 60 ? 'bg-green-100' : progress >= 20 ? 'bg-primary/10' : 'bg-gray-100'}`}>
                {progress >= 60 ? (
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : progress >= 20 ? (
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                ) : (
                  <span className="text-xs">2</span>
                )}
              </div>
              <span className="text-sm">Generating content with AI</span>
            </div>
            
            <div className={`flex items-center space-x-3 ${progress >= 80 ? 'text-green-600' : progress >= 60 ? 'text-primary' : 'text-gray-400'}`}>
              <div className={`w-5 h-5 rounded-full flex items-center justify-center ${progress >= 80 ? 'bg-green-100' : progress >= 60 ? 'bg-primary/10' : 'bg-gray-100'}`}>
                {progress >= 80 ? (
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : progress >= 60 ? (
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                ) : (
                  <span className="text-xs">3</span>
                )}
              </div>
              <span className="text-sm">Finding relevant images</span>
            </div>
            
            <div className={`flex items-center space-x-3 ${progress >= 100 ? 'text-green-600' : progress >= 80 ? 'text-primary' : 'text-gray-400'}`}>
              <div className={`w-5 h-5 rounded-full flex items-center justify-center ${progress >= 100 ? 'bg-green-100' : progress >= 80 ? 'bg-primary/10' : 'bg-gray-100'}`}>
                {progress >= 100 ? (
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : progress >= 80 ? (
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                ) : (
                  <span className="text-xs">4</span>
                )}
              </div>
              <span className="text-sm">Preparing your editor</span>
            </div>
          </div>
        </div>

        {/* Tip */}
        <div className="mt-6 text-sm text-text-secondary">
          💡 <strong>Tip:</strong> Your document will be ready for editing in just a moment!
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
