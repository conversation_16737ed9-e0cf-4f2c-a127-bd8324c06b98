import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const DocumentOutline = ({ outline, onOutlineChange, isGenerating }) => {
  const [editingItem, setEditingItem] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [expandedItems, setExpandedItems] = useState(new Set([1, 2, 3]));

  const mockOutline = outline || [
    {
      id: 1,
      title: 'Introduction to Digital Marketing',
      type: 'chapter',
      level: 0,
      children: [
        { id: 11, title: 'What is Digital Marketing?', type: 'section', level: 1 },
        { id: 12, title: 'Evolution of Marketing', type: 'section', level: 1 },
        { id: 13, title: 'Key Benefits', type: 'section', level: 1 }
      ]
    },
    {
      id: 2,
      title: 'Search Engine Optimization',
      type: 'chapter',
      level: 0,
      children: [
        { id: 21, title: 'SEO Fundamentals', type: 'section', level: 1 },
        { id: 22, title: 'Keyword Research', type: 'section', level: 1 },
        { id: 23, title: 'On-Page Optimization', type: 'section', level: 1 }
      ]
    },
    {
      id: 3,
      title: 'Content Marketing Strategy',
      type: 'chapter',
      level: 0,
      children: [
        { id: 31, title: 'Content Planning', type: 'section', level: 1 },
        { id: 32, title: 'Content Creation', type: 'section', level: 1 },
        { id: 33, title: 'Content Distribution', type: 'section', level: 1 }
      ]
    },
    {
      id: 4,
      title: 'Social Media Marketing',
      type: 'chapter',
      level: 0,
      children: [
        { id: 41, title: 'Platform Selection', type: 'section', level: 1 },
        { id: 42, title: 'Content Strategy', type: 'section', level: 1 },
        { id: 43, title: 'Engagement Tactics', type: 'section', level: 1 }
      ]
    },
    {
      id: 5,
      title: 'Analytics and Measurement',
      type: 'chapter',
      level: 0,
      children: [
        { id: 51, title: 'Setting Up Analytics', type: 'section', level: 1 },
        { id: 52, title: 'Key Metrics', type: 'section', level: 1 },
        { id: 53, title: 'Reporting and Optimization', type: 'section', level: 1 }
      ]
    }
  ];

  const handleToggleExpand = (itemId) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const handleStartEdit = (item) => {
    setEditingItem(item.id);
    setEditValue(item.title);
  };

  const handleSaveEdit = () => {
    if (editValue.trim()) {
      console.log('Saving edit:', editingItem, editValue);
      // onOutlineChange would be called here
    }
    setEditingItem(null);
    setEditValue('');
  };

  const handleCancelEdit = () => {
    setEditingItem(null);
    setEditValue('');
  };

  const handleMoveUp = (item) => {
    console.log('Moving up:', item.title);
    // Implementation for reordering
  };

  const handleMoveDown = (item) => {
    console.log('Moving down:', item.title);
    // Implementation for reordering
  };

  const handleAddSection = (chapterId) => {
    console.log('Adding section to chapter:', chapterId);
    // Implementation for adding new section
  };

  const handleDeleteItem = (itemId) => {
    console.log('Deleting item:', itemId);
    // Implementation for deleting item
  };

  const renderOutlineItem = (item, index, siblings) => {
    const isExpanded = expandedItems.has(item.id);
    const isEditing = editingItem === item.id;
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id} className="group">
        <div className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-background transition-micro ${
          item.level === 0 ? 'font-medium' : 'ml-6'
        }`}>
          {/* Expand/Collapse Button */}
          {hasChildren && (
            <Button
              variant="ghost"
              onClick={() => handleToggleExpand(item.id)}
              className="w-6 h-6 p-0"
            >
              <Icon name={isExpanded ? "ChevronDown" : "ChevronRight"} size={14} />
            </Button>
          )}
          
          {!hasChildren && <div className="w-6" />}

          {/* Item Icon */}
          <div className={`p-1 rounded ${
            item.type === 'chapter' ? 'bg-primary/10' : 'bg-secondary/10'
          }`}>
            <Icon 
              name={item.type === 'chapter' ? "BookOpen" : "FileText"} 
              size={14}
              color={item.type === 'chapter' ? "var(--color-primary)" : "var(--color-secondary)"}
            />
          </div>

          {/* Title */}
          <div className="flex-1">
            {isEditing ? (
              <div className="flex items-center space-x-2">
                <Input
                  type="text"
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  className="flex-1 h-8 text-sm"
                  autoFocus
                />
                <Button
                  variant="success"
                  onClick={handleSaveEdit}
                  className="w-8 h-8 p-0"
                >
                  <Icon name="Check" size={12} />
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleCancelEdit}
                  className="w-8 h-8 p-0"
                >
                  <Icon name="X" size={12} />
                </Button>
              </div>
            ) : (
              <span className={`text-sm ${
                item.type === 'chapter' ? 'text-text-primary font-medium' : 'text-text-secondary'
              }`}>
                {item.title}
              </span>
            )}
          </div>

          {/* Action Buttons */}
          {!isEditing && (
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {item.type === 'chapter' && (
                <Button
                  variant="ghost"
                  onClick={() => handleAddSection(item.id)}
                  className="w-6 h-6 p-0"
                  title="Add section"
                >
                  <Icon name="Plus" size={12} />
                </Button>
              )}
              
              <Button
                variant="ghost"
                onClick={() => handleStartEdit(item)}
                className="w-6 h-6 p-0"
                title="Edit"
              >
                <Icon name="Edit2" size={12} />
              </Button>

              {index > 0 && (
                <Button
                  variant="ghost"
                  onClick={() => handleMoveUp(item)}
                  className="w-6 h-6 p-0"
                  title="Move up"
                >
                  <Icon name="ChevronUp" size={12} />
                </Button>
              )}

              {index < siblings.length - 1 && (
                <Button
                  variant="ghost"
                  onClick={() => handleMoveDown(item)}
                  className="w-6 h-6 p-0"
                  title="Move down"
                >
                  <Icon name="ChevronDown" size={12} />
                </Button>
              )}

              <Button
                variant="ghost"
                onClick={() => handleDeleteItem(item.id)}
                className="w-6 h-6 p-0 text-error hover:text-error"
                title="Delete"
              >
                <Icon name="Trash2" size={12} />
              </Button>
            </div>
          )}
        </div>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="ml-4">
            {item.children.map((child, childIndex) => 
              renderOutlineItem(child, childIndex, item.children)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-text-primary">Document Outline</h3>
          <Button
            variant="outline"
            onClick={() => console.log('Regenerate outline')}
            iconName="RefreshCw"
            className="text-xs px-2 py-1"
            disabled={isGenerating}
          >
            Regenerate
          </Button>
        </div>
        
        {isGenerating && (
          <div className="mt-3 p-3 bg-secondary/10 rounded-lg border border-secondary/20">
            <div className="flex items-center space-x-2">
              <div className="animate-spin">
                <Icon name="Loader2" size={16} color="var(--color-secondary)" />
              </div>
              <span className="text-sm text-secondary">Generating outline...</span>
            </div>
          </div>
        )}
      </div>

      {/* Outline Tree */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-1">
          {mockOutline.map((item, index) => 
            renderOutlineItem(item, index, mockOutline)
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="p-4 border-t border-border">
        <div className="space-y-2">
          <Button
            variant="primary"
            onClick={() => console.log('Add new chapter')}
            iconName="Plus"
            iconPosition="left"
            className="w-full"
          >
            Add Chapter
          </Button>
          
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              onClick={() => console.log('Export outline')}
              iconName="Download"
              className="text-xs"
            >
              Export
            </Button>
            <Button
              variant="outline"
              onClick={() => console.log('Import outline')}
              iconName="Upload"
              className="text-xs"
            >
              Import
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentOutline;