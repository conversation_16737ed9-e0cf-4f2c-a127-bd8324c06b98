import React, { useState } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

/**
 * Enhanced Content Preview Component
 * Shows extracted content with editing capabilities, metadata, and formatting
 */
const ContentPreview = ({ 
  extractedData, 
  onContentUpdate,
  onEdit,
  className = ""
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(extractedData?.extractedContent || '');
  const [editedTitle, setEditedTitle] = useState(extractedData?.originalTitle || '');

  if (!extractedData || !extractedData.extractedContent) {
    return null;
  }

  const wordCount = editedContent.split(/\s+/).filter(word => word.length > 0).length;
  const readingTime = Math.ceil(wordCount / 200); // Average reading speed: 200 words/minute
  const characterCount = editedContent.length;

  const handleSaveEdit = () => {
    const updatedData = {
      ...extractedData,
      extractedContent: editedContent,
      originalTitle: editedTitle,
      wordCount: wordCount
    };
    
    onContentUpdate?.(updatedData);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedContent(extractedData.extractedContent);
    setEditedTitle(extractedData.originalTitle);
    setIsEditing(false);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className={`bg-green-50 border border-green-200 rounded-lg p-6 space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-2">
          <Icon name="CheckCircle" size={20} className="text-green-600 flex-shrink-0 mt-0.5" />
          <h4 className="font-semibold text-green-800">Content Extracted Successfully</h4>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsEditing(!isEditing)}
          iconName={isEditing ? "X" : "Edit3"}
          iconPosition="left"
          className="text-green-700 hover:text-green-800"
        >
          {isEditing ? 'Cancel' : 'Edit'}
        </Button>
      </div>

      {/* Metadata */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div className="bg-white p-3 rounded border">
          <div className="text-gray-500 text-xs uppercase tracking-wide">Word Count</div>
          <div className="font-semibold text-gray-900">{wordCount.toLocaleString()}</div>
        </div>
        <div className="bg-white p-3 rounded border">
          <div className="text-gray-500 text-xs uppercase tracking-wide">Reading Time</div>
          <div className="font-semibold text-gray-900">{readingTime} min</div>
        </div>
        <div className="bg-white p-3 rounded border">
          <div className="text-gray-500 text-xs uppercase tracking-wide">Characters</div>
          <div className="font-semibold text-gray-900">{characterCount.toLocaleString()}</div>
        </div>
        <div className="bg-white p-3 rounded border">
          <div className="text-gray-500 text-xs uppercase tracking-wide">Source</div>
          <div className="font-semibold text-gray-900 truncate" title={extractedData.sourceUrl || extractedData.fileName || 'Unknown'}>
            {(() => {
              if (extractedData.sourceUrl) {
                try {
                  return new URL(extractedData.sourceUrl).hostname;
                } catch (error) {
                  return extractedData.sourceUrl;
                }
              }
              return extractedData.fileName || 'DOCX File';
            })()}
          </div>
        </div>
      </div>

      {/* Content Title */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">Title</label>
        {isEditing ? (
          <input
            type="text"
            value={editedTitle}
            onChange={(e) => setEditedTitle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Enter document title..."
          />
        ) : (
          <div className="bg-white p-3 rounded border">
            <h5 className="font-medium text-gray-900">{extractedData.originalTitle || 'Untitled Document'}</h5>
          </div>
        )}
      </div>

      {/* Additional Metadata */}
      {(extractedData.author || extractedData.publishDate) && (
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          {extractedData.author && (
            <div className="flex items-center space-x-1">
              <Icon name="User" size={14} />
              <span>By {extractedData.author}</span>
            </div>
          )}
          {extractedData.publishDate && (
            <div className="flex items-center space-x-1">
              <Icon name="Calendar" size={14} />
              <span>Published {formatDate(extractedData.publishDate)}</span>
            </div>
          )}
        </div>
      )}

      {/* Content Preview/Editor */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-700">Content</label>
          {!isEditing && (
            <div className="text-xs text-gray-500">
              Showing {Math.min(500, editedContent.length)} of {editedContent.length} characters
            </div>
          )}
        </div>
        
        {isEditing ? (
          <div className="space-y-3">
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="w-full h-64 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none"
              placeholder="Enter or edit the extracted content..."
            />
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{editedContent.length} characters, {wordCount} words</span>
              <span>~{readingTime} min read</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="primary"
                size="sm"
                onClick={handleSaveEdit}
                iconName="Save"
                iconPosition="left"
              >
                Save Changes
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancelEdit}
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="bg-white p-4 rounded border max-h-48 overflow-y-auto">
            <div className="text-gray-700 text-sm leading-relaxed whitespace-pre-wrap">
              {editedContent.length > 500 
                ? `${editedContent.substring(0, 500)}...` 
                : editedContent
              }
            </div>
            {editedContent.length > 500 && (
              <button
                onClick={() => setIsEditing(true)}
                className="mt-2 text-green-600 hover:text-green-800 text-sm font-medium"
              >
                View Full Content →
              </button>
            )}
          </div>
        )}
      </div>

      {/* Content Quality Indicators */}
      <div className="bg-white p-3 rounded border">
        <div className="text-sm font-medium text-gray-700 mb-2">Content Analysis</div>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Length</span>
            <span className={`font-medium ${
              wordCount < 500 ? 'text-yellow-600' : 
              wordCount < 2000 ? 'text-green-600' : 'text-blue-600'
            }`}>
              {wordCount < 500 ? 'Short' : 
               wordCount < 2000 ? 'Medium' : 'Long'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Complexity</span>
            <span className="font-medium text-gray-900">
              {wordCount > 2000 ? 'Comprehensive' : 'Standard'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Suggested Document Type</span>
            <span className="font-medium text-gray-900">
              {wordCount < 1000 ? 'Guide' : 
               wordCount < 3000 ? 'eBook' : 'Report'}
            </span>
          </div>
        </div>
      </div>

      {/* Source Information */}
      <div className="bg-blue-50 border border-blue-200 rounded p-3">
        <div className="flex items-start space-x-2">
          <Icon
            name={extractedData.sourceUrl ? "Link" : "FileText"}
            size={16}
            className="text-blue-600 mt-0.5 flex-shrink-0"
          />
          <div className="space-y-1">
            <div className="text-sm font-medium text-blue-800">
              {extractedData.sourceUrl ? "Source URL" : "Source File"}
            </div>
            {extractedData.sourceUrl ? (
              <a
                href={extractedData.sourceUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 break-all"
              >
                {extractedData.sourceUrl}
              </a>
            ) : (
              <div className="text-sm text-blue-600 break-all">
                {extractedData.fileName || 'DOCX Document'}
                {extractedData.fileSize && (
                  <span className="text-xs text-blue-500 ml-2">
                    ({(extractedData.fileSize / 1024).toFixed(1)} KB)
                  </span>
                )}
              </div>
            )}
            <div className="text-xs text-blue-600">
              Extracted on {formatDate(extractedData.extractedAt)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentPreview;
