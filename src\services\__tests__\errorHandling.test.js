/**
 * Unit tests for error handling and retry logic in DOCX export
 */

import {
    categorizeError,
    createUserFriendlyErrorMessage,
    isRetryableError,
    validateImageUrl,
    validateImageFormat,
    downloadImageWithRetry,
    processImages,
    createImageErrorSummary,
    generateImageErrorMessage,
    createDetailedErrorReport
} from '../errorHandlingService';

// Mock global fetch
global.fetch = jest.fn();

describe('Error Categorization', () => {
    test('should categorize timeout errors correctly', () => {
        const error = new Error('Request timed out');
        error.name = 'AbortError';
        expect(categorizeError(error)).toBe('timeout');
    });

    test('should categorize client errors correctly', () => {
        const error = new Error('Not found');
        error.status = 404;
        expect(categorizeError(error)).toBe('client_error');
    });

    test('should categorize server errors correctly', () => {
        const error = new Error('Server error');
        error.status = 500;
        expect(categorizeError(error)).toBe('server_error');
    });

    test('should categorize network errors correctly', () => {
        const error = new Error('Network error occurred');
        expect(categorizeError(error)).toBe('network_error');
    });

    test('should categorize validation errors correctly', () => {
        const error = new Error('Invalid URL format');
        expect(categorizeError(error)).toBe('validation_error');
    });

    test('should categorize size errors correctly', () => {
        const error = new Error('Image too large');
        expect(categorizeError(error)).toBe('size_error');
    });
});

describe('User-Friendly Error Messages', () => {
    test('should create user-friendly message for timeout errors', () => {
        const error = new Error('Request timed out');
        error.name = 'AbortError';
        const message = createUserFriendlyErrorMessage(error);
        expect(message).toContain('timed out');
    });

    test('should create user-friendly message for 404 errors', () => {
        const error = new Error('Not found');
        error.status = 404;
        const message = createUserFriendlyErrorMessage(error);
        expect(message).toContain('not found');
    });

    test('should create user-friendly message for server errors', () => {
        const error = new Error('Server error');
        error.status = 500;
        const message = createUserFriendlyErrorMessage(error);
        expect(message).toContain('server is experiencing problems');
    });
});

describe('Retry Logic', () => {
    test('should identify retryable errors correctly', () => {
        // Retryable errors
        const serverError = new Error('Server error');
        serverError.status = 500;
        expect(isRetryableError(serverError)).toBe(true);

        const networkError = new Error('Network error');
        expect(isRetryableError(networkError)).toBe(true);

        const timeoutError = new Error('Timeout');
        timeoutError.name = 'AbortError';
        expect(isRetryableError(timeoutError)).toBe(true);

        // Non-retryable errors
        const validationError = new Error('Invalid URL format');
        expect(isRetryableError(validationError)).toBe(false);

        const sizeError = new Error('Image too large');
        expect(isRetryableError(sizeError)).toBe(false);

        const clientError = new Error('Not found');
        clientError.status = 404;
        expect(isRetryableError(clientError)).toBe(false);
    });
});

describe('URL Validation', () => {
    test('should validate correct image URLs', () => {
        const result = validateImageUrl('https://example.com/image.jpg');
        expect(result.isValid).toBe(true);
    });

    test('should reject invalid URLs', () => {
        const result = validateImageUrl('not-a-url');
        expect(result.isValid).toBe(false);
    });

    test('should reject non-HTTP protocols', () => {
        const result = validateImageUrl('ftp://example.com/image.jpg');
        expect(result.isValid).toBe(false);
    });

    test('should handle empty URLs', () => {
        const result = validateImageUrl('');
        expect(result.isValid).toBe(false);
    });

    test('should handle null or undefined URLs', () => {
        expect(validateImageUrl(null).isValid).toBe(false);
        expect(validateImageUrl(undefined).isValid).toBe(false);
    });
});

describe('Image Format Validation', () => {
    test('should validate supported image formats', () => {
        expect(validateImageFormat('image/jpeg').isValid).toBe(true);
        expect(validateImageFormat('image/png').isValid).toBe(true);
        expect(validateImageFormat('image/gif').isValid).toBe(true);
    });

    test('should reject unsupported formats', () => {
        expect(validateImageFormat('image/svg+xml').isValid).toBe(false);
        expect(validateImageFormat('application/pdf').isValid).toBe(false);
    });

    test('should handle empty or invalid content types', () => {
        expect(validateImageFormat('').isValid).toBe(false);
        expect(validateImageFormat(null).isValid).toBe(false);
    });
});

describe('Image Error Summary', () => {
    test('should categorize failed images correctly', () => {
        const failedImages = [
            { error: 'Network error', src: 'https://example.com/1.jpg' },
            { error: 'Timeout', src: 'https://example.com/2.jpg' },
            { error: 'Image too large', src: 'https://example.com/3.jpg' },
            { error: 'Invalid URL format', src: 'https://example.com/4.jpg' },
            { error: 'HTTP 404: Not Found', status: 404, src: 'https://example.com/5.jpg' }
        ];

        const summary = createImageErrorSummary(failedImages);
        expect(summary.totalFailed).toBe(5);
        expect(summary.networkErrors.length).toBe(1);
        expect(summary.timeoutErrors.length).toBe(1);
        expect(summary.sizeErrors.length).toBe(1);
        expect(summary.formatErrors.length).toBe(1);
        expect(summary.accessErrors.length).toBe(1);
    });

    test('should generate user-friendly error message', () => {
        const imageStats = {
            totalImages: 10,
            successCount: 7,
            failureCount: 3,
            failedImages: [
                { error: 'Network error', src: 'https://example.com/1.jpg' },
                { error: 'Timeout', src: 'https://example.com/2.jpg' },
                { error: 'Image too large', src: 'https://example.com/3.jpg' }
            ]
        };

        const message = generateImageErrorMessage(imageStats);
        expect(message).toContain('3 of 10 images could not be processed');
        expect(message).toContain('network issues');
        expect(message).toContain('timeouts');
        expect(message).toContain('size limitations');
        expect(message).toContain('7 images');
    });

    test('should create detailed error report', () => {
        const imageStats = {
            totalImages: 5,
            successCount: 3,
            failureCount: 2,
            processingTime: 1500,
            failedImages: [
                { error: 'Network error', src: 'https://example.com/1.jpg' },
                { error: 'Image too large', src: 'https://example.com/2.jpg' }
            ]
        };

        const report = createDetailedErrorReport(imageStats);
        expect(report.hasErrors).toBe(true);
        expect(report.totalImages).toBe(5);
        expect(report.successCount).toBe(3);
        expect(report.failureCount).toBe(2);
        expect(report.processingTime).toBe(1500);
        expect(report.failedUrls.length).toBe(2);
        expect(report.userMessage).toContain('2 of 5 images could not be processed');
    });
});

// Mock implementation for downloadImageWithRetry tests
jest.mock('../errorHandlingService', () => {
    const originalModule = jest.requireActual('../errorHandlingService');

    return {
        ...originalModule,
        downloadImage: jest.fn()
    };
});

// These tests would need to be expanded in a real implementation
describe('Image Download with Retry', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('should handle successful downloads', async () => {
        // This is a simplified test - in a real implementation, you would mock
        // the downloadImage function to return success
    });

    test('should retry on retryable errors', async () => {
        // This is a simplified test - in a real implementation, you would mock
        // the downloadImage function to fail with retryable errors
    });

    test('should not retry on non-retryable errors', async () => {
        // This is a simplified test - in a real implementation, you would mock
        // the downloadImage function to fail with non-retryable errors
    });
});