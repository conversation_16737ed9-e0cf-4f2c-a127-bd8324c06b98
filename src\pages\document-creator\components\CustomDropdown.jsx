import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const CustomDropdown = ({ 
  value, 
  onChange, 
  options, 
  className = '',
  placeholder = 'Select...'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(
    options.find(option => option.id === value) || null
  );
  const dropdownRef = useRef(null);
  const triggerRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update selected option when value prop changes
  useEffect(() => {
    const option = options.find(option => option.id === value);
    setSelectedOption(prevOption => {
      // Only update if the option actually changed
      if (prevOption?.id !== option?.id) {
        return option || null;
      }
      return prevOption;
    });
  }, [value, options]);

  const handleOptionClick = (option) => {
    setSelectedOption(option);
    onChange(option.id);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`relative inline-block ${className}`} ref={dropdownRef}>
      {/* Dropdown Trigger */}
      <button
        ref={triggerRef}
        type="button"
        onClick={toggleDropdown}
        className="relative inline-block mx-1 px-1 py-1 border-0 border-b-2 border-primary bg-transparent text-primary font-semibold focus:outline-none focus:ring-0 hover:border-primary/80 transition-colors rounded-none text-lg md:text-xl cursor-pointer"
      >
        {selectedOption ? selectedOption.name : placeholder}
        <Icon 
          name={isOpen ? "ChevronUp" : "ChevronDown"} 
          size={16} 
          className="inline ml-1 transition-transform duration-200"
        />
      </button>

      {/* Dropdown Options - Designrr Style */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 min-w-[200px] bg-white border border-gray-100 rounded-lg shadow-xl z-50 overflow-hidden">
          {options.map((option) => (
            <button
              key={option.id}
              type="button"
              onClick={() => handleOptionClick(option)}
              className={`w-full text-left px-4 py-3 text-sm hover:bg-blue-50 transition-colors duration-200 border-b border-gray-50 last:border-b-0 ${
                selectedOption?.id === option.id
                  ? 'bg-blue-50 text-primary font-semibold'
                  : 'text-gray-700 hover:text-primary'
              }`}
            >
              <div className="flex items-center">
                {option.icon && (
                  <Icon name={option.icon} size={16} className="mr-3 text-gray-400" />
                )}
                <div>
                  <div className="font-medium">{option.name}</div>
                  {option.description && (
                    <div className="text-xs text-gray-500 mt-0.5">{option.description}</div>
                  )}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomDropdown;
