import { supabase } from '../lib/supabase'

class SessionManager {
  constructor() {
    this.refreshTimer = null
    this.sessionCheckInterval = null
    this.listeners = new Set()
  }

  // Add session event listener
  addListener(callback) {
    this.listeners.add(callback)
    return () => this.listeners.delete(callback)
  }

  // Notify all listeners of session changes
  notifyListeners(event, session) {
    this.listeners.forEach(callback => {
      try {
        callback(event, session)
      } catch (error) {
        console.error('Session listener error:', error)
      }
    })
  }

  // Start session monitoring
  startSessionMonitoring() {
    this.stopSessionMonitoring() // Clear any existing timers

    // Check session every 30 seconds
    this.sessionCheckInterval = setInterval(() => {
      this.checkSessionValidity()
    }, 30000)

    // Set up token refresh
    this.setupTokenRefresh()
  }

  // Stop session monitoring
  stopSessionMonitoring() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval)
      this.sessionCheckInterval = null
    }
  }

  // Check if current session is valid
  async checkSessionValidity() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.error('Session check error:', error)
        this.notifyListeners('SESSION_ERROR', null)
        return false
      }

      if (!session) {
        this.notifyListeners('SESSION_EXPIRED', null)
        return false
      }

      // Check if token is close to expiring (within 5 minutes)
      const expiresAt = session.expires_at * 1000 // Convert to milliseconds
      const now = Date.now()
      const timeUntilExpiry = expiresAt - now
      const fiveMinutes = 5 * 60 * 1000

      if (timeUntilExpiry <= fiveMinutes) {
        console.log('Token expiring soon, attempting refresh...')
        await this.refreshSession()
      }

      return true
    } catch (error) {
      console.error('Session validity check failed:', error)
      this.notifyListeners('SESSION_ERROR', null)
      return false
    }
  }

  // Set up automatic token refresh
  async setupTokenRefresh() {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) return

      const expiresAt = session.expires_at * 1000
      const now = Date.now()
      const timeUntilExpiry = expiresAt - now
      
      // Refresh 5 minutes before expiry
      const refreshTime = Math.max(timeUntilExpiry - (5 * 60 * 1000), 60000) // At least 1 minute

      this.refreshTimer = setTimeout(() => {
        this.refreshSession()
      }, refreshTime)

    } catch (error) {
      console.error('Token refresh setup failed:', error)
    }
  }

  // Refresh the current session
  async refreshSession() {
    try {
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Session refresh failed:', error)
        this.notifyListeners('SESSION_REFRESH_FAILED', null)
        return false
      }

      if (data.session) {
        console.log('Session refreshed successfully')
        this.notifyListeners('SESSION_REFRESHED', data.session)
        this.setupTokenRefresh() // Set up next refresh
        return true
      }

      return false
    } catch (error) {
      console.error('Session refresh error:', error)
      this.notifyListeners('SESSION_REFRESH_FAILED', null)
      return false
    }
  }

  // Force logout and clear session
  async forceLogout() {
    try {
      this.stopSessionMonitoring()
      await supabase.auth.signOut()
      this.notifyListeners('FORCED_LOGOUT', null)
    } catch (error) {
      console.error('Force logout error:', error)
    }
  }

  // Get session info
  async getSessionInfo() {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) return null

      const expiresAt = session.expires_at * 1000
      const now = Date.now()
      const timeUntilExpiry = expiresAt - now

      return {
        isValid: timeUntilExpiry > 0,
        expiresAt: new Date(expiresAt),
        timeUntilExpiry,
        user: session.user
      }
    } catch (error) {
      console.error('Get session info error:', error)
      return null
    }
  }

  // Store session metadata
  storeSessionMetadata(metadata) {
    try {
      localStorage.setItem('docforge_session_metadata', JSON.stringify({
        ...metadata,
        timestamp: Date.now()
      }))
    } catch (error) {
      console.error('Store session metadata error:', error)
    }
  }

  // Get stored session metadata
  getSessionMetadata() {
    try {
      const stored = localStorage.getItem('docforge_session_metadata')
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.error('Get session metadata error:', error)
      return null
    }
  }

  // Clear session metadata
  clearSessionMetadata() {
    try {
      localStorage.removeItem('docforge_session_metadata')
    } catch (error) {
      console.error('Clear session metadata error:', error)
    }
  }

  // Track login activity
  async trackLoginActivity(ipAddress, userAgent) {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return

      // Store login metadata
      this.storeSessionMetadata({
        loginTime: new Date().toISOString(),
        ipAddress,
        userAgent,
        deviceInfo: this.getDeviceInfo()
      })

      // You could also call a database function here to log the activity
      // await supabase.rpc('update_login_stats', {
      //   user_uuid: session.user.id,
      //   ip_addr: ipAddress
      // })

    } catch (error) {
      console.error('Track login activity error:', error)
    }
  }

  // Get device information
  getDeviceInfo() {
    const userAgent = navigator.userAgent
    const platform = navigator.platform
    const language = navigator.language
    
    return {
      userAgent,
      platform,
      language,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  }

  // Check for suspicious activity
  checkSuspiciousActivity() {
    const metadata = this.getSessionMetadata()
    if (!metadata) return false

    const now = Date.now()
    const sessionAge = now - metadata.timestamp
    const maxSessionAge = 24 * 60 * 60 * 1000 // 24 hours

    // Session too old
    if (sessionAge > maxSessionAge) {
      console.warn('Session age exceeded maximum allowed time')
      return true
    }

    // Add more suspicious activity checks here
    // - Multiple failed login attempts
    // - Login from unusual location
    // - Unusual access patterns

    return false
  }
}

// Create singleton instance
export const sessionManager = new SessionManager()

// Session storage utilities
export const sessionStorage = {
  // Store encrypted session data
  store: (key, data) => {
    try {
      const encrypted = btoa(JSON.stringify(data))
      localStorage.setItem(`docforge_${key}`, encrypted)
    } catch (error) {
      console.error('Session storage error:', error)
    }
  },

  // Retrieve and decrypt session data
  retrieve: (key) => {
    try {
      const encrypted = localStorage.getItem(`docforge_${key}`)
      if (!encrypted) return null
      return JSON.parse(atob(encrypted))
    } catch (error) {
      console.error('Session retrieval error:', error)
      return null
    }
  },

  // Remove session data
  remove: (key) => {
    try {
      localStorage.removeItem(`docforge_${key}`)
    } catch (error) {
      console.error('Session removal error:', error)
    }
  },

  // Clear all session data
  clear: () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('docforge_'))
      keys.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.error('Session clear error:', error)
    }
  }
}

export default sessionManager
