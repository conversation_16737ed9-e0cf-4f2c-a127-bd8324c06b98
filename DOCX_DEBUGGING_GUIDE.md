# DOCX Import Debugging Guide

## Overview
This guide helps troubleshoot DOCX import functionality issues in DocForge AI.

## Step-by-Step Debugging Process

### 1. Check Browser Console
Open browser developer tools (F12) and check the Console tab for errors:

```javascript
// Look for these types of errors:
- "DOCX extraction error:"
- "Error parsing DOCX content:"
- "Mammoth library test result:"
- Network errors or failed imports
```

### 2. Verify Mammoth Library
The mammoth library test runs automatically when the component loads. Check console for:

```javascript
// Success message:
"Mammoth library test result: {success: true, message: '...'}"

// Failure message:
"Mammoth library test result: {success: false, error: '...'}"
```

### 3. Test File Requirements
Ensure your DOCX file meets these requirements:

- **Format**: .docx (Microsoft Word 2007+)
- **Size**: Under 10MB
- **Content**: Not empty
- **Status**: Not password-protected
- **Integrity**: Not corrupted

### 4. Debug Information in Modal
When extraction fails, the error modal now shows:
- Error type and message
- Original error details
- Stack trace (first 3 lines)
- File information

### 5. Console Logging
The enhanced service now logs detailed information:

```javascript
// File validation
"Starting DOCX extraction for file: filename.docx Size: 12345"
"File validation passed, converting to array buffer..."

// Mammoth processing
"Array buffer created, size: 12345"
"Extracting content with mammoth..."
"Mammoth extraction completed. Text length: 500 HTML length: 1200"

// Content parsing
"Parsing DOCX content. Raw text length: 500 HTML length: 1200"
"Cleaned text length: 480"
"Extracted title: Document Title"
"Word count: 85"
"Parse completed successfully: {...}"
```

## Common Issues and Solutions

### Issue 1: "Mammoth library not available"
**Cause**: mammoth.js not properly installed or imported
**Solution**: 
```bash
npm install mammoth
# Restart development server
npm run dev
```

### Issue 2: "Failed to read the DOCX file"
**Cause**: File corruption or invalid format
**Solutions**:
- Try opening the file in Microsoft Word
- Save the file again as .docx format
- Check file is not password-protected

### Issue 3: "No content could be extracted"
**Cause**: Empty document or unsupported content
**Solutions**:
- Ensure document contains text content
- Try a simple text document first
- Check for complex formatting that might cause issues

### Issue 4: "Invalid file format"
**Cause**: File is not a valid .docx file
**Solutions**:
- Ensure file extension is .docx (not .doc)
- Check file was saved in correct format
- Try creating a new simple .docx file

## Testing Files

### Create Test Files
Create these test files to verify functionality:

1. **Simple Text Document**
   - Create new Word document
   - Add title: "Test Document"
   - Add paragraph: "This is a test paragraph."
   - Save as test-simple.docx

2. **Document with Headings**
   - Create new Word document
   - Add Heading 1: "Main Title"
   - Add Heading 2: "Subtitle"
   - Add normal text paragraphs
   - Save as test-headings.docx

3. **Longer Document**
   - Create document with 500+ words
   - Include multiple paragraphs
   - Add some headings
   - Save as test-long.docx

### Test Procedure
1. Try uploading each test file
2. Check console for detailed logging
3. Verify extraction success/failure
4. Review error messages if any

## Advanced Debugging

### Enable Verbose Logging
The service now includes comprehensive logging. To see all logs:
1. Open browser console
2. Upload a DOCX file
3. Review the step-by-step logging output

### Check Network Tab
In browser dev tools, check Network tab for:
- Failed resource loading
- CORS errors
- Large file upload issues

### Verify File Content
To manually check file content:
```javascript
// In browser console after file selection:
const file = document.querySelector('input[type="file"]').files[0];
console.log('File details:', {
  name: file.name,
  size: file.size,
  type: file.type,
  lastModified: new Date(file.lastModified)
});
```

## Error Codes and Messages

### Validation Errors
- "No file provided for extraction"
- "Invalid file type. Only .docx files are supported"
- "File size too large. Maximum size is 10MB"
- "File is empty. Please select a valid DOCX file"

### Processing Errors
- "Mammoth library not available"
- "No content could be extracted from the DOCX file"
- "Failed to read the DOCX file. The file may be corrupted"
- "Failed to process the DOCX file"

### Parsing Errors
- "No content to parse from DOCX file"
- "Error parsing DOCX content"
- "Error parsing HTML structure"

## Support Information

If issues persist after following this guide:

1. **Collect Information**:
   - Browser and version
   - File details (size, source application)
   - Complete console error messages
   - Steps to reproduce

2. **Test Environment**:
   - Try different browsers
   - Test with different DOCX files
   - Check if issue is file-specific

3. **Fallback Options**:
   - Copy/paste content manually
   - Use URL import if content is available online
   - Start from scratch and reference the document

## Quick Fixes

### Clear Browser Cache
```bash
# Hard refresh
Ctrl + F5 (Windows)
Cmd + Shift + R (Mac)
```

### Restart Development Server
```bash
# Stop server (Ctrl+C) then:
npm run dev
```

### Verify Dependencies
```bash
npm list mammoth
# Should show: mammoth@x.x.x
```
