import React from 'react';
import TemplateCard from './TemplateCard';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const TemplateGrid = ({ 
  templates, 
  loading, 
  onSelectTemplate, 
  onPreviewTemplate, 
  onFavoriteTemplate,
  onLoadMore,
  hasMore,
  sortBy,
  onSortChange
}) => {
  const sortOptions = [
    { value: 'popular', label: 'Most Popular', icon: 'TrendingUp' },
    { value: 'recent', label: 'Recently Added', icon: 'Clock' },
    { value: 'rating', label: 'Highest Rated', icon: 'Star' },
    { value: 'alphabetical', label: 'A-Z', icon: 'ArrowUpDown' },
    { value: 'credits', label: 'Credits (Low to High)', icon: 'Zap' }
  ];

  if (loading && templates.length === 0) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading templates...</p>
        </div>
      </div>
    );
  }

  if (!loading && templates.length === 0) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center max-w-md">
          <Icon name="Search" size={48} className="mx-auto text-text-secondary mb-4" />
          <h3 className="text-lg font-semibold text-text-primary mb-2">No templates found</h3>
          <p className="text-text-secondary mb-6">
            Try adjusting your filters or search terms to find the perfect template.
          </p>
          <Button variant="primary">
            <Icon name="RotateCcw" size={16} />
            Clear Filters
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Sort Controls */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-text-secondary">
            {templates.length} template{templates.length !== 1 ? 's' : ''} found
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm text-text-secondary">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => onSortChange(e.target.value)}
            className="text-sm border border-border rounded-lg px-3 py-1.5 bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {templates.map((template) => (
          <TemplateCard
            key={template.id}
            template={template}
            onSelect={onSelectTemplate}
            onPreview={onPreviewTemplate}
            onFavorite={onFavoriteTemplate}
          />
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="flex justify-center mt-12">
          <Button
            variant="outline"
            onClick={onLoadMore}
            disabled={loading}
            className="px-8"
          >
            {loading ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                Loading...
              </>
            ) : (
              <>
                <Icon name="ChevronDown" size={16} />
                Load More Templates
              </>
            )}
          </Button>
        </div>
      )}

      {/* Loading Overlay for Additional Templates */}
      {loading && templates.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-6">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="bg-surface border border-border rounded-lg overflow-hidden animate-pulse">
              <div className="h-48 bg-background"></div>
              <div className="p-4">
                <div className="h-4 bg-background rounded mb-2"></div>
                <div className="h-3 bg-background rounded w-2/3 mb-4"></div>
                <div className="h-3 bg-background rounded mb-2"></div>
                <div className="h-3 bg-background rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TemplateGrid;