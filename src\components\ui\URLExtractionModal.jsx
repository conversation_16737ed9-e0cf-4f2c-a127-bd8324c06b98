import React from 'react';
import Icon from '../AppIcon';
import Button from './Button';

/**
 * Enhanced modal for URL extraction results and error handling
 * Provides informative feedback for both success and failure cases
 */
const URLExtractionModal = ({ 
  isOpen, 
  onClose, 
  type = 'error', // 'error', 'success', 'loading'
  title,
  message,
  extractedData = null,
  onRetry = null,
  onContinue = null,
  onTryTestUrl = null
}) => {
  if (!isOpen) return null;

  const getModalIcon = () => {
    switch (type) {
      case 'success':
        return <Icon name="CheckCircle" size={48} className="text-green-500" />;
      case 'loading':
        return <Icon name="Loader2" size={48} className="text-blue-500 animate-spin" />;
      case 'error':
      default:
        return <Icon name="AlertCircle" size={48} className="text-red-500" />;
    }
  };

  const getModalColor = () => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'loading':
        return 'border-blue-200 bg-blue-50';
      case 'error':
      default:
        return 'border-red-200 bg-red-50';
    }
  };

  const renderErrorContent = () => {
    const isNetworkError = message?.includes('Failed to fetch') || message?.includes('network');
    const isContentError = message?.includes('too short') || message?.includes('No content found');
    const isProxyError = message?.includes('internal error') || message?.includes('proxy');

    return (
      <div className="space-y-6">
        {/* Error Explanation */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            {getModalIcon()}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {title || 'Content Extraction Failed'}
            </h3>
            <p className="text-gray-600">
              {message || 'Unable to extract content from the provided URL.'}
            </p>
          </div>
        </div>

        {/* Specific Error Guidance */}
        {isNetworkError && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon name="Wifi" size={20} className="text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Network Issue Detected</h4>
                <p className="text-sm text-gray-700">
                  This appears to be a network connectivity issue. Please check:
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Your internet connection is stable</li>
                  <li>• The website URL is accessible in your browser</li>
                  <li>• Try again in a few moments</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {isContentError && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon name="FileText" size={20} className="text-orange-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Content Issue</h4>
                <p className="text-sm text-gray-700">
                  The webpage doesn't contain enough extractable content. This might happen with:
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Pages that load content dynamically with JavaScript</li>
                  <li>• Pages with mostly images or videos</li>
                  <li>• Login-protected or paywall content</li>
                  <li>• Landing pages with minimal text</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {isProxyError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon name="Server" size={20} className="text-red-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Service Limitation</h4>
                <p className="text-sm text-gray-700">
                  Our content extraction service is currently experiencing issues. This can happen when:
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• The target website blocks automated access</li>
                  <li>• Free proxy services are overloaded</li>
                  <li>• The website requires special authentication</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Test URL Suggestion */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Icon name="Lightbulb" size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Try Our Test URLs</h4>
              <p className="text-sm text-gray-700">
                These URLs will return sample content to demonstrate the workflow:
              </p>
              <div className="space-y-1">
                <code className="block text-xs bg-white px-2 py-1 rounded border cursor-pointer hover:bg-gray-50"
                      onClick={() => onTryTestUrl?.('https://example.com/test-article')}>
                  https://example.com/test-article
                </code>
                <code className="block text-xs bg-white px-2 py-1 rounded border cursor-pointer hover:bg-gray-50"
                      onClick={() => onTryTestUrl?.('https://test.com/blog-post')}>
                  https://test.com/blog-post
                </code>
              </div>
            </div>
          </div>
        </div>

        {/* Alternative Options */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Alternative Options</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Copy and paste content manually into the document editor</li>
            <li>• Start from scratch and create original content</li>
            <li>• Upload a DOCX file instead</li>
            <li>• Try a different blog post or article URL</li>
          </ul>
        </div>
      </div>
    );
  };

  const renderSuccessContent = () => (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          {getModalIcon()}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Content Extracted Successfully!
          </h3>
          <p className="text-gray-600">
            We've successfully extracted content from your URL.
          </p>
        </div>
      </div>

      {extractedData && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 space-y-3">
          <h4 className="font-medium text-gray-900">Extracted Content Summary</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Title:</span>
              <span className="font-medium">{extractedData.originalTitle || 'Untitled'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Word Count:</span>
              <span className="font-medium">{extractedData.wordCount || 0} words</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Reading Time:</span>
              <span className="font-medium">~{Math.ceil((extractedData.wordCount || 0) / 200)} min</span>
            </div>
          </div>
          {extractedData.extractedContent && (
            <div className="mt-3">
              <p className="text-gray-600 text-xs mb-1">Content Preview:</p>
              <div className="bg-white p-3 rounded border text-sm text-gray-700 max-h-24 overflow-y-auto">
                {extractedData.extractedContent.substring(0, 200)}
                {extractedData.extractedContent.length > 200 && '...'}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Icon name="ArrowRight" size={20} className="text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-gray-900">Next Steps</h4>
            <p className="text-sm text-gray-700 mt-1">
              The extracted content will be used to enhance your document creation process. 
              Continue with the wizard to customize your document.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLoadingContent = () => (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          {getModalIcon()}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Extracting Content...
          </h3>
          <p className="text-gray-600">
            Please wait while we extract content from your URL.
          </p>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-700">Progress</span>
            <span className="text-blue-600 font-medium">Processing...</span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>
          <p className="text-xs text-gray-600">
            This may take 10-30 seconds depending on the website and content size.
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            URL Content Extraction
          </h2>
          {type !== 'loading' && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Icon name="X" size={20} className="text-gray-500" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {type === 'error' && renderErrorContent()}
          {type === 'success' && renderSuccessContent()}
          {type === 'loading' && renderLoadingContent()}
        </div>

        {/* Footer Actions */}
        {type !== 'loading' && (
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            {type === 'error' && (
              <>
                {onTryTestUrl && (
                  <Button
                    variant="outline"
                    onClick={onTryTestUrl}
                    iconName="Lightbulb"
                    iconPosition="left"
                  >
                    Try Test URL
                  </Button>
                )}
                {onRetry && (
                  <Button
                    variant="outline"
                    onClick={onRetry}
                    iconName="RefreshCw"
                    iconPosition="left"
                  >
                    Try Again
                  </Button>
                )}
                <Button
                  variant="primary"
                  onClick={onClose}
                >
                  Continue Anyway
                </Button>
              </>
            )}
            
            {type === 'success' && (
              <>
                <Button
                  variant="outline"
                  onClick={onClose}
                >
                  Review Content
                </Button>
                {onContinue && (
                  <Button
                    variant="primary"
                    onClick={onContinue}
                    iconName="ArrowRight"
                    iconPosition="right"
                  >
                    Continue to Next Step
                  </Button>
                )}
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default URLExtractionModal;
