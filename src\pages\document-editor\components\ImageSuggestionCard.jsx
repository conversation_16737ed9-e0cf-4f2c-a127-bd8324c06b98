import React, { useState } from 'react';
import InlineImageGallery from './InlineImageGallery';
import Button from '../../../components/ui/Button';

/**
 * ImageSuggestionCard - Inline card component for contextual image suggestions
 * 
 * This component renders as an inline card within document content at AI-determined
 * optimal locations. It matches the provided design mockup with:
 * - Gallery icon on the left
 * - "Image suggestions available" text with context
 * - Blue "View Images" button on the right
 * - Hover states and responsive design
 */
const ImageSuggestionCard = (props) => {
  // Debug: Check for any malformed props
  if (process.env.NODE_ENV === 'development') {
    const expectedProps = [
      'chapterId', 'placementId', 'imageCount', 'searchQuery', 'chapterTitle',
      'placement', 'onViewImages', 'onImageSelect', 'onClose', 'images',
      'useInlineGallery', 'className'
    ];
    const unexpectedProps = Object.keys(props).filter(key => !expectedProps.includes(key));
    if (unexpectedProps.length > 0) {
      console.warn('🚨 ImageSuggestionCard received unexpected props:', unexpectedProps, props);
    }
  }

  // Safely destructure props
  const {
    chapterId,
    placementId,
    imageCount = 0,
    searchQuery = '',
    chapterTitle = '',
    placement = null,
    onViewImages,
    onImageSelect, // New: Direct image selection handler
    onClose, // New: Close card handler
    images = [], // New: Array of image objects
    useInlineGallery = true, // New: Toggle between inline gallery and modal
    className = ''
  } = props;
  const [isHovered, setIsHovered] = useState(false);

  const handleViewImages = () => {
    if (onViewImages) {
      onViewImages(chapterId, placementId, placement);
    }
  };

  // Don't render if no images available
  if (imageCount === 0) {
    return null;
  }

  // Simple consistent styling for chapter-boundary cards
  const styles = {
    borderColor: 'border-gray-200',
    bgColor: 'bg-white',
    iconBg: 'bg-blue-100',
    iconColor: 'text-blue-600'
  };

  // Use inline gallery if enabled and images are available
  if (useInlineGallery && images.length > 0) {
    return (
      <div
        className={`image-suggestion-card my-4 ${className}`}
        data-chapter-id={chapterId}
        data-placement-id={placementId}
      >
        <InlineImageGallery
          images={images}
          chapterId={chapterId}
          placementId={placementId}
          searchQuery={searchQuery}
          chapterTitle={chapterTitle}
          onImageSelect={onImageSelect}
          onClose={onClose}
          maxImages={3}
        />
      </div>
    );
  }

  // Fallback to original modal-trigger card design
  return (
    <div
      className={`image-suggestion-card my-4 ${className}`}
      data-chapter-id={chapterId}
      data-placement-id={placementId}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={`
        flex items-center justify-between p-4 ${styles.bgColor} border ${styles.borderColor} rounded-lg
        transition-all duration-200 hover:shadow-md relative
        ${isHovered ? 'shadow-md' : 'shadow-sm'}
      `}>
        {/* Left Section: Icon and Text */}
        <div className="flex items-center space-x-3">
          {/* Gallery Icon */}
          <div className={`w-10 h-10 ${styles.iconBg} rounded-lg flex items-center justify-center flex-shrink-0`}>
            <svg
              className={`w-5 h-5 ${styles.iconColor}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
              />
            </svg>
          </div>

          {/* Text Content */}
          <div className="min-w-0 flex-1">
            <h4 className="font-medium text-gray-900 text-sm sm:text-base">
              Image suggestions available
            </h4>
            <p className="text-sm text-gray-600 truncate">
              {imageCount} image{imageCount !== 1 ? 's' : ''} found for "{searchQuery}"
            </p>
          </div>
        </div>

        {/* Right Section: View Images Button */}
        <div className="flex-shrink-0 ml-4">
          <button
            onClick={handleViewImages}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label={`View ${imageCount} image suggestions for ${searchQuery}`}
          >
            {/* Eye Icon */}
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
            <span className="hidden sm:inline">View Images</span>
            <span className="sm:hidden">View</span>
          </button>
        </div>


      </div>

      {/* Optional: Contextual hint for placement */}
      {placement?.contextualHint && (
        <div className="mt-2 px-4">
          <p className="text-xs text-gray-500 italic">
            💡 {placement.contextualHint}
          </p>
        </div>
      )}
    </div>
  );
};

export default ImageSuggestionCard;
