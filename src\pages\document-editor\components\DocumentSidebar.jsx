import React, { useState } from 'react';
import Button from '../../../components/ui/Button';

/**
 * DocumentSidebar - Navigation and tools sidebar for document editor
 * Shows document structure, image suggestions, and export options
 */
const DocumentSidebar = ({ 
  documentData, 
  generatedContent, 
  imageSuggestions, 
  onImagePlace, 
  onExport 
}) => {
  const [activeTab, setActiveTab] = useState('structure');
  const [selectedChapter, setSelectedChapter] = useState(null);

  const tabs = [
    { id: 'structure', name: 'Structure', icon: 'List' },
    { id: 'images', name: 'Images', icon: 'Image' },
    { id: 'export', name: 'Export', icon: 'Download' }
  ];

  const scrollToChapter = (chapterId) => {
    const element = document.getElementById(chapterId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const renderStructureTab = () => (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        <div className="flex justify-between">
          <span>Word Count:</span>
          <span className="font-medium">{generatedContent?.wordCount?.toLocaleString() || 0}</span>
        </div>
        <div className="flex justify-between">
          <span>Reading Time:</span>
          <span className="font-medium">{generatedContent?.estimatedReadingTime || '0 min'}</span>
        </div>
      </div>

      <div className="border-t pt-4">
        <h3 className="font-semibold text-gray-900 mb-3">Document Structure</h3>
        
        {/* Introduction */}
        <div 
          className="p-3 rounded-lg border border-gray-200 hover:border-primary/50 cursor-pointer transition-colors mb-2"
          onClick={() => scrollToChapter('introduction')}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="font-medium text-gray-900">Introduction</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {generatedContent?.introduction?.wordCount || 0} words
          </div>
        </div>

        {/* Chapters */}
        {generatedContent?.chapters?.map((chapter) => (
          <div 
            key={chapter.id}
            className="p-3 rounded-lg border border-gray-200 hover:border-primary/50 cursor-pointer transition-colors mb-2"
            onClick={() => scrollToChapter(chapter.id)}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span className="font-medium text-gray-900">
                Chapter {chapter.number}: {chapter.title}
              </span>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {chapter.wordCount || 0} words • {chapter.sections?.length || 0} sections
            </div>
          </div>
        ))}

        {/* Conclusion */}
        <div 
          className="p-3 rounded-lg border border-gray-200 hover:border-primary/50 cursor-pointer transition-colors"
          onClick={() => scrollToChapter('conclusion')}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="font-medium text-gray-900">Conclusion</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {generatedContent?.conclusion?.wordCount || 0} words
          </div>
        </div>
      </div>
    </div>
  );

  const renderImagesTab = () => (
    <div className="space-y-4">
      <div className="text-sm text-gray-600 mb-4">
        {Object.keys(imageSuggestions).length > 0 
          ? `${Object.keys(imageSuggestions).length} chapters with image suggestions`
          : 'No image suggestions available'
        }
      </div>

      {Object.entries(imageSuggestions).map(([chapterId, suggestions]) => (
        <div key={chapterId} className="border rounded-lg p-3">
          <h4 className="font-medium text-gray-900 mb-2">
            {suggestions.chapterTitle}
          </h4>
          <div className="text-xs text-gray-500 mb-3">
            Search: "{suggestions.searchQuery}"
          </div>
          
          <div className="space-y-2">
            {suggestions.images?.slice(0, 3).map((image, index) => (
              <div key={image.id} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50">
                <img 
                  src={image.thumbnailUrl} 
                  alt={image.description}
                  className="w-12 h-8 object-cover rounded"
                />
                <div className="flex-1 min-w-0">
                  <div className="text-xs font-medium text-gray-900 truncate">
                    {image.description}
                  </div>
                  <div className="text-xs text-gray-500">
                    by {image.photographer}
                  </div>
                </div>
                <Button
                  size="xs"
                  variant="secondary"
                  onClick={() => onImagePlace(chapterId, image, 'top')}
                >
                  Add
                </Button>
              </div>
            ))}
          </div>
        </div>
      ))}

      {Object.keys(imageSuggestions).length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-sm">No images available</p>
          <p className="text-xs">Enable "Add images" in the questionnaire to see suggestions</p>
        </div>
      )}
    </div>
  );

  const renderExportTab = () => (
    <div className="space-y-4">
      <div className="text-sm text-gray-600 mb-4">
        Export your document in various formats
      </div>

      <div className="space-y-3">
        <Button
          variant="primary"
          className="w-full justify-start"
          iconName="FileText"
          iconPosition="left"
          onClick={() => onExport('pdf')}
        >
          Export as PDF
        </Button>
        
        <Button
          variant="secondary"
          className="w-full justify-start"
          iconName="FileText"
          iconPosition="left"
          onClick={() => onExport('docx')}
        >
          Export as Word
        </Button>
        
        <Button
          variant="secondary"
          className="w-full justify-start"
          iconName="Code"
          iconPosition="left"
          onClick={() => onExport('html')}
        >
          Export as HTML
        </Button>
        
        <Button
          variant="secondary"
          className="w-full justify-start"
          iconName="Type"
          iconPosition="left"
          onClick={() => onExport('txt')}
        >
          Export as Text
        </Button>
      </div>

      <div className="border-t pt-4 mt-6">
        <h4 className="font-medium text-gray-900 mb-3">Document Info</h4>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex justify-between">
            <span>Created:</span>
            <span>{new Date(generatedContent?.metadata?.generatedAt || Date.now()).toLocaleDateString()}</span>
          </div>
          <div className="flex justify-between">
            <span>Topic:</span>
            <span className="truncate ml-2">{documentData?.topicAndNiche?.mainTopic || 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span>Audience:</span>
            <span className="truncate ml-2">{documentData?.audienceAnalysis?.primaryAudience || 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span>Tone:</span>
            <span className="truncate ml-2">{documentData?.toneAndVoice?.toneOfVoice || 'N/A'}</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="font-semibold text-gray-900 truncate">
          {generatedContent?.title || 'Document Editor'}
        </h2>
        <p className="text-sm text-gray-500">
          {generatedContent?.metadata?.documentType || 'Document'}
        </p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 px-3 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'structure' && renderStructureTab()}
        {activeTab === 'images' && renderImagesTab()}
        {activeTab === 'export' && renderExportTab()}
      </div>
    </div>
  );
};

export default DocumentSidebar;
