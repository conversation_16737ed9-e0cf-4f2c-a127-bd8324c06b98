import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import Button from '../ui/Button'
import Input from '../ui/Input'
import Icon from '../AppIcon'

const RegisterForm = ({ onSwitchToLogin }) => {
  const { signUp, loading, error, clearError } = useAuth()
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    userType: '',
    agreeToTerms: false
  })
  const [formErrors, setFormErrors] = useState({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const userTypes = [
    { value: 'student', label: 'Student' },
    { value: 'educator', label: 'Educator' },
    { value: 'researcher', label: 'Researcher' },
    { value: 'business', label: 'Business Professional' },
    { value: 'entrepreneur', label: 'Entrepreneur' },
    { value: 'content_creator', label: 'Content Creator' }
  ]

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
    
    // Clear global error
    if (error) {
      clearError()
    }
  }

  const validateForm = () => {
    const errors = {}
    
    if (!formData.fullName.trim()) {
      errors.fullName = 'Full name is required'
    }
    
    if (!formData.email) {
      errors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }
    
    if (!formData.password) {
      errors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters long'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      errors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    }
    
    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }
    
    if (!formData.userType) {
      errors.userType = 'Please select your user type'
    }
    
    if (!formData.agreeToTerms) {
      errors.agreeToTerms = 'You must agree to the terms and conditions'
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    const userData = {
      full_name: formData.fullName,
      user_type: formData.userType
    }

    try {
      const result = await signUp(formData.email, formData.password, userData)

      if (result.success) {
        // Check if user is immediately confirmed (email verification disabled)
        if (result.data?.user && !result.data.user.email_confirmed_at) {
          // Expected with email verification disabled
        }

        // Check if user is logged in immediately
        if (result.data?.session) {
          // User will be redirected by the auth context
        } else {
          alert('Registration successful! You can now sign in with your credentials.')
          // Switch to login form
          if (onSwitchToLogin) {
            onSwitchToLogin()
          }
        }
      } else {
        console.error('❌ Registration failed:', result.error)
        alert(`Registration failed: ${result.error}`)
      }
    } catch (error) {
      console.error('💥 Registration error:', error)
      alert(`Registration error: ${error.message}`)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-surface rounded-2xl border border-border p-8 shadow-card">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-primary to-primary/80 rounded-2xl flex items-center justify-center">
              <Icon name="UserPlus" size={32} color="white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-text-primary mb-2">Create your account</h1>
          <p className="text-text-secondary">Join DocForge AI and start creating amazing documents</p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-error/10 border border-error/20 rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="AlertCircle" size={16} color="var(--color-error)" />
              <p className="text-sm text-error">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-text-primary mb-2">
              Full Name
            </label>
            <Input
              id="fullName"
              name="fullName"
              type="text"
              value={formData.fullName}
              onChange={handleChange}
              placeholder="Enter your full name"
              className={formErrors.fullName ? 'border-error focus:ring-error' : ''}
              disabled={loading}
            />
            {formErrors.fullName && (
              <p className="mt-1 text-sm text-error">{formErrors.fullName}</p>
            )}
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-text-primary mb-2">
              Email address
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              className={formErrors.email ? 'border-error focus:ring-error' : ''}
              disabled={loading}
            />
            {formErrors.email && (
              <p className="mt-1 text-sm text-error">{formErrors.email}</p>
            )}
          </div>

          <div>
            <label htmlFor="userType" className="block text-sm font-medium text-text-primary mb-2">
              I am a...
            </label>
            <select
              id="userType"
              name="userType"
              value={formData.userType}
              onChange={handleChange}
              className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm ${formErrors.userType ? 'border-error focus:ring-error' : ''}`}
              disabled={loading}
            >
              <option value="">Select your role</option>
              {userTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {formErrors.userType && (
              <p className="mt-1 text-sm text-error">{formErrors.userType}</p>
            )}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-text-primary mb-2">
              Password
            </label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleChange}
                placeholder="Create a strong password"
                className={`pr-10 ${formErrors.password ? 'border-error focus:ring-error' : ''}`}
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                disabled={loading}
              >
                <Icon 
                  name={showPassword ? 'EyeOff' : 'Eye'} 
                  size={16} 
                  color="var(--color-text-secondary)" 
                />
              </button>
            </div>
            {formErrors.password && (
              <p className="mt-1 text-sm text-error">{formErrors.password}</p>
            )}
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-text-primary mb-2">
              Confirm Password
            </label>
            <div className="relative">
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Confirm your password"
                className={`pr-10 ${formErrors.confirmPassword ? 'border-error focus:ring-error' : ''}`}
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                disabled={loading}
              >
                <Icon 
                  name={showConfirmPassword ? 'EyeOff' : 'Eye'} 
                  size={16} 
                  color="var(--color-text-secondary)" 
                />
              </button>
            </div>
            {formErrors.confirmPassword && (
              <p className="mt-1 text-sm text-error">{formErrors.confirmPassword}</p>
            )}
          </div>

          <div>
            <label className="flex items-start space-x-3">
              <Input
                type="checkbox"
                name="agreeToTerms"
                checked={formData.agreeToTerms}
                onChange={handleChange}
                className={`mt-1 ${formErrors.agreeToTerms ? 'border-error' : ''}`}
                disabled={loading}
              />
              <span className="text-sm text-text-secondary">
                I agree to the{' '}
                <a href="/terms" className="text-primary hover:text-primary/80 underline">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="/privacy" className="text-primary hover:text-primary/80 underline">
                  Privacy Policy
                </a>
              </span>
            </label>
            {formErrors.agreeToTerms && (
              <p className="mt-1 text-sm text-error">{formErrors.agreeToTerms}</p>
            )}
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={loading}
            disabled={loading}
          >
            {loading ? 'Creating account...' : 'Create account'}
          </Button>
        </form>

        <div className="mt-8 text-center">
          <p className="text-text-secondary">
            Already have an account?{' '}
            <button
              onClick={onSwitchToLogin}
              className="text-primary hover:text-primary/80 font-medium transition-colors"
              disabled={loading}
            >
              Sign in
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}

export default RegisterForm
