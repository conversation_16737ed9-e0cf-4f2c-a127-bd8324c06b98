# Task 1: Image Extension Testing Checklist

## Objective
Verify that the @tiptap/extension-image has been successfully added to the DocumentCanvasMinimal editor and basic image functionality is working.

## Implementation Summary
- ✅ Added `Image` import from `@tiptap/extension-image`
- ✅ Configured Image extension in editor with:
  - `inline: false` - Images are block-level elements
  - `allowBase64: true` - Support for base64 encoded images
  - Custom CSS classes for styling
- ✅ Added 'image' to supported node types in floating menu logic
- ✅ Added image-specific floating menu options (edit, delete)
- ✅ Added image insertion option to plus menu
- ✅ Added test function accessible via browser console

## Testing Checklist

### ✅ Basic Extension Loading
- [ ] Navigate to `/document-editor/test-image`
- [ ] Open browser DevTools console
- [ ] Verify no errors related to Image extension
- [ ] Check that `window.testImageInsertion` function is available

### ✅ Image Insertion via Console
- [ ] Run `window.testImageInsertion()` in browser console
- [ ] Verify image appears in editor
- [ ] Check image has proper styling (rounded corners, shadow, responsive)
- [ ] Verify image is selectable and focusable

### ✅ Image Insertion via Plus Menu
- [ ] Click in empty paragraph to show plus menu
- [ ] Click plus button to expand menu
- [ ] Verify "Image" option appears with 🖼️ icon
- [ ] Click "Image" option
- [ ] Verify sample image is inserted
- [ ] Verify menu closes after insertion

### ✅ Image Floating Menu
- [ ] Click on inserted image
- [ ] Verify floating menu appears with ellipsis button
- [ ] Click ellipsis button to expand menu
- [ ] Verify "Edit image" and "Delete image" options appear
- [ ] Test "Delete image" functionality
- [ ] Verify image is removed when delete is clicked

### ✅ Image Node Detection
- [ ] Insert image and click on it
- [ ] Check browser console for node type detection logs
- [ ] Verify logs show `nodeType: 'image'`
- [ ] Verify menu type is correctly determined as 'ellipsis'

### ✅ Image Styling and Responsiveness
- [ ] Insert image and verify CSS classes are applied:
  - `tiptap-image`
  - `max-w-full`
  - `h-auto`
  - `rounded-lg`
  - `shadow-sm`
- [ ] Resize browser window to test responsive behavior
- [ ] Verify image scales properly on mobile viewport

### ✅ Editor Integration
- [ ] Insert image, then type text before and after
- [ ] Verify cursor positioning works correctly around images
- [ ] Test undo/redo with image insertion and deletion
- [ ] Verify image persists in editor content

## Expected Results

### ✅ Success Criteria
- Image extension loads without errors
- Images can be inserted via console test function
- Images can be inserted via plus menu
- Images display with proper styling
- Floating menu appears for image nodes with appropriate options
- Image deletion works correctly
- No console errors or warnings related to images

### ❌ Failure Indicators
- Console errors mentioning Image extension
- Test function not available or throwing errors
- Images not appearing after insertion
- Floating menu not showing for image nodes
- Styling not applied correctly
- Browser crashes or performance issues

## Rollback Instructions

If any critical issues are found:

1. **Remove Image Extension**:
   ```javascript
   // Remove this import
   import Image from '@tiptap/extension-image';
   
   // Remove from extensions array
   Image.configure({...}),
   ```

2. **Revert Node Type Support**:
   ```javascript
   // Change back to:
   const supportedNodeTypes = ['paragraph', 'heading', 'listItem', 'blockquote', 'codeBlock'];
   ```

3. **Remove Image Menu Options**:
   - Remove `case 'image':` from floating menu logic
   - Remove image button from plus menu

4. **Remove Test Function**:
   - Remove the useEffect that adds `window.testImageInsertion`

## Next Steps

Upon successful completion of this testing checklist:
- Mark Task 1 as COMPLETE
- Begin Task 2: Create Image URL Input Component
- Document any issues or improvements needed for future tasks

## Notes

- Test function is temporary and will be removed in later tasks
- Image insertion currently uses hardcoded test URL
- Image editing functionality is placeholder (will be implemented in later tasks)
- Focus on basic functionality verification at this stage
