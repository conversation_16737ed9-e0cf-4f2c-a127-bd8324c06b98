/**
 * Tests for URL Extraction Service
 */

import { extractContentFromUrl, getContentPreview, analyzeContentForSuggestions } from '../urlExtractionService';

describe('URL Extraction Service', () => {
  describe('getContentPreview', () => {
    test('should return full content if shorter than maxLength', () => {
      const content = 'Short content';
      const preview = getContentPreview(content, 200);
      expect(preview).toBe('Short content');
    });

    test('should truncate content if longer than maxLength', () => {
      const content = 'This is a very long content that should be truncated when it exceeds the maximum length limit';
      const preview = getContentPreview(content, 50);
      expect(preview).toBe('This is a very long content that should be...');
    });

    test('should handle empty content', () => {
      const preview = getContentPreview('', 200);
      expect(preview).toBe('');
    });
  });

  describe('analyzeContentForSuggestions', () => {
    test('should suggest document type based on content length', () => {
      const shortContent = {
        extractedContent: 'Short content with less than 1000 words. '.repeat(20),
        originalTitle: 'Test Article'
      };
      
      const suggestions = analyzeContentForSuggestions(shortContent);
      expect(suggestions.suggestedDocumentType).toBe('guide');
      expect(suggestions.estimatedLength).toBe('short');
    });

    test('should extract topic from title', () => {
      const content = {
        extractedContent: 'Some content here',
        originalTitle: 'The Ultimate Guide to Machine Learning!'
      };
      
      const suggestions = analyzeContentForSuggestions(content);
      expect(suggestions.suggestedTopic).toBe('The Ultimate Guide to Machine Learning');
    });
  });

  describe('extractContentFromUrl', () => {
    test('should handle invalid URLs', async () => {
      const result = await extractContentFromUrl('not-a-url');
      expect(result.success).toBe(false);
      expect(result.data.extractionError).toContain('Invalid URL format');
    });

    test('should handle test URLs with mock data', async () => {
      const result = await extractContentFromUrl('https://example.com/test-article');
      expect(result.success).toBe(true);
      expect(result.data.originalTitle).toBeTruthy();
      expect(result.data.extractedContent).toBeTruthy();
      expect(result.data.wordCount).toBeGreaterThan(0);
    });
  });
});
