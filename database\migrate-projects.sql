-- DocForge AI - Simplified Projects Migration Script
-- Run this script in Supabase SQL Editor to set up the core projects system
-- This script is idempotent and can be run multiple times safely

-- Step 1: Create simplified projects schema
-- Projects table - Contains both metadata and content
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,

    -- Basic project information
    title TEXT NOT NULL,
    description TEXT,
    document_type TEXT NOT NULL CHECK (document_type IN ('ebook', 'academic', 'business', 'guide', 'report', 'whitepaper', 'manual')),
    category TEXT NOT NULL CHECK (category IN ('eBooks', 'Academic', 'Business')),

    -- Project status and progress
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'review', 'completed', 'archived')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),

    -- Content data (JSONB for flexibility - matches current localStorage structure)
    questionnaire_data JSONB NOT NULL, -- Original Document Creator form data
    generated_content JSONB,           -- AI-generated content structure

    -- Computed metadata (auto-calculated from content)
    word_count INTEGER DEFAULT 0,
    chapter_count INTEGER DEFAULT 0,

    -- Visual and formatting
    thumbnail_url TEXT,
    format TEXT DEFAULT 'pdf' CHECK (format IN ('pdf', 'epub', 'docx', 'html')),

    -- Project settings
    is_template BOOLEAN DEFAULT false,

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),

    -- Soft delete
    deleted_at TIMESTAMPTZ,

    -- Search and indexing
    search_vector tsvector GENERATED ALWAYS AS (
        setweight(to_tsvector('english', title), 'A') ||
        setweight(to_tsvector('english', COALESCE(description, '')), 'B')
    ) STORED
);

-- Project activities table - For audit trail and activity feed
CREATE TABLE IF NOT EXISTS public.project_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,

    -- Activity details
    activity_type TEXT NOT NULL CHECK (activity_type IN (
        'created', 'updated', 'deleted', 'duplicated',
        'published', 'archived', 'exported'
    )),
    activity_description TEXT,
    activity_data JSONB, -- Additional activity metadata

    -- Context
    ip_address INET,
    user_agent TEXT,

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON public.projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_document_type ON public.projects(document_type);
CREATE INDEX IF NOT EXISTS idx_projects_category ON public.projects(category);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON public.projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON public.projects(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_search_vector ON public.projects USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_projects_deleted_at ON public.projects(deleted_at) WHERE deleted_at IS NULL;

-- JSONB indexes for content queries
CREATE INDEX IF NOT EXISTS idx_projects_questionnaire_data ON public.projects USING GIN(questionnaire_data);
CREATE INDEX IF NOT EXISTS idx_projects_generated_content ON public.projects USING GIN(generated_content);

-- Activity indexes
CREATE INDEX IF NOT EXISTS idx_activities_project_id ON public.project_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_activities_user_id ON public.project_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON public.project_activities(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activities_type ON public.project_activities(activity_type);

-- Step 3: Set up Row Level Security
\i projects-rls-policies.sql

-- Step 4: Create triggers and functions
\i projects-triggers.sql

-- Step 4: Insert sample data for testing (optional)
-- Uncomment the following section for development/testing

/*
-- Sample projects for testing
INSERT INTO public.projects (
    user_id, title, description, document_type, category, status, progress,
    word_count, thumbnail_url, format
) VALUES 
(
    (SELECT id FROM public.user_profiles LIMIT 1),
    'Sample eBook Project',
    'A comprehensive guide to getting started with DocForge AI',
    'ebook',
    'eBooks',
    'completed',
    100,
    15000,
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
    'pdf'
),
(
    (SELECT id FROM public.user_profiles LIMIT 1),
    'Academic Research Paper',
    'Research on AI-assisted content creation',
    'academic',
    'Academic',
    'in_progress',
    75,
    8500,
    'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop',
    'pdf'
),
(
    (SELECT id FROM public.user_profiles LIMIT 1),
    'Business Strategy Guide',
    'Strategic planning for modern businesses',
    'business',
    'Business',
    'draft',
    25,
    3200,
    'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=400&h=300&fit=crop',
    'pdf'
);

-- Sample documents for the projects
INSERT INTO public.documents (
    project_id, document_data, generated_content, word_count
) 
SELECT 
    p.id,
    '{"documentPurpose": {"primaryType": "ebook", "title": "Sample Project"}, "topicAndNiche": {"mainTopic": "DocForge AI Guide"}}',
    '{"title": "Sample Project", "introduction": {"content": "# Introduction\n\nThis is a sample introduction.", "wordCount": 150}, "chapters": [{"id": "chapter-1", "number": 1, "title": "Getting Started", "content": "# Getting Started\n\nContent here...", "wordCount": 500}], "conclusion": {"content": "# Conclusion\n\nSample conclusion.", "wordCount": 100}}',
    750
FROM public.projects p
WHERE p.title = 'Sample eBook Project';
*/

-- Step 5: Grant necessary permissions
-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Grant permissions on tables
GRANT SELECT, INSERT, UPDATE, DELETE ON public.projects TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.documents TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.project_collaborators TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.project_shares TO authenticated;
GRANT SELECT, INSERT ON public.project_activities TO authenticated;

-- Grant permissions for anonymous users (for public shares)
GRANT SELECT ON public.projects TO anon;
GRANT SELECT ON public.documents TO anon;
GRANT SELECT ON public.project_shares TO anon;

-- Step 6: Create helpful views for common queries

-- View for project overview with latest document
CREATE OR REPLACE VIEW public.project_overview AS
SELECT 
    p.*,
    d.document_data,
    d.generated_content,
    d.version as document_version,
    d.updated_at as document_updated_at,
    up.full_name as owner_name,
    up.avatar_url as owner_avatar,
    (
        SELECT COUNT(*) 
        FROM public.project_collaborators pc 
        WHERE pc.project_id = p.id AND pc.status = 'accepted'
    ) as collaborator_count,
    (
        SELECT COUNT(*) 
        FROM public.project_shares ps 
        WHERE ps.project_id = p.id AND ps.is_active = true
    ) as active_shares_count
FROM public.projects p
LEFT JOIN public.documents d ON p.id = d.project_id AND d.is_current_version = true
LEFT JOIN public.user_profiles up ON p.user_id = up.id
WHERE p.deleted_at IS NULL;

-- View for user's accessible projects
CREATE OR REPLACE VIEW public.user_accessible_projects AS
SELECT DISTINCT
    p.*,
    CASE 
        WHEN p.user_id = auth.user_id() THEN 'owner'
        WHEN pc.role IS NOT NULL THEN pc.role
        WHEN p.is_public THEN 'viewer'
        ELSE NULL
    END as user_role,
    CASE 
        WHEN p.user_id = auth.user_id() THEN true
        WHEN pc.can_edit = true THEN true
        ELSE false
    END as can_edit,
    CASE 
        WHEN p.user_id = auth.user_id() THEN true
        WHEN pc.can_share = true THEN true
        ELSE false
    END as can_share
FROM public.projects p
LEFT JOIN public.project_collaborators pc ON p.id = pc.project_id 
    AND pc.user_id = auth.user_id() 
    AND pc.status = 'accepted'
WHERE 
    p.deleted_at IS NULL
    AND (
        p.user_id = auth.user_id() OR
        pc.user_id = auth.user_id() OR
        p.is_public = true
    );

-- Grant permissions on views
GRANT SELECT ON public.project_overview TO authenticated;
GRANT SELECT ON public.user_accessible_projects TO authenticated;

-- Step 7: Create helpful functions for common operations

-- Function to duplicate a project
CREATE OR REPLACE FUNCTION public.duplicate_project(
    source_project_id UUID,
    new_title TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_project_id UUID;
    source_project RECORD;
    source_document RECORD;
BEGIN
    -- Check if user has access to source project
    IF NOT public.user_has_project_access(source_project_id, 'viewer') THEN
        RAISE EXCEPTION 'Access denied to source project';
    END IF;
    
    -- Get source project data
    SELECT * INTO source_project 
    FROM public.projects 
    WHERE id = source_project_id AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Source project not found';
    END IF;
    
    -- Create new project
    INSERT INTO public.projects (
        user_id, title, description, document_type, category,
        format, thumbnail_url, is_template
    ) VALUES (
        auth.user_id(),
        COALESCE(new_title, source_project.title || ' (Copy)'),
        source_project.description,
        source_project.document_type,
        source_project.category,
        source_project.format,
        source_project.thumbnail_url,
        false
    ) RETURNING id INTO new_project_id;
    
    -- Copy document if exists
    SELECT * INTO source_document 
    FROM public.documents 
    WHERE project_id = source_project_id AND is_current_version = true;
    
    IF FOUND THEN
        INSERT INTO public.documents (
            project_id, document_data, generated_content, word_count
        ) VALUES (
            new_project_id,
            source_document.document_data,
            source_document.generated_content,
            source_document.word_count
        );
    END IF;
    
    -- Log activity
    INSERT INTO public.project_activities (
        project_id, user_id, activity_type, activity_description, activity_data
    ) VALUES (
        new_project_id,
        auth.user_id(),
        'duplicated',
        'Project duplicated from another project',
        jsonb_build_object('source_project_id', source_project_id)
    );
    
    RETURN new_project_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on function
GRANT EXECUTE ON FUNCTION public.duplicate_project TO authenticated;

-- Verification queries to check installation
-- Uncomment to run verification after migration

/*
-- Verify tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('projects', 'documents', 'project_collaborators', 'project_shares', 'project_activities');

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('projects', 'documents', 'project_collaborators', 'project_shares', 'project_activities');

-- Verify indexes exist
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('projects', 'documents', 'project_collaborators', 'project_shares', 'project_activities');
*/
