import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const DocumentToolbar = ({ 
  documentType, 
  onDocumentTypeChange, 
  language, 
  onLanguageChange, 
  tone, 
  onToneChange,
  onExport,
  onSave,
  onShare,
  isSaving 
}) => {
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);

  const documentTypes = [
    { id: 'ebook', name: 'eBook', icon: 'Book' },
    { id: 'academic', name: 'Academic Paper', icon: 'GraduationCap' },
    { id: 'business', name: 'Business Document', icon: 'Briefcase' }
  ];

  const languages = [
    { id: 'english', name: 'English', flag: '🇺🇸' },
    { id: 'yoruba', name: 'Yoruba', flag: '🇳🇬' },
    { id: 'french', name: 'French', flag: '🇫🇷' }
  ];

  const tones = [
    { id: 'academic', name: 'Academic', description: 'Formal, scholarly' },
    { id: 'conversational', name: 'Conversational', description: 'Friendly, approachable' },
    { id: 'professional', name: 'Professional', description: 'Business-appropriate' }
  ];

  const exportFormats = [
    { id: 'pdf', name: 'PDF Document', icon: 'FileText', description: 'Portable Document Format' },
    { id: 'docx', name: 'Word Document', icon: 'FileText', description: 'Microsoft Word format' },
    { id: 'epub', name: 'EPUB eBook', icon: 'Book', description: 'Electronic publication' },
    { id: 'mobi', name: 'MOBI eBook', icon: 'Book', description: 'Kindle format' },
    { id: 'flipbook', name: 'Interactive Flipbook', icon: 'BookOpen', description: 'Web-based flipbook' }
  ];

  const shareOptions = [
    { id: 'link', name: 'Share Link', icon: 'Link', description: 'Generate shareable link' },
    { id: 'email', name: 'Email', icon: 'Mail', description: 'Send via email' },
    { id: 'publish', name: 'Publish Online', icon: 'Globe', description: 'Make publicly available' }
  ];

  const handleExport = (format) => {
    onExport(format);
    setShowExportMenu(false);
  };

  const handleShare = (option) => {
    onShare(option);
    setShowShareMenu(false);
  };

  return (
    <div className="bg-surface border-b border-border p-4">
      <div className="flex items-center justify-between flex-wrap gap-4">
        {/* Left Section - Document Settings */}
        <div className="flex items-center space-x-4">
          {/* Document Type Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-text-primary">Type:</label>
            <select
              value={documentType}
              onChange={(e) => onDocumentTypeChange(e.target.value)}
              className="px-3 py-1.5 text-sm border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-surface"
            >
              {documentTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>

          {/* Language Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-text-primary">Language:</label>
            <select
              value={language}
              onChange={(e) => onLanguageChange(e.target.value)}
              className="px-3 py-1.5 text-sm border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-surface"
            >
              {languages.map((lang) => (
                <option key={lang.id} value={lang.id}>
                  {lang.flag} {lang.name}
                </option>
              ))}
            </select>
          </div>

          {/* Tone Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-text-primary">Tone:</label>
            <select
              value={tone}
              onChange={(e) => onToneChange(e.target.value)}
              className="px-3 py-1.5 text-sm border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-surface"
            >
              {tones.map((toneOption) => (
                <option key={toneOption.id} value={toneOption.id}>
                  {toneOption.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center space-x-2">
          {/* Save Button */}
          <Button
            variant="ghost"
            onClick={onSave}
            disabled={isSaving}
            iconName={isSaving ? "Loader2" : "Save"}
            className={`text-sm ${isSaving ? 'animate-spin' : ''}`}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>

          {/* Export Dropdown */}
          <div className="relative">
            <Button
              variant="outline"
              onClick={() => setShowExportMenu(!showExportMenu)}
              iconName="Download"
              iconPosition="left"
              className="text-sm"
            >
              Export
              <Icon name="ChevronDown" size={14} className="ml-1" />
            </Button>

            {showExportMenu && (
              <div className="absolute right-0 mt-2 w-64 bg-surface rounded-lg shadow-elevation-3 border border-border z-1100">
                <div className="py-2">
                  <div className="px-3 py-2 border-b border-border">
                    <p className="text-sm font-medium text-text-primary">Export Format</p>
                  </div>
                  {exportFormats.map((format) => (
                    <button
                      key={format.id}
                      onClick={() => handleExport(format)}
                      className="w-full text-left px-3 py-2 hover:bg-background transition-micro"
                    >
                      <div className="flex items-start space-x-3">
                        <Icon name={format.icon} size={16} className="mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-text-primary">{format.name}</p>
                          <p className="text-xs text-text-secondary">{format.description}</p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Share Dropdown */}
          <div className="relative">
            <Button
              variant="primary"
              onClick={() => setShowShareMenu(!showShareMenu)}
              iconName="Share2"
              iconPosition="left"
              className="text-sm"
            >
              Share
              <Icon name="ChevronDown" size={14} className="ml-1" />
            </Button>

            {showShareMenu && (
              <div className="absolute right-0 mt-2 w-64 bg-surface rounded-lg shadow-elevation-3 border border-border z-1100">
                <div className="py-2">
                  <div className="px-3 py-2 border-b border-border">
                    <p className="text-sm font-medium text-text-primary">Share Options</p>
                  </div>
                  {shareOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => handleShare(option)}
                      className="w-full text-left px-3 py-2 hover:bg-background transition-micro"
                    >
                      <div className="flex items-start space-x-3">
                        <Icon name={option.icon} size={16} className="mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-text-primary">{option.name}</p>
                          <p className="text-xs text-text-secondary">{option.description}</p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="mt-4 flex items-center justify-between text-sm text-text-secondary">
        <div className="flex items-center space-x-4">
          <span>Auto-save: On</span>
          <span>•</span>
          <span>Last saved: 2 minutes ago</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Icon name="Zap" size={14} color="var(--color-accent)" />
            <span className="text-accent font-medium">Credits: 200</span>
          </div>
          <span>•</span>
          <span>Words: 1,247</span>
        </div>
      </div>

      {/* Overlay for dropdowns */}
      {(showExportMenu || showShareMenu) && (
        <div
          className="fixed inset-0 z-1050"
          onClick={() => {
            setShowExportMenu(false);
            setShowShareMenu(false);
          }}
        />
      )}
    </div>
  );
};

export default DocumentToolbar;